import React, { useState } from 'react'
import './DataTableEnhanced.css'

export default function DataTableEnhanced({ data, loading }) {
  const [selectedItem, setSelectedItem] = useState(null)
  const [imageError, setImageError] = useState({})

  const formatPrice = (price) => {
    return `¥${parseFloat(price).toFixed(2)}`
  }

  const formatSales = (sales) => {
    if (sales >= 10000) {
      return `${(sales / 10000).toFixed(1)}万+`
    }
    if (sales >= 1000) {
      return `${(sales / 1000).toFixed(1)}k+`
    }
    return sales.toString()
  }

  const handleImageError = (itemId) => {
    setImageError(prev => ({ ...prev, [itemId]: true }))
  }

  const renderSkeleton = () => {
    return Array(5).fill(0).map((_, index) => (
      <div key={`skeleton-${index}`} className="table-row skeleton-row">
        <div className="table-cell">
          <div className="skeleton-image"></div>
        </div>
        <div className="table-cell">
          <div className="skeleton-text"></div>
          <div className="skeleton-text short"></div>
        </div>
        <div className="table-cell">
          <div className="skeleton-price"></div>
        </div>
        <div className="table-cell">
          <div className="skeleton-sales"></div>
        </div>
        <div className="table-cell">
          <div className="skeleton-shop"></div>
        </div>
      </div>
    ))
  }

  return (
    <>
      <div className="data-table-enhanced">
        {/* 表格头部 */}
        <div className="table-header">
          <div className="header-left">
            <div className="header-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="7" height="7" rx="2" stroke="currentColor" strokeWidth="2"/>
                <rect x="14" y="3" width="7" height="7" rx="2" stroke="currentColor" strokeWidth="2"/>
                <rect x="3" y="14" width="7" height="7" rx="2" stroke="currentColor" strokeWidth="2"/>
                <rect x="14" y="14" width="7" height="7" rx="2" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
            <h2 className="table-title">商品数据</h2>
            {data.length > 0 && (
              <span className="data-count">{data.length} 条</span>
            )}
          </div>
          {data.length > 0 && (
            <div className="header-actions">
              <button className="action-button">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M3 7h14M8 7V3m4 4V3m-4 4v10m4-10v10m5-10v10a2 2 0 01-2 2H5a2 2 0 01-2-2V7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                筛选
              </button>
            </div>
          )}
        </div>

        {/* 表格内容 */}
        <div className="table-container">
          {/* 表头 */}
          <div className="table-head">
            <div className="table-row header-row">
              <div className="table-cell">商品图片</div>
              <div className="table-cell">商品名称</div>
              <div className="table-cell">价格</div>
              <div className="table-cell">销量</div>
              <div className="table-cell">店铺</div>
            </div>
          </div>

          {/* 表体 */}
          <div className="table-body">
            {loading && data.length === 0 ? (
              renderSkeleton()
            ) : data.length > 0 ? (
              data.map((item) => (
                <div 
                  key={item.id} 
                  className="table-row data-row"
                  onClick={() => setSelectedItem(item)}
                >
                  <div className="table-cell">
                    <div className="product-image-wrapper">
                      {imageError[item.id] ? (
                        <div className="image-placeholder">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="4" stroke="currentColor" strokeWidth="2"/>
                            <path d="M9 15l3-3 3 3m-6-6l1.5-1.5M15 9l3-3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      ) : (
                        <img 
                          src={item.image_url || item.thumb_url} 
                          alt={item.goods_name}
                          onError={() => handleImageError(item.id)}
                          loading="lazy"
                        />
                      )}
                    </div>
                  </div>
                  <div className="table-cell">
                    <div className="product-name">{item.goods_name}</div>
                    {item.brand_name && (
                      <div className="product-brand">{item.brand_name}</div>
                    )}
                  </div>
                  <div className="table-cell">
                    <div className="product-price">{formatPrice(item.price)}</div>
                    {item.coupon_price && item.coupon_price < item.price && (
                      <div className="coupon-price">券后 {formatPrice(item.coupon_price)}</div>
                    )}
                  </div>
                  <div className="table-cell">
                    <div className="product-sales">
                      <span className="sales-number">{formatSales(item.sales)}</span>
                      <span className="sales-label">已售</span>
                    </div>
                  </div>
                  <div className="table-cell">
                    <div className="shop-info">
                      <div className="shop-name">{item.shop_name}</div>
                      {item.merchant_type_name && (
                        <div className="shop-type">{item.merchant_type_name}</div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-state">
                <div className="empty-icon">
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                    <circle cx="32" cy="32" r="30" stroke="currentColor" strokeWidth="2" strokeDasharray="4 4"/>
                    <rect x="20" y="20" width="24" height="24" rx="4" stroke="currentColor" strokeWidth="2"/>
                    <path d="M28 36l4-4 4 4m-8-8l2-2m6 2l4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <h3 className="empty-title">暂无数据</h3>
                <p className="empty-description">开始爬取后，商品数据将实时显示在这里</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 详情模态框 */}
      {selectedItem && (
        <div className="modal-overlay" onClick={() => setSelectedItem(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3 className="modal-title">商品详情</h3>
              <button className="modal-close" onClick={() => setSelectedItem(null)}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="detail-image-section">
                <img 
                  src={selectedItem.hd_url || selectedItem.image_url || selectedItem.thumb_url}
                  alt={selectedItem.goods_name}
                  className="detail-image"
                />
              </div>
              <div className="detail-info-section">
                <h4 className="detail-name">{selectedItem.goods_name}</h4>
                
                <div className="detail-price-section">
                  <div className="current-price">{formatPrice(selectedItem.price)}</div>
                  {selectedItem.market_price && selectedItem.market_price > selectedItem.price && (
                    <div className="original-price">¥{selectedItem.market_price}</div>
                  )}
                </div>

                <div className="detail-stats">
                  <div className="stat-item">
                    <span className="stat-label">销量</span>
                    <span className="stat-value">{formatSales(selectedItem.sales)}</span>
                  </div>
                  {selectedItem.comment_count && (
                    <div className="stat-item">
                      <span className="stat-label">评价</span>
                      <span className="stat-value">{selectedItem.comment_count}</span>
                    </div>
                  )}
                  {selectedItem.rating && (
                    <div className="stat-item">
                      <span className="stat-label">评分</span>
                      <span className="stat-value">{selectedItem.rating}</span>
                    </div>
                  )}
                </div>

                <div className="detail-info-grid">
                  <div className="info-item">
                    <span className="info-label">商品ID</span>
                    <span className="info-value">{selectedItem.goods_id || selectedItem.id}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">店铺名称</span>
                    <span className="info-value">{selectedItem.shop_name}</span>
                  </div>
                  {selectedItem.brand_name && (
                    <div className="info-item">
                      <span className="info-label">品牌</span>
                      <span className="info-value">{selectedItem.brand_name}</span>
                    </div>
                  )}
                  {selectedItem.category && (
                    <div className="info-item">
                      <span className="info-label">分类</span>
                      <span className="info-value">{selectedItem.category}</span>
                    </div>
                  )}
                </div>

                {selectedItem.tags && selectedItem.tags.length > 0 && (
                  <div className="detail-tags">
                    {selectedItem.tags.map((tag, index) => (
                      <span key={index} className="tag-item">{tag}</span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}