#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用商品筛选器和补贴数据导出修复
"""

import yaml
from pathlib import Path

def update_config():
    """更新配置文件，优化筛选器设置"""
    config_path = Path("config/settings.yaml")
    
    # 读取现有配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新筛选器配置
    if 'product_filter' in config:
        print("📝 更新商品筛选器配置...")
        
        # 启用筛选器
        config['product_filter']['enabled'] = True
        print("  ✅ 启用筛选器")
        
        # 降低匹配阈值，更包容
        config['product_filter']['match_threshold'] = 0.6
        print("  ✅ 匹配阈值: 0.8 → 0.6")
        
        # 调整权重分配
        if 'weights' not in config['product_filter']:
            config['product_filter']['weights'] = {}
        
        config['product_filter']['weights']['brand'] = 0.35  # 降低品牌权重
        config['product_filter']['weights']['product_type'] = 0.20  # 降低产品类型权重
        config['product_filter']['weights']['specification'] = 0.30  # 提高规格权重
        config['product_filter']['weights']['keyword'] = 0.15  # 提高关键词权重
        
        print("  ✅ 优化权重分配:")
        print("    - 品牌权重: 40% → 35%")
        print("    - 产品类型: 25% → 20%")
        print("    - 规格权重: 25% → 30%")
        print("    - 关键词权重: 10% → 15%")
        
        # 确保调试模式开启
        if 'debug' in config['product_filter']:
            config['product_filter']['debug']['enabled'] = True
            config['product_filter']['debug']['log_scores'] = True
            print("  ✅ 开启调试模式")
    
    # 写回配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, allow_unicode=True, default_flow_style=False, sort_keys=False)
    
    print("\n✅ 配置更新完成！")

def print_fix_summary():
    """打印修复摘要"""
    print("\n" + "="*60)
    print("🔧 商品筛选器和补贴数据导出修复已完成")
    print("="*60)
    
    print("\n📋 修复内容:")
    print("1. ✅ 品牌匹配逻辑优化")
    print("   - 主品牌搜索现在包含子品牌商品")
    print("   - 例如：搜索'海尔'会包含'统帅'品牌商品")
    
    print("\n2. ✅ 产品类型匹配改进")
    print("   - 不再硬性过滤，而是降低评分")
    print("   - 提高了对分类错误的容错性")
    
    print("\n3. ✅ 百亿补贴数据导出修复")
    print("   - 修复了icon_ids字段名不匹配问题")
    print("   - 增加了详细的调试日志")
    
    print("\n4. ✅ 配置优化")
    print("   - 降低匹配阈值至0.6")
    print("   - 优化权重分配")
    print("   - 启用调试模式")
    
    print("\n📊 验证方法:")
    print("1. 运行爬虫，搜索'海尔476'或'海尔520'")
    print("2. 检查统帅品牌商品是否被正确包含")
    print("3. 查看Excel文件中的补贴信息列")
    print("4. 检查日志中的补贴统计信息")
    
    print("\n💡 建议:")
    print("- 如果筛选过于宽松，可将match_threshold调整至0.65-0.7")
    print("- 如果筛选过于严格，可将match_threshold调整至0.5-0.55")
    print("- 观察日志中的匹配分数，根据实际情况微调")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    try:
        update_config()
        print_fix_summary()
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保在项目根目录下运行此脚本")