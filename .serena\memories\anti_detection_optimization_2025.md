# 反检测机制优化记录 (2025-01-27)

## 核心问题诊断
手动浏览器正常但程序被风控的原因：
1. **过度伪装反而暴露** - 试图创建"完美"环境反而异常
2. **浏览器指纹被检测** - 40个启动参数形成独特指纹
3. **行为模式机械化** - 固定滚动距离、规律延迟
4. **API重写被检测** - fetch/XHR重写的toString()暴露修改痕迹

## 优化方案实施

### 1. 移除过度伪装
- **删除所有API重写**：移除fetch、XMLHttpRequest、WebSocket、console重写
- **精简启动参数**：从40个减少到3个必要参数
  ```python
  args = [
      "--disable-blink-features=AutomationControlled",
      "--no-first-run", 
      "--no-default-browser-check"
  ]
  ```
- **最小化反检测脚本**：只保留必要的webdriver隐藏

### 2. 增强人类行为模拟
- **贝塞尔曲线鼠标移动**：自然的鼠标轨迹
- **不规则滚动**：快慢结合，偶尔回滚
- **阅读停顿**：3-15秒随机停顿
- **随机交互**：悬停、轻点击空白区域

### 3. 优化请求模式
- **泊松分布间隔**：更符合人类行为的随机间隔
- **爆发式浏览**：快速查看3-7个商品后长停顿10-30秒
- **强制休息**：每20-30个商品休息2-5分钟

### 4. 人性化风控恢复
- **基础冷却时间**：
  - 低级别：5-10分钟
  - 中级别：10-20分钟  
  - 高级别：20-40分钟
  - 严重级别：40-60分钟
- **渐进式恢复**：连续风控时间翻倍（最多3倍）

## 技术实现细节

### lightweight_fingerprint.py 修改
- 移除所有window.fetch、XMLHttpRequest等核心API重写
- 只保留基础的navigator.webdriver隐藏
- 简化为最小必要的反检测措施

### browser_manager.py 修改  
- 精简启动参数到3个
- 移除与playwright-stealth冲突的自定义脚本
- 使用最小化的补充反检测脚本

### anti_detection.py 增强
- 实现贝塞尔曲线鼠标移动算法
- 添加泊松分布延迟生成
- 增加爆发式浏览模式
- 恢复人性化冷却时间

## 测试结果
- **BrowserScan**: ✅ 通过检测
- **Bot.sannysoft**: ✅ 通过检测
- 显著降低了被检测为自动化工具的概率

## 关键经验
1. **最小化修改原则** - 让浏览器尽可能保持原生状态
2. **真实的不完美** - 模拟人类的随机性和不规律性
3. **合理的时间管理** - 避免过快操作和立即重试
4. **避免过度防御** - 过多的伪装反而暴露自动化特征