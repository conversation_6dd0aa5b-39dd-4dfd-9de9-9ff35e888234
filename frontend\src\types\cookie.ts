export interface Cookie {
  name: string;
  value: string;
  domain?: string;
  path?: string;
  expires?: Date | string;
  secure?: boolean;
  httpOnly?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

export interface CookieValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  parsedCookies: <PERSON><PERSON>[];
}

export type CookieInputFormat = 'string' | 'json' | 'browser' | 'header';

export interface CookieManagerState {
  cookies: Cookie[];
  rawInput: string;
  inputFormat: CookieInputFormat;
  validationResult: CookieValidationResult | null;
  isValidating: boolean;
  lastValidated: Date | null;
}