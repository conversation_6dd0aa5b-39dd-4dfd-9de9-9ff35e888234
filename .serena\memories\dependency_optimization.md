# 依赖优化记录

## 优化时间
2025-01-27

## 优化内容

### 1. 依赖精简
从原本的14个依赖减少到8个核心依赖：
- **删除未使用的库**：pandas, aiohttp, chardet
- **删除开发工具**：pytest, pytest-asyncio, black, isort, mypy, types-PyYAML

### 2. 保留的核心依赖
```
playwright>=1.51.0          # 浏览器自动化
playwright-stealth>=1.0.6   # 反检测
orjson>=3.9.0               # 高性能JSON解析
openpyxl>=3.1.2             # Excel导出
python-dateutil>=2.8.2      # 时间处理
pyyaml>=6.0.1               # 配置文件
loguru>=0.7.2               # 日志系统
tenacity>=8.2.3             # 重试机制
```

### 3. 优化效果
- 减少约50MB安装体积
- 加快部署速度
- 避免依赖冲突
- 保持所有核心功能正常