.progress-monitor-demo {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.demo-header {
  max-width: 1200px;
  margin: 0 auto 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.demo-controls {
  display: flex;
  gap: 16px;
}

.demo-mode-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-mode-switch:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.demo-mode-switch input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.demo-mode-switch span {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 控制面板 */
.control-panel {
  display: flex;
  gap: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.control-button {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-button.start {
  background: #10b981;
  color: white;
}

.control-button.start:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.control-button.pause {
  background: #f59e0b;
  color: white;
}

.control-button.pause:hover:not(:disabled) {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.control-button.resume {
  background: #3b82f6;
  color: white;
}

.control-button.resume:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.control-button.stop {
  background: #ef4444;
  color: white;
}

.control-button.stop:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* 统计信息 */
.demo-stats {
  padding: 24px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.demo-stats h3 {
  margin: 0 0 20px;
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  font-size: 12px;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .control-panel {
    flex-wrap: wrap;
  }

  .control-button {
    flex: 1 1 calc(50% - 6px);
    min-width: 140px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}