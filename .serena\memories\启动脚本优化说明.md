# 启动脚本优化说明

## 优化内容

### 1. 环境使用优化
- **原版本**：总是创建虚拟环境
- **优化版本**：
  - 优先使用当前Python环境
  - 只安装缺失的依赖包
  - 检测已安装的包，避免重复安装

### 2. 端口管理优化
- **自动处理端口占用**：
  - 检测端口是否被占用
  - 尝试清理占用端口的进程
  - 如果清理失败，自动查找备用端口
  - 动态更新前端API配置

- **端口配置**：
  - 后端默认端口：8000，备用：8001-8003, 8080, 8888
  - 前端默认端口：5173，备用：5174-5175, 3000-3002

### 3. 进程管理优化
- **启动前清理**：自动清理之前残留的进程
- **关闭时清理**：确保所有子进程都被正确关闭
- **进程监控**：使用psutil监控和管理进程

## 使用方法

### Windows用户
```bash
# 使用优化版启动脚本
start_optimized.bat

# 或直接运行Python脚本
python start_optimized.py
```

### 特性
1. **无需虚拟环境**：使用当前Python环境
2. **智能端口管理**：自动处理端口冲突
3. **进程清理**：自动清理残留进程
4. **依赖检查**：只安装缺失的包

### 端口被占用时的行为
1. 首先尝试清理占用端口的进程
2. 如果清理失败，使用备用端口
3. 自动更新前端的API连接配置
4. 在控制台显示实际使用的端口