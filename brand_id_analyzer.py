#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
品牌ID数据分析工具
用于分析收集到的品牌ID数据并生成映射建议

功能：
1. 分析已收集的API响应数据
2. 提取品牌ID和品牌名称关系
3. 生成品牌映射建议
4. 检测重复和冲突
5. 生成实现代码
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from collections import defaultdict, Counter
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.utils.helpers import save_json, ensure_dir

class BrandIDAnalyzer:
    """品牌ID数据分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.data_dir = Path("data")
        self.responses_dir = self.data_dir / "api_responses"
        self.analysis_dir = self.data_dir / "analysis"
        
        # 确保目录存在
        ensure_dir(str(self.analysis_dir))
        
        # 数据存储
        self.raw_data = {}  # 原始数据
        self.brand_data = defaultdict(list)  # 品牌ID数据
        self.existing_mappings = {}  # 现有映射
        
        # 加载现有的品牌ID映射
        self._load_existing_mappings()
        
        logger.info("品牌ID分析器初始化完成")
    
    def _load_existing_mappings(self) -> None:
        """加载现有的品牌ID映射"""
        try:
            # 从processor.py中提取现有映射
            processor_file = Path("src/data/processor.py")
            if processor_file.exists():
                with open(processor_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 简单解析现有映射（这里可以改进为更精确的解析）
                if '"3838": "统帅"' in content:
                    self.existing_mappings["3838"] = "统帅"
                
                logger.info(f"加载现有映射: {len(self.existing_mappings)} 个")
            
        except Exception as e:
            logger.warning(f"加载现有映射失败: {e}")
    
    def load_collected_data(self) -> bool:
        """加载收集到的数据"""
        logger.info("📂 加载收集到的数据...")
        
        # 定义文件映射
        response_files = [
            "refrigerator_responses.json",
            "air_conditioner_responses.json", 
            "water_heater_responses.json",
            "washing_machine_responses.json"
        ]
        
        total_loaded = 0
        
        for filename in response_files:
            filepath = self.responses_dir / filename
            
            if not filepath.exists():
                logger.warning(f"文件不存在: {filepath}")
                continue
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                keyword = data.get("keyword", filename.replace("_responses.json", ""))
                goods_data = data.get("goods_data", [])
                
                self.raw_data[keyword] = goods_data
                total_loaded += len(goods_data)
                
                logger.info(f"加载 {keyword}: {len(goods_data)} 个商品")
                
            except Exception as e:
                logger.error(f"加载文件失败 {filepath}: {e}")
        
        if total_loaded == 0:
            logger.error("没有加载到任何数据，请先运行 brand_id_collector.py")
            return False
        
        logger.info(f"✅ 总共加载 {total_loaded} 个商品数据")
        return True
    
    def extract_brand_data(self) -> None:
        """提取品牌数据"""
        logger.info("🔍 提取品牌ID数据...")
        
        for keyword, goods_list in self.raw_data.items():
            for goods in goods_list:
                brand_id = goods.get("brand_id")
                brand_name = goods.get("brand_name", "")
                goods_name = goods.get("goods_name", "")
                
                if brand_id:  # 只处理有brand_id的数据
                    brand_info = {
                        "brand_id": str(brand_id),
                        "brand_name": brand_name.strip() if brand_name else "",
                        "goods_name": goods_name,
                        "keyword": keyword,
                        "goods_id": goods.get("goods_id"),
                        "price": goods.get("price"),
                        "sales": goods.get("sales"),
                        "merchant_name": goods.get("merchant_name", "")
                    }
                    
                    self.brand_data[str(brand_id)].append(brand_info)
        
        logger.info(f"✅ 提取到 {len(self.brand_data)} 个唯一品牌ID")
    
    def analyze_brand_mappings(self) -> Dict[str, Any]:
        """分析品牌映射关系"""
        logger.info("📊 分析品牌映射关系...")
        
        analysis_results = {
            "brand_id_stats": {},
            "conflicts": [],
            "new_mappings": {},
            "existing_mappings": {},
            "quality_scores": {}
        }
        
        for brand_id, brand_list in self.brand_data.items():
            # 统计品牌名称
            brand_names = [item["brand_name"] for item in brand_list if item["brand_name"]]
            name_counter = Counter(brand_names)
            
            # 获取最常见的品牌名称
            most_common = name_counter.most_common(1)
            most_common_name = most_common[0][0] if most_common else ""
            most_common_count = most_common[0][1] if most_common else 0
            
            # 计算一致性得分
            total_with_name = len(brand_names)
            consistency_score = (most_common_count / total_with_name) if total_with_name > 0 else 0
            
            # 品牌ID统计
            brand_stats = {
                "brand_id": brand_id,
                "most_common_name": most_common_name,
                "consistency_score": consistency_score,
                "total_occurrences": len(brand_list),
                "occurrences_with_name": total_with_name,
                "all_names": list(set(brand_names)),
                "name_distribution": dict(name_counter),
                "keywords": list(set(item["keyword"] for item in brand_list)),
                "sample_goods": [item["goods_name"] for item in brand_list[:3]]
            }
            
            analysis_results["brand_id_stats"][brand_id] = brand_stats
            
            # 质量评分
            quality_score = self._calculate_quality_score(brand_stats)
            analysis_results["quality_scores"][brand_id] = quality_score
            
            # 检查是否是新映射
            if brand_id in self.existing_mappings:
                analysis_results["existing_mappings"][brand_id] = {
                    "existing_name": self.existing_mappings[brand_id],
                    "suggested_name": most_common_name,
                    "matches": self.existing_mappings[brand_id] == most_common_name
                }
            else:
                if most_common_name and quality_score >= 0.5:  # 质量阈值
                    analysis_results["new_mappings"][brand_id] = most_common_name
            
            # 检查冲突
            if len(set(brand_names)) > 1 and total_with_name > 1:
                conflict = {
                    "brand_id": brand_id,
                    "conflicting_names": list(set(brand_names)),
                    "name_distribution": dict(name_counter),
                    "recommended": most_common_name
                }
                analysis_results["conflicts"].append(conflict)
        
        return analysis_results
    
    def _calculate_quality_score(self, brand_stats: Dict[str, Any]) -> float:
        """计算品牌映射质量得分"""
        # 基础得分：一致性
        consistency = brand_stats["consistency_score"]
        
        # 出现频次加分
        occurrence_bonus = min(brand_stats["total_occurrences"] / 10, 0.3)
        
        # 有品牌名称的比例加分
        name_ratio = brand_stats["occurrences_with_name"] / brand_stats["total_occurrences"]
        name_bonus = name_ratio * 0.2
        
        # 关键词覆盖度加分
        keyword_coverage = len(brand_stats["keywords"]) / 4  # 总共4个关键词
        keyword_bonus = keyword_coverage * 0.1
        
        total_score = consistency + occurrence_bonus + name_bonus + keyword_bonus
        return min(total_score, 1.0)  # 最大1.0
    
    def generate_report(self, analysis_results: Dict[str, Any]) -> None:
        """生成分析报告"""
        logger.info("📄 生成分析报告...")
        
        # 完整报告
        report = {
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "total_brand_ids": len(self.brand_data),
                "new_mappings_found": len(analysis_results["new_mappings"]),
                "existing_mappings_checked": len(analysis_results["existing_mappings"]),
                "conflicts_detected": len(analysis_results["conflicts"]),
                "high_quality_mappings": len([bid for bid, score in analysis_results["quality_scores"].items() if score >= 0.8])
            },
            "analysis_results": analysis_results,
            "implementation": {
                "new_mappings_code": self._generate_mapping_code(analysis_results["new_mappings"]),
                "conflict_resolutions": self._generate_conflict_resolutions(analysis_results["conflicts"])
            }
        }
        
        # 保存完整报告
        report_file = self.analysis_dir / "brand_id_analysis_detailed.json"
        save_json(report, str(report_file))
        
        # 保存简化的新映射
        new_mappings_file = self.analysis_dir / "new_brand_mappings.json"
        save_json(analysis_results["new_mappings"], str(new_mappings_file))
        
        # 保存高质量映射
        high_quality = {
            bid: name for bid, name in analysis_results["new_mappings"].items()
            if analysis_results["quality_scores"].get(bid, 0) >= 0.8
        }
        high_quality_file = self.analysis_dir / "high_quality_mappings.json"
        save_json(high_quality, str(high_quality_file))
        
        logger.info(f"✅ 详细报告: {report_file}")
        logger.info(f"✅ 新映射: {new_mappings_file}")
        logger.info(f"✅ 高质量映射: {high_quality_file}")
        
        # 打印摘要
        self._print_summary(report)
    
    def _generate_mapping_code(self, new_mappings: Dict[str, str]) -> str:
        """生成映射代码"""
        if not new_mappings:
            return "# 没有发现新的高质量品牌ID映射"
        
        code_lines = [
            "# 建议添加到 src/data/processor.py 的 brand_id_mapping 字典中:",
            "self.brand_id_mapping = {",
            '    # 现有映射',
            '    "3838": "统帅",  # 统帅品牌ID',
            "",
            "    # 新发现的品牌ID映射:"
        ]
        
        # 按品牌名称排序
        sorted_mappings = sorted(new_mappings.items(), key=lambda x: x[1])
        
        for brand_id, brand_name in sorted_mappings:
            code_lines.append(f'    "{brand_id}": "{brand_name}",  # {brand_name}品牌ID')
        
        code_lines.append("}")
        
        return "\n".join(code_lines)
    
    def _generate_conflict_resolutions(self, conflicts: List[Dict]) -> List[Dict]:
        """生成冲突解决建议"""
        resolutions = []
        
        for conflict in conflicts:
            resolution = {
                "brand_id": conflict["brand_id"],
                "issue": f"品牌ID {conflict['brand_id']} 有多个不同的品牌名称",
                "conflicting_names": conflict["conflicting_names"],
                "recommendation": conflict["recommended"],
                "reason": f"推荐 '{conflict['recommended']}' 因为它出现频率最高"
            }
            resolutions.append(resolution)
        
        return resolutions
    
    def _print_summary(self, report: Dict[str, Any]) -> None:
        """打印分析摘要"""
        summary = report["summary"]
        new_mappings = report["analysis_results"]["new_mappings"]
        conflicts = report["analysis_results"]["conflicts"]
        
        print("\n" + "="*60)
        print("📊 品牌ID分析报告摘要")
        print("="*60)
        print(f"🏷️  总品牌ID数量: {summary['total_brand_ids']}")
        print(f"🆕 发现新映射: {summary['new_mappings_found']}")
        print(f"✅ 高质量映射: {summary['high_quality_mappings']}")
        print(f"⚠️  检测到冲突: {summary['conflicts_detected']}")
        print(f"🔍 已有映射检查: {summary['existing_mappings_checked']}")
        
        if new_mappings:
            print(f"\n🎯 推荐的新品牌ID映射 (前10个):")
            for i, (brand_id, brand_name) in enumerate(list(new_mappings.items())[:10]):
                print(f"  {brand_id} -> {brand_name}")
            
            if len(new_mappings) > 10:
                print(f"  ... 还有 {len(new_mappings) - 10} 个映射")
        
        if conflicts:
            print(f"\n⚠️  需要注意的冲突 (前5个):")
            for i, conflict in enumerate(conflicts[:5]):
                names = ", ".join(conflict["conflicting_names"])
                print(f"  品牌ID {conflict['brand_id']}: {names}")
                print(f"    推荐: {conflict['recommended']}")
        
        print("\n📁 输出文件:")
        print(f"  详细报告: data/analysis/brand_id_analysis_detailed.json")
        print(f"  新映射: data/analysis/new_brand_mappings.json")
        print(f"  高质量映射: data/analysis/high_quality_mappings.json")
        print("="*60)
    
    def run_analysis(self) -> bool:
        """运行完整分析"""
        try:
            # 1. 加载数据
            if not self.load_collected_data():
                return False
            
            # 2. 提取品牌数据
            self.extract_brand_data()
            
            # 3. 分析映射关系
            analysis_results = self.analyze_brand_mappings()
            
            # 4. 生成报告
            self.generate_report(analysis_results)
            
            return True
            
        except Exception as e:
            logger.error(f"分析失败: {e}")
            return False

def main():
    """主函数"""
    print("🔍 品牌ID数据分析工具")
    print("="*40)
    
    analyzer = BrandIDAnalyzer()
    
    success = analyzer.run_analysis()
    
    if success:
        print("\n✅ 分析完成!")
    else:
        print("\n❌ 分析失败!")
        print("请确保已运行 brand_id_collector.py 收集数据")

if __name__ == "__main__":
    main()
