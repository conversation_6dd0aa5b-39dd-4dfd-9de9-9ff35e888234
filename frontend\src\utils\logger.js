/**
 * 前端日志系统
 * 提供统一的日志记录功能，支持不同级别的日志、模块化日志、持久化存储等
 */

class Logger {
  constructor() {
    this.logs = []
    this.maxLogs = 1000 // 最多保存1000条日志
    this.modules = new Set()
    this.enabledModules = new Set()
    this.logLevel = this.loadLogLevel()
    this.isEnabled = this.loadEnabledState()
    
    // 日志级别
    this.levels = {
      DEBUG: { value: 0, color: '#6c757d', emoji: '🔍' },
      INFO: { value: 1, color: '#0dcaf0', emoji: 'ℹ️' },
      WARN: { value: 2, color: '#ffc107', emoji: '⚠️' },
      ERROR: { value: 3, color: '#dc3545', emoji: '❌' },
      SUCCESS: { value: 4, color: '#198754', emoji: '✅' }
    }
    
    // 初始化时加载历史日志
    this.loadLogs()
    
    // 监听存储事件，实现跨标签页同步
    window.addEventListener('storage', (e) => {
      if (e.key === 'logger_logs') {
        this.loadLogs()
      }
    })
  }
  
  // 加载日志级别设置
  loadLogLevel() {
    const saved = localStorage.getItem('logger_level')
    return saved || 'DEBUG'
  }
  
  // 加载启用状态
  loadEnabledState() {
    const saved = localStorage.getItem('logger_enabled')
    return saved !== 'false' // 默认启用
  }
  
  // 加载历史日志
  loadLogs() {
    try {
      const saved = localStorage.getItem('logger_logs')
      if (saved) {
        this.logs = JSON.parse(saved)
      }
    } catch (error) {
      console.error('Failed to load logs:', error)
      this.logs = []
    }
  }
  
  // 保存日志到本地存储
  saveLogs() {
    try {
      // 只保存最近的日志
      const logsToSave = this.logs.slice(-this.maxLogs)
      localStorage.setItem('logger_logs', JSON.stringify(logsToSave))
    } catch (error) {
      // 如果存储空间不足，删除旧日志
      if (error.name === 'QuotaExceededError') {
        this.logs = this.logs.slice(-Math.floor(this.maxLogs / 2))
        this.saveLogs()
      }
    }
  }
  
  // 设置日志级别
  setLevel(level) {
    if (this.levels[level]) {
      this.logLevel = level
      localStorage.setItem('logger_level', level)
    }
  }
  
  // 启用/禁用日志
  setEnabled(enabled) {
    this.isEnabled = enabled
    localStorage.setItem('logger_enabled', enabled.toString())
  }
  
  // 启用特定模块的日志
  enableModule(module) {
    this.modules.add(module)
    this.enabledModules.add(module)
    this.saveModuleSettings()
  }
  
  // 禁用特定模块的日志
  disableModule(module) {
    this.enabledModules.delete(module)
    this.saveModuleSettings()
  }
  
  // 保存模块设置
  saveModuleSettings() {
    localStorage.setItem('logger_modules', JSON.stringify([...this.modules]))
    localStorage.setItem('logger_enabled_modules', JSON.stringify([...this.enabledModules]))
  }
  
  // 加载模块设置
  loadModuleSettings() {
    try {
      const modules = localStorage.getItem('logger_modules')
      const enabledModules = localStorage.getItem('logger_enabled_modules')
      if (modules) {
        this.modules = new Set(JSON.parse(modules))
      }
      if (enabledModules) {
        this.enabledModules = new Set(JSON.parse(enabledModules))
      }
    } catch (error) {
      console.error('Failed to load module settings:', error)
    }
  }
  
  // 核心日志方法
  log(level, module, message, data = null) {
    if (!this.isEnabled) return
    
    // 检查日志级别
    if (this.levels[level].value < this.levels[this.logLevel].value) {
      return
    }
    
    // 检查模块是否启用
    if (module && this.modules.has(module) && !this.enabledModules.has(module)) {
      return
    }
    
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      module,
      message,
      data,
      id: `${timestamp}-${Math.random().toString(36).substr(2, 9)}`
    }
    
    // 添加到日志数组
    this.logs.push(logEntry)
    if (this.logs.length > this.maxLogs * 1.5) {
      this.logs = this.logs.slice(-this.maxLogs)
    }
    
    // 保存到本地存储
    this.saveLogs()
    
    // 在控制台输出
    this.consoleOutput(logEntry)
    
    // 触发日志事件
    window.dispatchEvent(new CustomEvent('logger:log', { detail: logEntry }))
    
    return logEntry
  }
  
  // 控制台输出
  consoleOutput(logEntry) {
    const { level, module, message, data, timestamp } = logEntry
    const levelInfo = this.levels[level]
    const time = new Date(timestamp).toLocaleTimeString()
    
    const prefix = `${levelInfo.emoji} [${time}] [${module || 'General'}]`
    const style = `color: ${levelInfo.color}; font-weight: bold;`
    
    if (data) {
      console.log(`%c${prefix} ${message}`, style, data)
    } else {
      console.log(`%c${prefix} ${message}`, style)
    }
  }
  
  // 便捷方法
  debug(module, message, data) {
    return this.log('DEBUG', module, message, data)
  }
  
  info(module, message, data) {
    return this.log('INFO', module, message, data)
  }
  
  warn(module, message, data) {
    return this.log('WARN', module, message, data)
  }
  
  error(module, message, data) {
    return this.log('ERROR', module, message, data)
  }
  
  success(module, message, data) {
    return this.log('SUCCESS', module, message, data)
  }
  
  // 获取所有日志
  getLogs(options = {}) {
    let logs = [...this.logs]
    
    // 按模块过滤
    if (options.module) {
      logs = logs.filter(log => log.module === options.module)
    }
    
    // 按级别过滤
    if (options.level) {
      const minLevel = this.levels[options.level].value
      logs = logs.filter(log => this.levels[log.level].value >= minLevel)
    }
    
    // 按时间范围过滤
    if (options.startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= new Date(options.startTime))
    }
    if (options.endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= new Date(options.endTime))
    }
    
    // 搜索关键词
    if (options.search) {
      const searchLower = options.search.toLowerCase()
      logs = logs.filter(log => 
        log.message.toLowerCase().includes(searchLower) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(searchLower))
      )
    }
    
    return logs
  }
  
  // 清空日志
  clear() {
    this.logs = []
    localStorage.removeItem('logger_logs')
    window.dispatchEvent(new CustomEvent('logger:clear'))
  }
  
  // 导出日志
  export(format = 'json') {
    const logs = this.getLogs()
    
    if (format === 'json') {
      return JSON.stringify(logs, null, 2)
    } else if (format === 'csv') {
      const headers = ['Timestamp', 'Level', 'Module', 'Message', 'Data']
      const rows = logs.map(log => [
        log.timestamp,
        log.level,
        log.module || '',
        log.message,
        log.data ? JSON.stringify(log.data) : ''
      ])
      
      const csv = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n')
      
      return csv
    } else if (format === 'txt') {
      return logs.map(log => {
        const time = new Date(log.timestamp).toLocaleString()
        const data = log.data ? ` | Data: ${JSON.stringify(log.data)}` : ''
        return `[${time}] [${log.level}] [${log.module || 'General'}] ${log.message}${data}`
      }).join('\n')
    }
    
    return logs
  }
  
  // 下载日志文件
  download(filename = 'logs', format = 'json') {
    const content = this.export(format)
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}_${new Date().toISOString().split('T')[0]}.${format}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  // 获取日志统计信息
  getStats() {
    const stats = {
      total: this.logs.length,
      byLevel: {},
      byModule: {},
      errors: [],
      warnings: []
    }
    
    // 初始化级别统计
    Object.keys(this.levels).forEach(level => {
      stats.byLevel[level] = 0
    })
    
    // 统计日志
    this.logs.forEach(log => {
      // 按级别统计
      stats.byLevel[log.level]++
      
      // 按模块统计
      const module = log.module || 'General'
      stats.byModule[module] = (stats.byModule[module] || 0) + 1
      
      // 收集错误和警告
      if (log.level === 'ERROR') {
        stats.errors.push(log)
      } else if (log.level === 'WARN') {
        stats.warnings.push(log)
      }
    })
    
    return stats
  }
}

// 创建全局日志实例
const logger = new Logger()

// 导出日志实例和便捷方法
export default logger

// 便捷方法导出
export const logDebug = (module, message, data) => logger.debug(module, message, data)
export const logInfo = (module, message, data) => logger.info(module, message, data)
export const logWarn = (module, message, data) => logger.warn(module, message, data)
export const logError = (module, message, data) => logger.error(module, message, data)
export const logSuccess = (module, message, data) => logger.success(module, message, data)