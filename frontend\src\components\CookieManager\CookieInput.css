.cookie-input {
  width: 100%;
}

.cookie-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cookie-input-textarea {
  font-family: '<PERSON>solas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.cookie-input-hint {
  display: block;
  margin-top: 8px;
  font-size: 12px;
}

.cookie-input .ant-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.cookie-input .ant-radio-button-wrapper {
  min-width: 100px;
  text-align: center;
}

@media (max-width: 768px) {
  .cookie-input-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .cookie-input .ant-radio-button-wrapper {
    min-width: 80px;
    font-size: 12px;
  }
}