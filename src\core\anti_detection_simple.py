"""
简化版反检测管理器 - 基于MediaCrawler实现
只使用简单的延迟策略，移除所有复杂的行为模拟
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Tuple, Any
from playwright.async_api import Page, Response
from loguru import logger

from src.utils.helpers import load_config


class SimpleAntiDetectionManager:
    """简化版反检测管理器 - 基于MediaCrawler的极简实现"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化简化版反检测管理器"""
        self.config = load_config(config_path)
        self.anti_detection_config = self.config.get("anti_detection", {})
        
        # 风控检测配置
        self.risk_indicators = self.anti_detection_config.get("risk_indicators", [])
        self.risk_status_codes = self.anti_detection_config.get("risk_status_codes", [429, 403, 503])
        
        # 简单的延迟配置（基于MediaCrawler）
        self.min_delay = 0  # 最小延迟0秒
        self.max_delay = 2  # 最大延迟2秒
        self.use_proxy = self.config.get("proxy", {}).get("enabled", False)
        
        # 风控状态跟踪（简化版）
        self.risk_detected = False
        self.last_risk_time = 0
        self.risk_count = 0
        self.rate_limited = False  # 429限流标记
        
        logger.info("简化版反检测管理器初始化完成（MediaCrawler模式）")

    async def add_simple_delay(self) -> None:
        """
        添加简单的随机延迟 - MediaCrawler风格
        有代理时延迟0-2秒，无代理时也是0-2秒
        """
        if self.use_proxy:
            # 有代理时的延迟（0-2秒）
            delay = random.uniform(self.min_delay, self.max_delay)
        else:
            # 无代理时的延迟（也是0-2秒）
            delay = random.uniform(self.min_delay, self.max_delay)
        
        if delay > 0:
            logger.debug(f"简单延迟: {delay:.1f}秒")
            await asyncio.sleep(delay)
    
    async def check_risk_control(self, page: Page, response: Optional[Response] = None) -> Tuple[bool, str]:
        """
        检查是否触发风控（简化版）
        
        Args:
            page: Playwright页面对象
            response: 可选的响应对象
            
        Returns:
            Tuple[bool, str]: (是否触发风控, 检测详情)
        """
        try:
            # 检查响应状态码
            if response and response.status in self.risk_status_codes:
                logger.warning(f"检测到风控状态码: {response.status}")
                return True, f"响应状态码 {response.status} 表示触发风控"
            
            # 检查页面标题中的风控关键词
            title = await page.title()
            risk_keywords = ["验证码", "人机验证", "安全验证", "访问频繁"]
            
            for keyword in risk_keywords:
                if keyword in title:
                    logger.warning(f"页面标题包含风控关键词: {keyword}")
                    return True, f"页面标题包含风控关键词: {keyword}"
            
            return False, "未检测到风控"
            
        except Exception as e:
            logger.error(f"检查风控状态时出错: {e}")
            return False, f"风控检查失败: {str(e)}"

    async def handle_risk_control(self, browser_manager, page: Page) -> bool:
        """
        处理风控情况（简化版）
        
        Args:
            browser_manager: 浏览器管理器实例
            page: 当前页面对象
            
        Returns:
            bool: 是否成功处理风控
        """
        try:
            logger.warning("检测到风控，执行简单处理...")
            
            # 标记风控状态
            self.risk_detected = True
            self.last_risk_time = time.time()
            self.risk_count += 1
            
            # 1. 简单等待（2-4秒）
            cooldown_time = random.uniform(2, 4)
            logger.info(f"等待冷却时间: {cooldown_time:.1f}秒")
            await asyncio.sleep(cooldown_time)
            
            # 2. 尝试刷新页面
            try:
                await page.reload(wait_until="networkidle")
                logger.info("页面刷新完成")
            except Exception as e:
                logger.warning(f"页面刷新失败: {e}")
            
            # 3. 如果在CDP模式下，可能需要重新创建页面
            if hasattr(browser_manager, 'use_cdp_mode') and browser_manager.use_cdp_mode:
                logger.info("CDP模式下建议重新创建页面")
                # 这里不强制重建，让调用者决定
            
            # 4. 重置风控状态
            self.risk_detected = False
            
            logger.info("简单风控处理完成")
            return True
            
        except Exception as e:
            logger.error(f"处理风控时出错: {e}")
            return False

    async def monitor_requests(self, page: Page) -> None:
        """
        监控页面请求，检测风控响应（简化版）
        
        Args:
            page: Playwright页面对象
        """
        async def handle_response(response: Response):
            """处理响应"""
            try:
                # 只检查API响应
                if "/proxy/api/search" in response.url or "/api/" in response.url:
                    if response.status in self.risk_status_codes:
                        logger.warning(f"API响应检测到风控状态码: {response.status}")
                        self.risk_detected = True
                        self.last_risk_time = time.time()
                        
                        # 429特殊处理 - 需要立即停止
                        if response.status == 429:
                            logger.error("🚨 检测到429限流，需要立即停止任务并保存数据！")
                            self.rate_limited = True
                        self.risk_count += 1

            except Exception as e:
                logger.debug(f"处理响应监控时出错: {e}")

        # 添加响应监听器
        page.on("response", handle_response)
        logger.info("已启动简单请求监控")
    
    def get_risk_status(self) -> Dict[str, Any]:
        """
        获取当前风控状态（简化版）
        
        Returns:
            Dict[str, Any]: 风控状态信息
        """
        current_time = time.time()
        
        return {
            "risk_detected": self.risk_detected,
            "last_risk_time": self.last_risk_time,
            "risk_count": self.risk_count,
            "time_since_last_risk": current_time - self.last_risk_time if self.last_risk_time > 0 else None
        }
    
    def should_continue_crawling(self) -> Tuple[bool, str]:
        """判断是否应该继续爬取（简化版）"""
        # 简单的判断逻辑
        if self.risk_count >= 10:
            return False, "风控次数过多，建议停止爬取"
        
        return True, "可以继续爬取"
    
    def reset_risk_status(self) -> None:
        """重置风控状态"""
        self.risk_detected = False
        self.risk_count = 0
        self.last_risk_time = 0
        logger.info("风控状态已重置")

    # 以下方法都是空实现，保持接口兼容性
    async def simulate_human_behavior(self, page: Page) -> None:
        """不再进行复杂的行为模拟"""
        pass
    
    async def simulate_minimal_behavior(self, page: Page) -> None:
        """不再进行行为模拟"""
        pass
    
    async def apply_browsing_pattern(self, page: Page, item_count: int) -> None:
        """不再使用复杂的浏览模式，只使用简单延迟"""
        await self.add_simple_delay()

    def get_poisson_delay(self, lambda_param: float = 1.5) -> float:
        """不再使用泊松分布，返回简单的随机延迟"""
        return random.uniform(self.min_delay, self.max_delay)