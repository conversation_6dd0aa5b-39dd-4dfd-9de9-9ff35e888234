.sort-selector-enhanced {
  background: var(--surface);
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.3s ease;
}

.sort-selector-enhanced:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 标题部分 */
.sort-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0.02) 100%);
  border-bottom: 1px solid var(--border);
}

.sort-header-icon {
  width: 40px;
  height: 40px;
  background: #2196F3;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.sort-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

/* 排序选项网格 */
.sort-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  padding: 20px;
}

.sort-option-card {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--secondary);
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.sort-option-card:hover:not(:disabled) {
  border-color: var(--border);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.sort-option-card.active {
  background: var(--surface);
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(238, 77, 45, 0.1);
}

.sort-option-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 选项图标 */
.option-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sort-option-card.blue .option-icon {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.sort-option-card.green .option-icon {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.sort-option-card.orange .option-icon {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.sort-option-card.purple .option-icon {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}

.sort-option-card.pink .option-icon {
  background: rgba(233, 30, 99, 0.1);
  color: #E91E63;
}

.sort-option-card.yellow .option-icon {
  background: rgba(255, 193, 7, 0.1);
  color: #FFC107;
}

.sort-option-card.active .option-icon {
  background: var(--primary);
  color: white;
}

/* 选项内容 */
.option-content {
  flex: 1;
}

.option-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 4px 0;
}

.option-description {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* 选中标记 */
.option-check {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: checkIn 0.2s ease;
}

@keyframes checkIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 信息提示 */
.sort-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 20px 20px;
  padding: 12px 16px;
  background: rgba(33, 150, 243, 0.05);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.info-icon {
  flex-shrink: 0;
  color: #2196F3;
}

.sort-info strong {
  color: var(--text);
  font-weight: 600;
}

/* 暗色主题适配 */
[data-theme="dark"] .sort-selector-enhanced {
  background: var(--surface);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .sort-header {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.05) 100%);
}

[data-theme="dark"] .sort-option-card {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .sort-option-card:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .sort-option-card.active {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .sort-info {
  background: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sort-options-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }
  
  .sort-header {
    padding: 16px 20px;
  }
  
  .sort-title {
    font-size: 16px;
  }
  
  .sort-header-icon {
    width: 36px;
    height: 36px;
  }
}