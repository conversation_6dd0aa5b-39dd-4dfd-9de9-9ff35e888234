/* 懒加载图片样式 */
.lazy-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity var(--duration-300) var(--ease-out);
}

.lazy-image-loading {
  opacity: 0;
}

.lazy-image-loaded {
  opacity: 1;
  animation: fadeIn var(--duration-300) var(--ease-out);
}

/* 加载占位符 */
.lazy-image-placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
}

.lazy-image-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top-color: var(--color-primary-500);
  border-radius: 50%;
  animation: spin var(--duration-700) linear infinite;
}

/* 错误状态 */
.lazy-image-error {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
  gap: var(--spacing-2);
}

.lazy-image-error svg {
  width: 48px;
  height: 48px;
  opacity: 0.3;
}

.lazy-image-error span {
  font-size: var(--font-size-sm);
}

/* 图片画廊 */
.lazy-image-gallery {
  display: grid;
  gap: var(--spacing-4);
}

.lazy-image-gallery-item {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-300) var(--ease-out);
}

.lazy-image-gallery-item:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

/* 响应式 */
@media (max-width: 768px) {
  .lazy-image-gallery {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  .lazy-image-gallery {
    grid-template-columns: 1fr !important;
  }
}