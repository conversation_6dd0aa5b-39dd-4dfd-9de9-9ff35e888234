# 拼多多爬虫控制面板故障排除指南

## 常见问题及解决方案

### 1. "localhost 拒绝了我们的连接请求"

**可能原因：**
- 服务未启动
- 端口被占用
- 防火墙阻止

**解决步骤：**

#### Windows用户：
1. 双击运行 `start_web_interface.bat`
2. 等待服务启动完成
3. 浏览器会自动打开 http://localhost:5173

#### Linux/WSL用户：
1. 运行 `./start_web_interface.sh`
2. 等待服务启动完成
3. 访问 http://localhost:5173

### 2. 端口被占用

**症状：**
- 启动脚本提示"端口已被占用"
- 服务无法启动

**解决方法：**

#### 方法1：停止占用端口的服务
```bash
# Windows
stop_web_interface.bat

# Linux/WSL
./stop_web_interface.sh
```

#### 方法2：手动查找并停止进程
```bash
# Windows - 查找占用8000端口的进程
netstat -ano | findstr :8000
# 停止进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# Linux/WSL
sudo lsof -i :8000
sudo kill -9 <PID>
```

### 3. 依赖安装失败

**后端依赖安装：**
```bash
pip install fastapi uvicorn websockets pydantic aiofiles loguru
```

**前端依赖安装：**
```bash
cd frontend
npm install
```

### 4. 服务启动但无法访问

**检查步骤：**

1. **验证后端服务：**
   - 访问：http://localhost:8000/api/health
   - 应返回：`{"status":"healthy",...}`

2. **验证前端服务：**
   - 访问：http://localhost:5173
   - 应显示爬虫控制面板

3. **检查防火墙：**
   - Windows防火墙可能阻止访问
   - 临时关闭防火墙测试
   - 或添加端口例外：8000, 5173

### 5. 手动启动服务

如果自动启动脚本失败，可以手动启动：

**启动后端：**
```bash
cd backend
python api_server.py
```

**启动前端：**
```bash
cd frontend
npm run dev
```

### 6. 查看日志

**后端日志位置：**
- 控制台输出
- `backend.log`（使用启动脚本时）

**前端日志位置：**
- 控制台输出
- `frontend.log`（使用启动脚本时）
- 浏览器开发者工具（F12）

### 7. 完全重置

如果问题持续，尝试完全重置：

```bash
# 1. 停止所有服务
stop_web_interface.bat  # Windows
./stop_web_interface.sh # Linux

# 2. 清理缓存
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force

# 3. 重新安装依赖
npm install

# 4. 重启服务
cd ..
start_web_interface.bat  # Windows
./start_web_interface.sh # Linux
```

## 快速检查清单

- [ ] Node.js 已安装（`node --version`）
- [ ] Python 已安装（`python --version`）
- [ ] 端口 8000 和 5173 未被占用
- [ ] 防火墙允许访问
- [ ] 所有依赖已正确安装
- [ ] 使用正确的启动脚本

## 获取帮助

如果问题仍未解决：
1. 收集错误日志
2. 记录具体错误信息
3. 检查系统环境配置
4. 提供详细的问题描述