# Serena 工具配置文件
# 拼多多爬虫项目专用配置

project:
  name: "拼多多商品爬虫系统"
  version: "1.0.0"
  description: "高效稳定的拼多多商品数据采集工具"
  status: "active"
  
# 核心模块配置
modules:
  browser_manager:
    enabled: true
    headless: false
    max_instances: 3
    
  anti_detection:
    enabled: true
    level: "high"
    strategies:
      - user_agent_rotation
      - fingerprint_masking
      - behavior_simulation
      
  data_collector:
    enabled: true
    auto_dedup: true
    thread_safe: true
    
  api_interceptor:
    enabled: true
    log_requests: true
    
# 性能优化配置
performance:
  browser_args_count: 40
  startup_time_target: 10
  memory_limit: 2048
  
# 数据处理配置  
data:
  export_formats:
    - excel
    - json
  auto_backup: true
  dedup_enabled: true
  
# 错误处理配置
error_handling:
  retry_count: 3
  recovery_enabled: true
  log_level: "INFO"
  
# 运行时配置
runtime:
  cookie_check: true
  login_detection: true
  sort_options:
    - default
    - sales
    - price_asc
    - price_desc
    
# 监控配置
monitoring:
  enabled: true
  metrics:
    - success_rate
    - response_time
    - data_quality
    - error_rate
    
# 自动化任务
automation:
  scheduled_tasks: []
  auto_restart: false
  health_check_interval: 300