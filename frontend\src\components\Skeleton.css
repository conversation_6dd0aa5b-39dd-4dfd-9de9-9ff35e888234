/* 骨架屏样式 */
.skeleton {
  background-color: var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.skeleton-animated::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: skeleton var(--duration-1000) infinite;
}

/* 深色模式调整 */
[data-theme="dark"] .skeleton {
  background-color: var(--color-gray-700);
}

[data-theme="dark"] .skeleton-animated::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.05),
    transparent
  );
}

/* 变体样式 */
.skeleton-text {
  border-radius: var(--radius-base);
  margin-bottom: var(--spacing-2);
}

.skeleton-rect {
  border-radius: var(--radius-lg);
}

.skeleton-circle {
  border-radius: var(--radius-full);
}

.skeleton-rounded {
  border-radius: var(--radius-lg);
}

/* 组合组件样式 */
.skeleton-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.skeleton-card-content {
  padding: var(--spacing-4);
}

.skeleton-card-content .skeleton:first-child {
  margin-bottom: var(--spacing-3);
}

.skeleton-table {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.skeleton-table-header {
  display: grid;
  grid-template-columns: repeat(var(--columns, 4), 1fr);
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.skeleton-table-body {
  padding: var(--spacing-3);
}

.skeleton-table-row {
  display: grid;
  grid-template-columns: repeat(var(--columns, 4), 1fr);
  gap: var(--spacing-3);
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--border-primary);
}

.skeleton-table-row:last-child {
  border-bottom: none;
}