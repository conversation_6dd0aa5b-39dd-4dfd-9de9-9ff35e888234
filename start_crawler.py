#!/usr/bin/env python3
"""
拼多多爬虫启动器 - Python版本
用于同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import signal
import platform
import json
import webbrowser
from pathlib import Path
from typing import Optional, Tuple, List
import socket
import atexit

# ANSI颜色代码（Windows 10+支持）
if platform.system() == 'Windows':
    os.system('color')  # 启用Windows终端的ANSI支持

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# 全局变量存储子进程
backend_process: Optional[subprocess.Popen] = None
frontend_process: Optional[subprocess.Popen] = None

def print_header():
    """打印启动器头部信息"""
    print(f"\n{Colors.HEADER}{'='*65}{Colors.ENDC}")
    print(f"{Colors.HEADER}║         拼多多爬虫Web界面启动器 (Python版)                    ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║              PDD Crawler Web Interface                        ║{Colors.ENDC}")
    print(f"{Colors.HEADER}{'='*65}{Colors.ENDC}\n")

def check_python_version() -> bool:
    """检查Python版本"""
    print(f"{Colors.OKBLUE}[环境检查] 检查Python版本...{Colors.ENDC}")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"{Colors.OKGREEN}✅ Python {version.major}.{version.minor}.{version.micro} 已安装{Colors.ENDC}")
        return True
    else:
        print(f"{Colors.FAIL}❌ Python版本过低，需要 >= 3.8，当前版本: {version.major}.{version.minor}{Colors.ENDC}")
        return False

def check_command(command: str) -> Tuple[bool, str]:
    """检查命令是否存在"""
    try:
        # 在Windows下，npm实际上是npm.cmd
        if platform.system() == 'Windows' and command == 'npm':
            # 尝试多种方式运行npm
            for npm_cmd in ['npm.cmd', 'npm']:
                try:
                    result = subprocess.run([npm_cmd, '--version'], capture_output=True, text=True)
                    if result.returncode == 0:
                        return True, f"v{result.stdout.strip()}"
                except:
                    continue
            
            # 如果都失败了，尝试使用cmd /c
            try:
                result = subprocess.run(['cmd', '/c', 'npm', '--version'], capture_output=True, text=True)
                if result.returncode == 0:
                    return True, f"v{result.stdout.strip()}"
            except:
                pass
        else:
            # 非Windows或非npm命令，正常处理
            if command == 'node':
                result = subprocess.run(['node', '--version'], capture_output=True, text=True)
                if result.returncode == 0:
                    return True, result.stdout.strip()
            elif command == 'npm':
                result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
                if result.returncode == 0:
                    return True, f"v{result.stdout.strip()}"
        
        # 如果上面的方法都失败了，尝试使用where/which
        if platform.system() == 'Windows':
            check_result = subprocess.run(['where', command], capture_output=True, text=True)
        else:
            check_result = subprocess.run(['which', command], capture_output=True, text=True)
        
        if check_result.returncode == 0:
            return True, "已安装（版本未知）"
        
        return False, ""
    except Exception as e:
        # 如果是FileNotFoundError，说明命令不存在
        if isinstance(e, FileNotFoundError):
            return False, ""
        return False, ""

def check_node_npm() -> bool:
    """检查Node.js和npm"""
    print(f"\n{Colors.OKBLUE}[环境检查] 检查Node.js和npm...{Colors.ENDC}")
    
    node_exists, node_version = check_command('node')
    if node_exists:
        print(f"{Colors.OKGREEN}✅ Node.js {node_version} 已安装{Colors.ENDC}")
    else:
        print(f"{Colors.FAIL}❌ Node.js未安装，请先安装Node.js{Colors.ENDC}")
        print(f"{Colors.WARNING}下载地址: https://nodejs.org/{Colors.ENDC}")
        return False
    
    npm_exists, npm_version = check_command('npm')
    if npm_exists:
        print(f"{Colors.OKGREEN}✅ npm {npm_version} 已安装{Colors.ENDC}")
    else:
        print(f"{Colors.FAIL}❌ npm未安装{Colors.ENDC}")
        return False
    
    return True

def is_port_in_use(port: int) -> bool:
    """检查端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return False
        except:
            return True

def check_python_dependencies() -> bool:
    """检查并安装Python依赖"""
    print(f"\n{Colors.OKBLUE}[依赖检查] 检查Python依赖...{Colors.ENDC}")
    
    # 检查requirements.txt中的依赖
    required_packages = []
    requirements_file = Path(__file__).parent / 'requirements.txt'
    if requirements_file.exists():
        with open(requirements_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    package = line.split('>=')[0].split('==')[0].strip()
                    required_packages.append(package)
    
    # API服务器需要的额外依赖
    api_packages = ['fastapi', 'uvicorn', 'websockets', 'pydantic', 'aiofiles', 'loguru']
    required_packages.extend(api_packages)
    
    # 检查已安装的包
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--format=json'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            installed_packages = {pkg['name'].lower() for pkg in json.loads(result.stdout)}
        else:
            installed_packages = set()
    except:
        installed_packages = set()
    
    # 检查缺失的包
    missing_packages = []
    for package in required_packages:
        if package.lower() not in installed_packages:
            missing_packages.append(package)
    
    if not missing_packages:
        print(f"{Colors.OKGREEN}✅ 所有Python依赖已安装{Colors.ENDC}")
        return True
    
    print(f"{Colors.WARNING}⚠️ 缺少以下Python依赖: {', '.join(missing_packages)}{Colors.ENDC}")
    print(f"{Colors.OKCYAN}正在安装依赖，请稍候...{Colors.ENDC}")
    
    # 安装requirements.txt
    if requirements_file.exists():
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)], 
                              capture_output=False)
        if result.returncode != 0:
            print(f"{Colors.FAIL}❌ 安装requirements.txt失败{Colors.ENDC}")
            return False
    
    # 安装API依赖
    result = subprocess.run([sys.executable, '-m', 'pip', 'install'] + api_packages, 
                          capture_output=False)
    if result.returncode == 0:
        print(f"{Colors.OKGREEN}✅ Python依赖安装完成{Colors.ENDC}")
        return True
    else:
        print(f"{Colors.WARNING}⚠️ 部分依赖安装可能有问题，但继续...{Colors.ENDC}")
        return True

def check_npm_dependencies() -> bool:
    """检查并安装npm依赖"""
    print(f"\n{Colors.OKBLUE}[依赖检查] 检查前端依赖...{Colors.ENDC}")
    
    frontend_dir = Path(__file__).parent / 'frontend'
    node_modules = frontend_dir / 'node_modules'
    
    if node_modules.exists():
        print(f"{Colors.OKGREEN}✅ 前端依赖已就绪{Colors.ENDC}")
        return True
    
    print(f"{Colors.OKCYAN}正在安装前端依赖，请稍候...{Colors.ENDC}")
    
    # 切换到frontend目录安装依赖
    original_cwd = os.getcwd()
    try:
        os.chdir(frontend_dir)
        result = subprocess.run(['npm', 'install'], capture_output=False)
        if result.returncode == 0:
            print(f"{Colors.OKGREEN}✅ 前端依赖安装完成{Colors.ENDC}")
            return True
        else:
            print(f"{Colors.FAIL}❌ 前端依赖安装失败{Colors.ENDC}")
            return False
    finally:
        os.chdir(original_cwd)

def start_backend() -> bool:
    """启动后端API服务器"""
    global backend_process
    
    print(f"\n{Colors.OKBLUE}[服务启动] 启动后端API服务器...{Colors.ENDC}")
    
    # 检查端口是否被占用
    if is_port_in_use(8000):
        print(f"{Colors.WARNING}⚠️ 端口8000已被占用，后端服务可能已在运行{Colors.ENDC}")
        # 尝试访问健康检查端点
        try:
            import urllib.request
            response = urllib.request.urlopen('http://localhost:8000/api/health', timeout=2)
            if response.status == 200:
                print(f"{Colors.OKGREEN}✅ 后端服务已在运行{Colors.ENDC}")
                return True
        except:
            pass
        print(f"{Colors.FAIL}端口被占用但服务无响应，请检查{Colors.ENDC}")
        return False
    
    # 启动后端服务
    backend_dir = Path(__file__).parent / 'backend'
    api_server = backend_dir / 'api_server.py'
    
    if not api_server.exists():
        print(f"{Colors.FAIL}❌ 找不到api_server.py文件{Colors.ENDC}")
        return False
    
    try:
        # Windows下使用新的控制台窗口
        project_root = Path(__file__).parent
        if platform.system() == 'Windows':
            backend_process = subprocess.Popen(
                [sys.executable, str(api_server)],
                cwd=str(project_root),  # 在项目根目录运行，而不是backend目录
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
        else:
            backend_process = subprocess.Popen(
                [sys.executable, str(api_server)],
                cwd=str(project_root)  # 在项目根目录运行，而不是backend目录
            )
        
        # 等待服务启动
        time.sleep(3)
        
        # 验证服务是否启动成功
        try:
            import urllib.request
            response = urllib.request.urlopen('http://localhost:8000/api/health', timeout=5)
            if response.status == 200:
                print(f"{Colors.OKGREEN}✅ 后端服务启动成功!{Colors.ENDC}")
                return True
        except:
            pass
        
        print(f"{Colors.WARNING}⚠️ 后端服务可能启动较慢，请稍后手动检查{Colors.ENDC}")
        return True
        
    except Exception as e:
        print(f"{Colors.FAIL}❌ 启动后端服务失败: {e}{Colors.ENDC}")
        return False

def start_frontend() -> bool:
    """启动前端开发服务器"""
    global frontend_process
    
    print(f"\n{Colors.OKBLUE}[服务启动] 启动前端开发服务器...{Colors.ENDC}")
    
    # 检查端口是否被占用
    if is_port_in_use(5173):
        print(f"{Colors.WARNING}⚠️ 端口5173已被占用，前端服务可能已在运行{Colors.ENDC}")
        return True
    
    # 启动前端服务
    frontend_dir = Path(__file__).parent / 'frontend'
    
    try:
        # Windows下使用新的控制台窗口
        if platform.system() == 'Windows':
            frontend_process = subprocess.Popen(
                ['npm', 'run', 'dev'],
                cwd=str(frontend_dir),
                creationflags=subprocess.CREATE_NEW_CONSOLE,
                shell=True
            )
        else:
            frontend_process = subprocess.Popen(
                ['npm', 'run', 'dev'],
                cwd=str(frontend_dir)
            )
        
        print(f"{Colors.OKGREEN}✅ 前端服务启动命令已执行{Colors.ENDC}")
        return True
        
    except Exception as e:
        print(f"{Colors.FAIL}❌ 启动前端服务失败: {e}{Colors.ENDC}")
        return False

def cleanup():
    """清理函数，终止子进程"""
    global backend_process, frontend_process
    
    print(f"\n{Colors.WARNING}正在关闭服务...{Colors.ENDC}")
    
    if backend_process:
        try:
            backend_process.terminate()
            backend_process.wait(timeout=5)
        except:
            try:
                backend_process.kill()
            except:
                pass
    
    if frontend_process:
        try:
            frontend_process.terminate()
            frontend_process.wait(timeout=5)
        except:
            try:
                frontend_process.kill()
            except:
                pass
    
    print(f"{Colors.OKGREEN}服务已关闭{Colors.ENDC}")

def signal_handler(signum, frame):
    """信号处理函数"""
    cleanup()
    sys.exit(0)

def main():
    """主函数"""
    print_header()
    
    # 注册清理函数
    atexit.register(cleanup)
    signal.signal(signal.SIGINT, signal_handler)
    if platform.system() == 'Windows':
        signal.signal(signal.SIGBREAK, signal_handler)
    
    # 环境检查
    if not check_python_version():
        print(f"\n{Colors.FAIL}环境检查失败，请安装Python >= 3.8{Colors.ENDC}")
        input("\n按回车键退出...")
        return 1
    
    if not check_node_npm():
        print(f"\n{Colors.FAIL}环境检查失败，请安装Node.js{Colors.ENDC}")
        input("\n按回车键退出...")
        return 1
    
    # 依赖检查和安装
    if not check_python_dependencies():
        print(f"\n{Colors.FAIL}Python依赖安装失败{Colors.ENDC}")
        input("\n按回车键退出...")
        return 1
    
    if not check_npm_dependencies():
        print(f"\n{Colors.FAIL}前端依赖安装失败{Colors.ENDC}")
        input("\n按回车键退出...")
        return 1
    
    # 启动服务
    if not start_backend():
        print(f"\n{Colors.FAIL}后端服务启动失败{Colors.ENDC}")
        input("\n按回车键退出...")
        return 1
    
    if not start_frontend():
        print(f"\n{Colors.FAIL}前端服务启动失败{Colors.ENDC}")
        cleanup()
        input("\n按回车键退出...")
        return 1
    
    # 打印成功信息
    print(f"\n{Colors.HEADER}{'='*65}{Colors.ENDC}")
    print(f"{Colors.HEADER}║                    🎉 启动完成！                              ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║                                                               ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║  前端地址: {Colors.OKCYAN}http://localhost:5173{Colors.HEADER}                              ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║  后端API:  {Colors.OKCYAN}http://localhost:8000/api{Colors.HEADER}                         ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║                                                               ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║  提示:                                                        ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║  1. 如果页面无法访问，请等待几秒后刷新                       ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║  2. 首次启动可能需要较长时间                                  ║{Colors.ENDC}")
    print(f"{Colors.HEADER}║  3. 按 Ctrl+C 停止所有服务                                    ║{Colors.ENDC}")
    print(f"{Colors.HEADER}{'='*65}{Colors.ENDC}\n")
    
    # 等待前端完全启动
    print(f"{Colors.OKCYAN}等待前端服务启动...{Colors.ENDC}")
    time.sleep(5)
    
    # 打开浏览器
    print(f"{Colors.OKCYAN}正在打开浏览器...{Colors.ENDC}")
    webbrowser.open('http://localhost:5173')
    
    print(f"\n{Colors.OKGREEN}服务正在运行中，按 Ctrl+C 停止所有服务{Colors.ENDC}\n")
    
    # 保持主进程运行
    try:
        while True:
            time.sleep(1)
            # 检查子进程状态
            if backend_process and backend_process.poll() is not None:
                print(f"{Colors.WARNING}⚠️ 后端服务已停止{Colors.ENDC}")
                break
            if frontend_process and frontend_process.poll() is not None:
                print(f"{Colors.WARNING}⚠️ 前端服务已停止{Colors.ENDC}")
                break
    except KeyboardInterrupt:
        pass
    
    cleanup()
    return 0

if __name__ == "__main__":
    sys.exit(main())