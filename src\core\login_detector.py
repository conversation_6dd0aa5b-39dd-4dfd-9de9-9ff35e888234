"""
登录状态检测器模块
负责检测用户登录状态，验证Cookie有效性
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from loguru import logger

from src.utils.helpers import load_config


class LoginDetector:
    """登录状态检测器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化登录检测器"""
        self.config = load_config(config_path)
        self.login_config = self.config.get("login_detection", {})
        
        # 登录状态指示器
        self.login_indicators = self.login_config.get("login_indicators", [])
        self.logout_indicators = self.login_config.get("logout_indicators", [])
        
        # 检测间隔
        self.check_interval = self.login_config.get("check_interval", 5)
        
        logger.info("登录状态检测器初始化完成")
    
    async def check_login_status(self, page: Page) -> <PERSON><PERSON>[bool, str]:
        """
        检查登录状态
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Tuple[bool, str]: (是否已登录, 检测详情)
        """
        try:
            logger.info("开始检测登录状态...")

            # 等待页面加载完成
            await page.wait_for_load_state("networkidle", timeout=10000)

            # 优先通过Cookie检查登录状态（如果有有效cookie，直接认为已登录）
            cookie_status = await self._check_cookie_validity(page)
            if cookie_status[0] is True:  # 有有效cookie，直接返回已登录
                logger.info("检测到有效Cookie，跳过页面元素检查")
                return cookie_status

            # 检查登录指示器
            login_detected = await self._check_login_indicators(page)
            if login_detected[0]:
                return login_detected

            # 检查未登录指示器
            logout_detected = await self._check_logout_indicators(page)
            if logout_detected[0] is False:  # 明确检测到未登录
                return logout_detected

            # 如果cookie检查返回了其他状态，使用该状态
            if cookie_status[0] is not None:
                return cookie_status

            # 通过页面内容检查
            content_status = await self._check_page_content(page)

            return content_status
            
        except Exception as e:
            logger.error(f"检测登录状态时出错: {e}")
            return False, f"检测失败: {str(e)}"
    
    async def _check_login_indicators(self, page: Page) -> Tuple[Optional[bool], str]:
        """检查登录指示器"""
        for indicator in self.login_indicators:
            try:
                # 检查元素是否存在且可见
                element = await page.query_selector(indicator)
                if element:
                    is_visible = await element.is_visible()
                    if is_visible:
                        logger.info(f"检测到登录指示器: {indicator}")
                        return True, f"检测到登录指示器: {indicator}"
            except Exception as e:
                logger.debug(f"检查登录指示器 {indicator} 时出错: {e}")
                continue
        
        return None, "未检测到登录指示器"
    
    async def _check_logout_indicators(self, page: Page) -> Tuple[Optional[bool], str]:
        """检查未登录指示器"""
        for indicator in self.logout_indicators:
            try:
                # 检查元素是否存在且可见
                element = await page.query_selector(indicator)
                if element:
                    is_visible = await element.is_visible()
                    if is_visible:
                        logger.warning(f"检测到未登录指示器: {indicator}")
                        return False, f"检测到未登录指示器: {indicator}"
            except Exception as e:
                logger.debug(f"检查未登录指示器 {indicator} 时出错: {e}")
                continue
        
        return None, "未检测到未登录指示器"
    
    async def _check_cookie_validity(self, page: Page) -> Tuple[Optional[bool], str]:
        """检查Cookie有效性"""
        try:
            # 获取当前页面的Cookie
            cookies = await page.context.cookies()
            
            # 检查关键Cookie是否存在
            required_cookies = [
                "pdd_user_id", "pdd_user_uin", "PDDAccessToken", "pdd_vds"
            ]
            
            found_cookies = []
            for cookie in cookies:
                if cookie["name"] in required_cookies:
                    found_cookies.append(cookie["name"])
            
            if len(found_cookies) >= 2:  # 至少需要2个关键Cookie
                logger.info(f"检测到有效Cookie: {found_cookies}")
                return True, f"检测到有效Cookie: {found_cookies}"
            elif found_cookies:
                logger.warning(f"Cookie不完整: {found_cookies}")
                return None, f"Cookie不完整: {found_cookies}"
            else:
                logger.warning("未检测到关键Cookie")
                return False, "未检测到关键Cookie"
                
        except Exception as e:
            logger.error(f"检查Cookie有效性时出错: {e}")
            return None, f"Cookie检查失败: {str(e)}"
    
    async def _check_page_content(self, page: Page) -> Tuple[bool, str]:
        """通过页面内容检查登录状态"""
        try:
            # 检查页面标题
            title = await page.title()
            if "登录" in title or "login" in title.lower():
                return False, f"页面标题显示未登录: {title}"
            
            # 检查页面URL
            url = page.url
            if "/login" in url or "/signin" in url:
                return False, f"当前在登录页面: {url}"
            
            # 检查页面文本内容
            try:
                # 检查是否有用户相关信息
                user_info_selectors = [
                    "[data-testid*='user']",
                    ".user",
                    ".avatar",
                    "[class*='user']",
                    "[class*='avatar']"
                ]
                
                for selector in user_info_selectors:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        logger.info(f"检测到用户信息元素: {selector}")
                        return True, f"检测到用户信息元素: {selector}"
                
                # 检查是否有登录按钮（表示未登录）
                login_buttons = await page.query_selector_all("text=/登录|login/i")
                if login_buttons:
                    visible_buttons = []
                    for button in login_buttons:
                        if await button.is_visible():
                            visible_buttons.append(button)
                    
                    if visible_buttons:
                        return False, f"检测到 {len(visible_buttons)} 个可见的登录按钮"
                
            except Exception as e:
                logger.debug(f"检查页面内容时出错: {e}")
            
            # 默认假设已登录（保守策略）
            return True, "基于页面内容推断为已登录状态"
            
        except Exception as e:
            logger.error(f"检查页面内容时出错: {e}")
            return False, f"页面内容检查失败: {str(e)}"
    
    async def wait_for_login(self, page: Page, timeout: int = 60) -> bool:
        """
        等待用户登录
        
        Args:
            page: Playwright页面对象
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功登录
        """
        logger.info(f"等待用户登录，超时时间: {timeout}秒")
        
        start_time = asyncio.get_event_loop().time()
        
        while True:
            # 检查是否超时
            if asyncio.get_event_loop().time() - start_time > timeout:
                logger.error("等待登录超时")
                return False
            
            # 检查登录状态
            is_logged_in, details = await self.check_login_status(page)
            if is_logged_in:
                logger.info(f"检测到用户已登录: {details}")
                return True
            
            # 等待一段时间后再次检查
            await asyncio.sleep(self.check_interval)
    
    async def ensure_logged_in(self, page: Page) -> bool:
        """
        确保用户已登录，如果未登录则抛出异常
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 是否已登录
            
        Raises:
            Exception: 如果用户未登录
        """
        is_logged_in, details = await self.check_login_status(page)
        
        if not is_logged_in:
            error_msg = f"用户未登录，请先登录后再运行爬虫。详情: {details}"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        logger.info(f"用户登录状态验证通过: {details}")
        return True
