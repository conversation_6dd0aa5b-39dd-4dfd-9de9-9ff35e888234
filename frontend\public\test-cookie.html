<!DOCTYPE html>
<html>
<head>
    <title>Cookie Status Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .valid { background: #d4edda; color: #155724; }
        .invalid { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Cookie Status Test</h1>
    <button onclick="checkStatus()">Check Cookie Status</button>
    <div id="result"></div>
    
    <script>
        async function checkStatus() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Checking...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/api/cookie/status');
                const data = await response.json();
                
                let html = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.valid) {
                    html += '<div class="status valid">✓ <PERSON>ie is VALID</div>';
                    if (data.expiresAt) {
                        html += '<p>Expires at: ' + new Date(data.expiresAt).toLocaleString() + '</p>';
                    }
                } else {
                    html += '<div class="status invalid">✗ Cookie is NOT valid</div>';
                }
                
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = '<div class="status invalid">Error: ' + error.message + '</div>';
            }
        }
        
        // Check on load
        checkStatus();
    </script>
</body>
</html>