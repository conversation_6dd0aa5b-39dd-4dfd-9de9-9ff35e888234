import React, { useCallback, useState } from 'react';
import { Input, Radio, Space, Typography, Button, message } from 'antd';
import { CopyOutlined, FormatPainterOutlined } from '@ant-design/icons';
import { CookieInputFormat } from '../../types/cookie';
import './CookieInput.css';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface CookieInputProps {
  value: string;
  format: CookieInputFormat;
  onChange: (value: string, format: CookieInputFormat) => void;
}

const formatPlaceholders: Record<CookieInputFormat, string> = {
  string: 'name1=value1; name2=value2; ...',
  json: '[{"name": "cookie1", "value": "value1", "domain": ".example.com"}, ...]',
  browser: '从浏览器开发者工具复制的Cookie字符串',
  header: 'Cookie: name1=value1; name2=value2; ...'
};

const formatExamples: Record<CookieInputFormat, string> = {
  string: 'sessionid=abc123; userid=12345; token=xyz789',
  json: `[
  {
    "name": "sessionid",
    "value": "abc123",
    "domain": ".pinduoduo.com",
    "path": "/",
    "expires": "2024-12-31T23:59:59.000Z",
    "secure": true,
    "httpOnly": true
  }
]`,
  browser: 'sessionid=abc123; userid=12345; token=xyz789; path=/; domain=.pinduoduo.com',
  header: 'Cookie: sessionid=abc123; userid=12345; token=xyz789'
};

const CookieInput: React.FC<CookieInputProps> = ({ value, format, onChange }) => {
  const [showExample, setShowExample] = useState(false);

  const handleFormatChange = useCallback((newFormat: CookieInputFormat) => {
    onChange(value, newFormat);
  }, [value, onChange]);

  const handleValueChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value, format);
  }, [format, onChange]);

  const handlePaste = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText();
      onChange(text, format);
      message.success('已粘贴剪贴板内容');
    } catch (error) {
      message.error('无法访问剪贴板');
    }
  }, [format, onChange]);

  const handleFormat = useCallback(() => {
    if (!value.trim()) {
      message.warning('请先输入Cookie内容');
      return;
    }

    try {
      let formatted = value;
      
      // 根据格式类型进行格式化
      if (format === 'json') {
        const parsed = JSON.parse(value);
        formatted = JSON.stringify(parsed, null, 2);
      } else if (format === 'string' || format === 'browser') {
        // 将Cookie字符串格式化为多行显示
        formatted = value
          .split(';')
          .map(cookie => cookie.trim())
          .filter(cookie => cookie)
          .join(';\n');
      } else if (format === 'header') {
        // 确保header格式正确
        if (!value.startsWith('Cookie:')) {
          formatted = 'Cookie: ' + value;
        }
      }

      onChange(formatted, format);
      message.success('格式化成功');
    } catch (error) {
      message.error('格式化失败，请检查输入格式');
    }
  }, [value, format, onChange]);

  const handleShowExample = useCallback(() => {
    setShowExample(!showExample);
    if (!showExample) {
      onChange(formatExamples[format], format);
    }
  }, [showExample, format, onChange]);

  return (
    <div className="cookie-input">
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <div>
          <Title level={5}>输入格式</Title>
          <Radio.Group 
            value={format} 
            onChange={(e) => handleFormatChange(e.target.value as CookieInputFormat)}
          >
            <Radio.Button value="string">字符串格式</Radio.Button>
            <Radio.Button value="json">JSON格式</Radio.Button>
            <Radio.Button value="browser">浏览器格式</Radio.Button>
            <Radio.Button value="header">Header格式</Radio.Button>
          </Radio.Group>
        </div>

        <div>
          <div className="cookie-input-header">
            <Title level={5}>Cookie内容</Title>
            <Space>
              <Button 
                size="small" 
                icon={<CopyOutlined />}
                onClick={handlePaste}
              >
                粘贴
              </Button>
              <Button 
                size="small" 
                icon={<FormatPainterOutlined />}
                onClick={handleFormat}
              >
                格式化
              </Button>
              <Button 
                size="small" 
                type={showExample ? 'primary' : 'default'}
                onClick={handleShowExample}
              >
                {showExample ? '隐藏示例' : '显示示例'}
              </Button>
            </Space>
          </div>
          
          <TextArea
            value={value}
            onChange={handleValueChange}
            placeholder={formatPlaceholders[format]}
            rows={8}
            className="cookie-input-textarea"
          />
          
          <Text type="secondary" className="cookie-input-hint">
            提示：支持从浏览器开发者工具直接复制Cookie，系统会自动识别格式
          </Text>
        </div>
      </Space>
    </div>
  );
};

export default CookieInput;