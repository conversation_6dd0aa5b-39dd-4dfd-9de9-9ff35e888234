/* 进度监控样式 */
.progress-monitor {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow-sm);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-5);
}

.progress-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
}

.progress-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.status-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.status-icon.running {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success-500);
}

.status-icon.paused {
  background: rgba(251, 191, 36, 0.1);
  color: var(--color-warning-500);
}

.status-icon.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success-500);
}

.status-icon.idle {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* 进度条 */
.progress-bar-container {
  margin-bottom: var(--spacing-5);
  position: relative;
}

.progress-bar {
  height: 24px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-info-400), var(--color-info-500));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill.running {
  background: linear-gradient(90deg, var(--color-info-400), var(--color-info-500));
}

.progress-fill.completed {
  background: linear-gradient(90deg, var(--color-success-400), var(--color-success-500));
}

.progress-fill.paused {
  background: linear-gradient(90deg, var(--color-warning-400), var(--color-warning-500));
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-percentage {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.progress-percentage-outside {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 统计信息 */
.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.stat-item:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-card);
  border-radius: var(--radius-md);
  color: var(--color-info-500);
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 实时指示器 */
.live-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
  padding: var(--spacing-2) var(--spacing-4);
  background: rgba(16, 185, 129, 0.1);
  border-radius: var(--radius-full);
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: var(--color-success-500);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.live-text {
  font-size: var(--font-size-sm);
  color: var(--color-success-600);
  font-weight: var(--font-weight-medium);
}

/* 旋转动画 */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .progress-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-item {
    padding: var(--spacing-2);
  }
  
  .progress-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
}