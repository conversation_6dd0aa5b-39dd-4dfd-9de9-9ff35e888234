# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv/

# Logs
logs/
*.log
error_*.log
pdd_crawler_*.log

# Output
output/
*.xlsx
*.csv

# Config (敏感信息)
config/cookies.json
config/cookies_backup_*.json

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Test
.pytest_cache/
htmlcov/
.coverage
*.cover
.hypothesis/

# Temporary
*.tmp
*.temp
nul

# Frontend
frontend/node_modules/
frontend/dist/
frontend/.vite/

# Serena git repo
serena/

# Browser data
browser_data/
browser_data/cdp_pdd_user_data_dir/

# 测试和临时文件
test_*.py
analyze_*.py
debug_*.py
verify_*.py
simple_*.py
manual_*.py
final_*.py
*_output.txt
*_results.txt
*_report.txt
*_check.txt
*_analysis.txt
test_*.html

# 临时脚本
restart_*.bat
restart_*.ps1
update_*.py
run_*_test.py
fix_*.bat
start_*.bat
start_*.sh
stop_*.bat
stop_*.sh

# 临时文档
COOKIE_*.md
cookie*.md
*_fix_*.md
*_summary.md
websocket_*.md

# 工具缓存
.serena/cache/
.claude/

# 其他临时文件
bit.js
butier.js
*.bak