import React, { useState } from 'react';
import { ConfigProvider, Layout, Typography, Space, message } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { CookieManager } from '../components/CookieManager';
import { <PERSON><PERSON> } from '../types/cookie';
import 'antd/dist/reset.css';

const { Header, Content } = Layout;
const { Title } = Typography;

const CookieManagementDemo: React.FC = () => {
  const [savedCookies, setSavedCookies] = useState<Cookie[]>([]);

  const handleCookieUpdate = (cookies: Cookie[]) => {
    console.log('Cookie更新:', cookies);
  };

  const handleCookieSave = async (cookies: Cookie[]): Promise<void> => {
    // 模拟API调用保存Cookie
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          setSavedCookies(cookies);
          console.log('Cookie已保存到后端:', cookies);
          resolve();
        } catch (error) {
          reject(error);
        }
      }, 1000);
    });
  };

  return (
    <ConfigProvider locale={zhCN}>
      <Layout style={{ minHeight: '100vh' }}>
        <Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
          <Title level={3} style={{ margin: '16px 0' }}>拼多多爬虫 - Cookie管理演示</Title>
        </Header>
        <Content style={{ padding: '24px', background: '#f0f2f5' }}>
          <div style={{ maxWidth: 1200, margin: '0 auto' }}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <CookieManager
                onCookieUpdate={handleCookieUpdate}
                onCookieSave={handleCookieSave}
                initialCookies={savedCookies}
              />
              
              {savedCookies.length > 0 && (
                <div style={{ 
                  background: '#fff', 
                  padding: '16px', 
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                  <Title level={5}>已保存的Cookie数量: {savedCookies.length}</Title>
                </div>
              )}
            </Space>
          </div>
        </Content>
      </Layout>
    </ConfigProvider>
  );
};

export default CookieManagementDemo;