#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼多多品牌ID数据收集脚本
用于扩充brand_id_mapping字典

功能：
1. 收集指定关键词的商品数据
2. 提取品牌ID和品牌名称信息
3. 生成品牌ID分析报告
4. 建议新的映射关系
"""

import sys
import os
import json
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Set
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.browser_manager import BrowserManager
from src.core.api_response_monitor import APIResponseMonitor
from src.core.anti_detection_simple import AntiDetectionManager
from src.core.login_detector import LoginDetector
from src.utils.helpers import load_config, save_json, ensure_dir

class BrandIDCollector:
    """品牌ID数据收集器"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化收集器"""
        self.config = load_config(config_path)
        
        # 初始化组件
        self.browser_manager = BrowserManager(config_path)
        self.login_detector = LoginDetector()
        self.anti_detection = AntiDetectionManager()
        self.api_monitor = APIResponseMonitor()
        
        # 数据收集配置
        self.keywords = ["冰箱", "空调", "热水器", "洗衣机"]
        self.target_per_keyword = 60
        self.total_target = len(self.keywords) * self.target_per_keyword
        
        # 数据存储
        self.collected_data = {}  # 按关键词分类的原始数据
        self.brand_data = defaultdict(list)  # 品牌ID数据
        self.analysis_results = {}  # 分析结果
        
        # 文件路径
        self.data_dir = Path("data")
        self.responses_dir = self.data_dir / "api_responses"
        self.analysis_dir = self.data_dir / "analysis"
        
        # 确保目录存在
        ensure_dir(str(self.responses_dir))
        ensure_dir(str(self.analysis_dir))
        
        logger.info("品牌ID收集器初始化完成")
    
    async def collect_brand_data(self) -> Dict[str, Any]:
        """
        收集品牌ID数据
        
        Returns:
            Dict[str, Any]: 收集结果摘要
        """
        logger.info("🚀 开始收集品牌ID数据")
        logger.info(f"目标关键词: {self.keywords}")
        logger.info(f"每个关键词收集: {self.target_per_keyword} 个商品")
        logger.info(f"总目标: {self.total_target} 个商品")
        
        start_time = datetime.now()
        
        try:
            # 启动浏览器
            async with self.browser_manager as browser_mgr:
                await browser_mgr.create_context()
                page = await browser_mgr.create_page()
                
                # 设置API监听回调
                self.api_monitor.set_data_callback(self._on_data_received)
                
                # 启动反风控监控
                await self.anti_detection.monitor_requests(page)
                
                # 检查登录状态
                await self._navigate_to_homepage(page)
                await self.login_detector.ensure_logged_in(page)
                
                # 逐个关键词收集数据
                for keyword in self.keywords:
                    logger.info(f"📊 开始收集关键词: {keyword}")
                    await self._collect_keyword_data(page, keyword)
                    
                    # 保存当前关键词的数据
                    await self._save_keyword_data(keyword)
                    
                    # 关键词间的延迟
                    await self.anti_detection.add_simple_delay()
                
                # 生成分析报告
                await self._generate_analysis_report()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 返回收集结果摘要
                return {
                    "success": True,
                    "message": "品牌ID数据收集完成",
                    "duration": duration,
                    "keywords_processed": len(self.keywords),
                    "total_goods_collected": sum(len(data) for data in self.collected_data.values()),
                    "unique_brand_ids": len(self.brand_data),
                    "analysis_file": str(self.analysis_dir / "brand_id_analysis.json")
                }
                
        except Exception as e:
            logger.error(f"数据收集失败: {e}")
            return {
                "success": False,
                "message": f"收集失败: {str(e)}",
                "error": str(e)
            }
    
    async def _navigate_to_homepage(self, page) -> None:
        """导航到首页"""
        base_url = self.config.get("target", {}).get("base_url", "https://mobile.yangkeduo.com")
        
        logger.info("导航到拼多多首页...")
        await page.goto(base_url, wait_until="networkidle")
        await asyncio.sleep(3)
    
    async def _collect_keyword_data(self, page, keyword: str) -> None:
        """收集单个关键词的数据"""
        try:
            # 清空之前的监听数据
            self.api_monitor.monitored_data.clear()
            
            # 设置API监听
            await self.api_monitor.setup_monitoring(page, keyword)
            
            # 导航到搜索页面
            await self._navigate_to_search(page, keyword)
            
            # 等待数据加载
            await asyncio.sleep(5)
            
            # 模拟滚动以加载更多数据
            await self._simulate_scrolling(page)
            
            # 收集监听到的数据
            collected_goods = list(self.api_monitor.monitored_data)
            self.collected_data[keyword] = collected_goods
            
            logger.info(f"关键词 '{keyword}' 收集到 {len(collected_goods)} 个商品")
            
            # 提取品牌数据
            self._extract_brand_data(keyword, collected_goods)
            
        except Exception as e:
            logger.error(f"收集关键词 '{keyword}' 数据失败: {e}")
    
    async def _navigate_to_search(self, page, keyword: str) -> None:
        """导航到搜索页面"""
        base_url = self.config.get("target", {}).get("base_url", "https://mobile.yangkeduo.com")
        
        # URL编码关键词
        import urllib.parse
        encoded_keyword = urllib.parse.quote(keyword)
        search_url = f"{base_url}/search_result.html?search_key={encoded_keyword}"
        
        logger.info(f"导航到搜索页面: {keyword}")
        await page.goto(search_url, wait_until="networkidle")
        await asyncio.sleep(3)
    
    async def _simulate_scrolling(self, page) -> None:
        """模拟滚动以加载更多数据"""
        logger.info("模拟滚动加载更多数据...")
        
        for i in range(5):  # 滚动5次
            # 滚动到页面底部
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(2)
            
            # 滚动回中间位置
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
            await asyncio.sleep(1)
    
    def _extract_brand_data(self, keyword: str, goods_list: List[Dict]) -> None:
        """从商品数据中提取品牌信息"""
        for goods in goods_list:
            brand_id = goods.get("brand_id")
            brand_name = goods.get("brand_name", "")
            goods_name = goods.get("goods_name", "")
            
            if brand_id:  # 只收集有brand_id的数据
                brand_info = {
                    "brand_id": str(brand_id),
                    "brand_name": brand_name,
                    "goods_name": goods_name,
                    "keyword": keyword,
                    "goods_id": goods.get("goods_id"),
                    "price": goods.get("price"),
                    "sales": goods.get("sales")
                }
                
                self.brand_data[str(brand_id)].append(brand_info)
    
    async def _save_keyword_data(self, keyword: str) -> None:
        """保存关键词数据到文件"""
        if keyword not in self.collected_data:
            return
        
        # 文件名映射
        filename_map = {
            "冰箱": "refrigerator_responses.json",
            "空调": "air_conditioner_responses.json", 
            "热水器": "water_heater_responses.json",
            "洗衣机": "washing_machine_responses.json"
        }
        
        filename = filename_map.get(keyword, f"{keyword}_responses.json")
        filepath = self.responses_dir / filename
        
        data_to_save = {
            "keyword": keyword,
            "collected_at": datetime.now().isoformat(),
            "total_goods": len(self.collected_data[keyword]),
            "goods_data": self.collected_data[keyword]
        }
        
        save_json(data_to_save, str(filepath))
        logger.info(f"已保存关键词 '{keyword}' 数据到: {filepath}")
    
    async def _on_data_received(self, goods_list: List[Dict]) -> None:
        """API监听数据回调"""
        logger.debug(f"收到 {len(goods_list)} 条商品数据")
    
    async def _generate_analysis_report(self) -> None:
        """生成品牌ID分析报告"""
        logger.info("📈 生成品牌ID分析报告...")
        
        # 统计分析
        brand_id_stats = {}
        brand_name_counter = Counter()
        keyword_stats = defaultdict(int)
        
        for brand_id, brand_list in self.brand_data.items():
            # 品牌ID统计
            brand_names = [item["brand_name"] for item in brand_list if item["brand_name"]]
            most_common_name = Counter(brand_names).most_common(1)
            most_common_name = most_common_name[0][0] if most_common_name else ""
            
            brand_id_stats[brand_id] = {
                "brand_id": brand_id,
                "most_common_name": most_common_name,
                "all_names": list(set(brand_names)),
                "occurrence_count": len(brand_list),
                "keywords": list(set(item["keyword"] for item in brand_list)),
                "sample_goods": [item["goods_name"] for item in brand_list[:3]]  # 前3个商品示例
            }
            
            # 品牌名称计数
            if most_common_name:
                brand_name_counter[most_common_name] += len(brand_list)
            
            # 关键词统计
            for item in brand_list:
                keyword_stats[item["keyword"]] += 1
        
        # 生成建议的映射关系
        suggested_mappings = {}
        for brand_id, stats in brand_id_stats.items():
            if stats["most_common_name"]:
                suggested_mappings[brand_id] = stats["most_common_name"]
        
        # 生成完整报告
        analysis_report = {
            "generated_at": datetime.now().isoformat(),
            "collection_summary": {
                "total_keywords": len(self.keywords),
                "keywords_processed": list(self.keywords),
                "total_goods_collected": sum(len(data) for data in self.collected_data.values()),
                "unique_brand_ids_found": len(self.brand_data),
                "goods_per_keyword": {k: len(v) for k, v in self.collected_data.items()}
            },
            "brand_id_analysis": brand_id_stats,
            "brand_name_frequency": dict(brand_name_counter.most_common()),
            "keyword_distribution": dict(keyword_stats),
            "suggested_brand_id_mappings": suggested_mappings,
            "implementation_code": self._generate_implementation_code(suggested_mappings)
        }
        
        # 保存分析报告
        analysis_file = self.analysis_dir / "brand_id_analysis.json"
        save_json(analysis_report, str(analysis_file))
        
        # 保存简化的映射文件
        mapping_file = self.analysis_dir / "suggested_mappings.json"
        save_json(suggested_mappings, str(mapping_file))
        
        logger.info(f"✅ 分析报告已保存到: {analysis_file}")
        logger.info(f"✅ 建议映射已保存到: {mapping_file}")
        
        # 打印摘要
        self._print_analysis_summary(analysis_report)
    
    def _generate_implementation_code(self, mappings: Dict[str, str]) -> str:
        """生成实现代码"""
        if not mappings:
            return "# 没有发现新的品牌ID映射"
        
        code_lines = [
            "# 建议添加到 src/data/processor.py 的 brand_id_mapping 字典中:",
            "self.brand_id_mapping = {",
            '    # 现有映射...',
            '    "3838": "统帅",  # 统帅品牌ID',
            "",
            "    # 新发现的品牌ID映射:"
        ]
        
        for brand_id, brand_name in sorted(mappings.items()):
            code_lines.append(f'    "{brand_id}": "{brand_name}",  # {brand_name}品牌ID')
        
        code_lines.append("}")
        
        return "\n".join(code_lines)
    
    def _print_analysis_summary(self, report: Dict[str, Any]) -> None:
        """打印分析摘要"""
        summary = report["collection_summary"]
        mappings = report["suggested_brand_id_mappings"]
        
        print("\n" + "="*60)
        print("📊 品牌ID收集分析摘要")
        print("="*60)
        print(f"🎯 处理关键词: {summary['total_keywords']} 个")
        print(f"📦 收集商品: {summary['total_goods_collected']} 个")
        print(f"🏷️  发现品牌ID: {summary['unique_brand_ids_found']} 个")
        print(f"✨ 建议新映射: {len(mappings)} 个")
        
        print("\n📈 各关键词收集情况:")
        for keyword, count in summary["goods_per_keyword"].items():
            print(f"  {keyword}: {count} 个商品")
        
        if mappings:
            print(f"\n🆕 发现的新品牌ID映射 (前10个):")
            for i, (brand_id, brand_name) in enumerate(list(mappings.items())[:10]):
                print(f"  {brand_id} -> {brand_name}")
            
            if len(mappings) > 10:
                print(f"  ... 还有 {len(mappings) - 10} 个映射")
        
        print("\n📁 文件保存位置:")
        print(f"  原始数据: {self.responses_dir}")
        print(f"  分析报告: {self.analysis_dir / 'brand_id_analysis.json'}")
        print(f"  建议映射: {self.analysis_dir / 'suggested_mappings.json'}")
        print("="*60)

async def main():
    """主函数"""
    print("🚀 拼多多品牌ID数据收集器")
    print("="*50)
    
    collector = BrandIDCollector()
    
    try:
        result = await collector.collect_brand_data()
        
        if result["success"]:
            print(f"\n✅ 收集完成!")
            print(f"⏱️  耗时: {result['duration']:.1f} 秒")
            print(f"📊 处理关键词: {result['keywords_processed']} 个")
            print(f"📦 收集商品: {result['total_goods_collected']} 个")
            print(f"🏷️  发现品牌ID: {result['unique_brand_ids']} 个")
            print(f"📄 分析报告: {result['analysis_file']}")
        else:
            print(f"\n❌ 收集失败: {result['message']}")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断收集")
    except Exception as e:
        print(f"\n❌ 收集出错: {e}")
        logger.error(f"收集出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
