import { <PERSON><PERSON> } from '../types/cookie';

interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

interface CookieValidateResponse extends ApiResponse {
  valid: boolean;
}

interface CookieStatusResponse extends ApiResponse {
  exists: boolean;
  valid: boolean;
  expiresAt?: string;
  cookies?: Cookie[];
}

interface ExportResponse extends ApiResponse {
  exportId?: string;
  fileName?: string;
}

interface CrawlStartResponse extends ApiResponse {
  taskId: string;
}

interface WebSocketCallbacks {
  onOpen?: () => void;
  onMessage?: (data: any) => void;
  onProgress?: (data: any) => void;
  onData?: (data: any) => void;
  onCompleted?: (data: any) => void;
  onError?: (data: any) => void;
  onClose?: () => void;
}

declare class ApiClient {
  taskId: string | null;
  websocket: WebSocket | null;

  // 爬虫相关
  startCrawl(config: {
    keywords?: string;
    keyword?: string;
    targetCount?: number;
    sortType?: string;
    maxPages?: number;
  }): Promise<CrawlStartResponse>;
  pauseCrawl(): Promise<ApiResponse>;
  resumeCrawl(): Promise<ApiResponse>;
  stopCrawl(): Promise<ApiResponse>;
  getCrawlStatus(): Promise<ApiResponse>;
  getPreviewData(limit?: number): Promise<ApiResponse>;
  exportData(): Promise<ExportResponse>;
  connectWebSocket(callbacks: WebSocketCallbacks): WebSocket;
  disconnectWebSocket(): void;
  checkHealth(): Promise<boolean>;

  // Cookie管理
  validateCookie(cookies: Cookie[]): Promise<CookieValidateResponse>;
  saveCookie(cookies: Cookie[]): Promise<ApiResponse>;
  getCookieStatus(): Promise<CookieStatusResponse>;
  clearCookie(): Promise<ApiResponse>;
  exportCookie(): Promise<ApiResponse>;
  importCookie(cookieData: any): Promise<ApiResponse>;
}

declare const apiClient: ApiClient;
export default apiClient;