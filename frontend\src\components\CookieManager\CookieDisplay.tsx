import React, { useState, useCallback } from 'react';
import { Table, Tag, Space, Typography, Button, Tooltip, message } from 'antd';
import { 
  EyeOutlined, 
  EyeInvisibleOutlined, 
  CopyOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import { Cookie, CookieValidationResult } from '../../types/cookie';
import './CookieDisplay.css';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Text } = Typography;

interface CookieDisplayProps {
  cookies: Cookie[];
  validationResult: CookieValidationResult | null;
}

const CookieDisplay: React.FC<CookieDisplayProps> = ({ cookies, validationResult }) => {
  const [showValues, setShowValues] = useState<Record<string, boolean>>({});
  const [copiedCookies, setCopiedCookies] = useState<Record<string, boolean>>({});

  const toggleValueVisibility = useCallback((cookieName: string) => {
    setShowValues(prev => ({
      ...prev,
      [cookieName]: !prev[cookieName]
    }));
  }, []);

  const maskValue = useCallback((value: string, show: boolean): string => {
    if (show || value.length <= 8) return value;
    
    const visibleLength = Math.min(4, Math.floor(value.length / 4));
    const start = value.substring(0, visibleLength);
    const end = value.substring(value.length - visibleLength);
    const masked = '*'.repeat(Math.max(value.length - visibleLength * 2, 4));
    
    return `${start}${masked}${end}`;
  }, []);

  const copyToClipboard = useCallback(async (text: string, name: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCookies(prev => ({ ...prev, [name]: true }));
      message.success('已复制到剪贴板');
      
      // 3秒后重置复制状态
      setTimeout(() => {
        setCopiedCookies(prev => ({ ...prev, [name]: false }));
      }, 3000);
    } catch (error) {
      message.error('复制失败');
    }
  }, []);

  const getExpiryStatus = useCallback((expires?: Date | string) => {
    if (!expires) return { status: 'normal', text: '会话Cookie' };
    
    const expiryDate = new Date(expires);
    const now = new Date();
    const dayFromNow = new Date();
    dayFromNow.setDate(dayFromNow.getDate() + 1);
    
    if (expiryDate < now) {
      return { status: 'expired', text: '已过期' };
    } else if (expiryDate < dayFromNow) {
      return { status: 'expiring', text: '即将过期' };
    } else {
      return { status: 'normal', text: dayjs(expiryDate).fromNow() + '过期' };
    }
  }, []);

  const columns: ColumnsType<Cookie> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (name: string) => (
        <Space>
          <Text strong>{name}</Text>
          <Button
            size="small"
            type="text"
            icon={copiedCookies[name] ? <CheckCircleOutlined /> : <CopyOutlined />}
            onClick={() => copyToClipboard(name, name)}
          />
        </Space>
      ),
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
      width: '35%',
      render: (value: string, record: Cookie) => {
        const isShown = showValues[record.name];
        const displayValue = maskValue(value, isShown);
        
        return (
          <Space>
            <Text code className="cookie-value">
              {displayValue}
            </Text>
            <Tooltip title={isShown ? '隐藏' : '显示'}>
              <Button
                size="small"
                type="text"
                icon={isShown ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={() => toggleValueVisibility(record.name)}
              />
            </Tooltip>
            <Button
              size="small"
              type="text"
              icon={<CopyOutlined />}
              onClick={() => copyToClipboard(value, `${record.name}_value`)}
            />
          </Space>
        );
      },
    },
    {
      title: '域',
      dataIndex: 'domain',
      key: 'domain',
      width: '15%',
      render: (domain?: string) => domain || '-',
    },
    {
      title: '过期时间',
      dataIndex: 'expires',
      key: 'expires',
      width: '20%',
      render: (expires?: Date | string) => {
        const { status, text } = getExpiryStatus(expires);
        
        if (status === 'expired') {
          return <Tag color="error" icon={<CloseCircleOutlined />}>{text}</Tag>;
        } else if (status === 'expiring') {
          return <Tag color="warning" icon={<WarningOutlined />}>{text}</Tag>;
        } else {
          return <Tag color="success" icon={<CheckCircleOutlined />}>{text}</Tag>;
        }
      },
    },
    {
      title: '属性',
      key: 'attributes',
      width: '10%',
      render: (_, record: Cookie) => (
        <Space size={0}>
          {record.secure && <Tag color="green">Secure</Tag>}
          {record.httpOnly && <Tag color="blue">HttpOnly</Tag>}
          {record.sameSite && <Tag color="orange">{record.sameSite}</Tag>}
        </Space>
      ),
    },
  ];

  const handleCopyAll = useCallback(() => {
    const cookieString = cookies
      .map(cookie => `${cookie.name}=${cookie.value}`)
      .join('; ');
    
    copyToClipboard(cookieString, 'all');
  }, [cookies, copyToClipboard]);

  return (
    <div className="cookie-display">
      <div className="cookie-display-header">
        <Space>
          <Text strong>Cookie列表 ({cookies.length}个)</Text>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={handleCopyAll}
          >
            复制所有
          </Button>
        </Space>
        
        {validationResult && (
          <Space>
            {validationResult.errors.length > 0 && (
              <Tag color="error">{validationResult.errors.length} 个错误</Tag>
            )}
            {validationResult.warnings.length > 0 && (
              <Tag color="warning">{validationResult.warnings.length} 个警告</Tag>
            )}
            {validationResult.isValid && (
              <Tag color="success" icon={<CheckCircleOutlined />}>验证通过</Tag>
            )}
          </Space>
        )}
      </div>
      
      <Table
        columns={columns}
        dataSource={cookies}
        rowKey="name"
        size="small"
        pagination={false}
        scroll={{ x: 800 }}
      />
      
      <div className="cookie-display-footer">
        <Text type="secondary">
          提示：点击眼睛图标查看完整Cookie值，点击复制图标复制内容
        </Text>
      </div>
    </div>
  );
};

export default CookieDisplay;