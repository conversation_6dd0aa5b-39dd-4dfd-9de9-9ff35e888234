import React from 'react'
import { commonShortcuts, formatShortcut } from '../hooks/useKeyboardShortcuts'
import './HelpModal.css'

const HelpModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null

  const shortcuts = [
    { ...commonShortcuts.search, category: '常用操作' },
    { ...commonShortcuts.startCrawl, category: '爬虫控制' },
    { ...commonShortcuts.stopCrawl, category: '爬虫控制' },
    { ...commonShortcuts.exportData, category: '数据管理' },
    { ...commonShortcuts.cookieManager, category: '系统设置' },
    { ...commonShortcuts.toggleTheme, category: '系统设置' },
    { ...commonShortcuts.help, category: '其他' },
    { ...commonShortcuts.escape, category: '其他' }
  ]

  // 按类别分组
  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = []
    }
    acc[shortcut.category].push(shortcut)
    return acc
  }, {})

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="help-modal" onClick={(e) => e.stopPropagation()}>
        <div className="help-modal-header">
          <h2>键盘快捷键</h2>
          <button className="modal-close" onClick={onClose}>
            ×
          </button>
        </div>
        
        <div className="help-modal-content">
          {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (
            <div key={category} className="shortcut-category">
              <h3>{category}</h3>
              <div className="shortcut-list">
                {shortcuts.map((shortcut, index) => (
                  <div key={index} className="shortcut-item">
                    <span className="shortcut-description">{shortcut.description}</span>
                    <kbd className="shortcut-keys">
                      {formatShortcut(shortcut.keys)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        <div className="help-modal-footer">
          <p>提示：在输入框中时快捷键不生效</p>
        </div>
      </div>
    </div>
  )
}

export default HelpModal