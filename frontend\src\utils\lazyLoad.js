import React, { lazy, Suspense } from 'react'
import Skeleton, { SkeletonCard, SkeletonTable } from '../components/Skeleton'

// 延迟加载组件的包装函数
export const lazyLoadComponent = (importFunc, fallback = null) => {
  const LazyComponent = lazy(importFunc)
  
  return (props) => (
    <Suspense fallback={fallback || <DefaultFallback />}>
      <LazyComponent {...props} />
    </Suspense>
  )
}

// 默认加载占位符
const DefaultFallback = () => (
  <div className="loading-container">
    <div className="loading-spinner" />
  </div>
)

// 特定类型的加载占位符
export const CardFallback = () => <SkeletonCard lines={3} />
export const TableFallback = () => <SkeletonTable rows={5} columns={4} />

// 带重试的延迟加载
export const lazyLoadWithRetry = (importFunc, fallback = null) => {
  const LazyComponent = lazy(() => 
    importFunc().catch(() => {
      // 组件加载失败时，等待一段时间后重试
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(importFunc())
        }, 1500)
      })
    })
  )
  
  return (props) => (
    <Suspense fallback={fallback || <DefaultFallback />}>
      <LazyComponent {...props} />
    </Suspense>
  )
}

// 预加载组件
export const preloadComponent = (importFunc) => {
  // 在空闲时预加载组件
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFunc()
    })
  } else {
    // 降级方案
    setTimeout(() => {
      importFunc()
    }, 1)
  }
}