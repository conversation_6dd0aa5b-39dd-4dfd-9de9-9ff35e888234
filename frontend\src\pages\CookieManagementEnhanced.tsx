import React from 'react';
import { ConfigProvider, Layout, Space, Typography, Divider } from 'antd';
import CookieManager from '../components/CookieManager';
import { Cookie } from '../types/cookie';

const { Header, Content } = Layout;
const { Title, Paragraph } = Typography;

const CookieManagementEnhanced: React.FC = () => {
  const handleCookieUpdate = (cookies: Cookie[]) => {
    console.log('Cookies updated:', cookies);
  };

  const handleCookieSave = async (cookies: Cookie[]) => {
    console.log('Saving cookies:', cookies);
    // 这里可以添加额外的保存逻辑
  };

  return (
    <ConfigProvider>
      <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
        <Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}>
          <Title level={3} style={{ margin: '16px 0' }}>拼多多爬虫 - Cookie管理增强版</Title>
        </Header>
        <Content style={{ padding: '24px' }}>
          <div style={{ maxWidth: 1200, margin: '0 auto' }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <Title level={4}>功能特性</Title>
                <Paragraph>
                  本示例展示了增强版的Cookie管理功能：
                </Paragraph>
                <ul>
                  <li>✅ Cookie验证API调用 - 实时验证Cookie有效性</li>
                  <li>✅ Cookie保存API调用 - 持久化存储Cookie到后端</li>
                  <li>✅ Cookie状态检查 - 自动检查Cookie状态并显示</li>
                  <li>✅ Cookie清除功能 - 一键清除所有Cookie</li>
                  <li>✅ Cookie导入/导出功能 - 支持JSON格式的导入导出</li>
                  <li>📊 Loading状态提示 - 所有操作都有加载状态</li>
                  <li>🔒 错误处理 - 完善的错误提示和处理机制</li>
                </ul>
              </div>

              <Divider />

              <CookieManager 
                onCookieUpdate={handleCookieUpdate}
                onCookieSave={handleCookieSave}
              />

              <div style={{ marginTop: 40 }}>
                <Title level={4}>API集成说明</Title>
                <Paragraph>
                  组件已集成以下API端点：
                </Paragraph>
                <ul>
                  <li><code>POST /api/cookie/validate</code> - 验证Cookie有效性</li>
                  <li><code>POST /api/cookie/save</code> - 保存Cookie到服务器</li>
                  <li><code>GET /api/cookie/status</code> - 获取Cookie状态</li>
                  <li><code>DELETE /api/cookie/clear</code> - 清除服务器上的Cookie</li>
                  <li><code>GET /api/cookie/export</code> - 导出Cookie数据</li>
                  <li><code>POST /api/cookie/import</code> - 导入Cookie数据</li>
                </ul>
              </div>
            </Space>
          </div>
        </Content>
      </Layout>
    </ConfigProvider>
  );
};

export default CookieManagementEnhanced;