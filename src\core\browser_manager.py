"""
浏览器管理器模块
负责浏览器的创建、配置、反检测和生命周期管理
"""

import asyncio
import random
from typing import Dict, List, Optional, Any

from playwright.async_api import async_playwright, <PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Page, Playwright
from loguru import logger

# 尝试导入playwright-stealth，如果失败则使用内置方案
try:
    from playwright_stealth import Stealth
    HAS_STEALTH = True
except ImportError:
    HAS_STEALTH = False
    logger.warning("playwright-stealth导入失败，将使用stealth.min.js")

from src.utils.helpers import load_config, load_json
from .cdp_browser_manager import CDPBrowserManager
from .stealth_manager import StealthManager


class BrowserManager:
    """
    浏览器管理器类
    支持CDP模式（连接真实浏览器）和标准模式（启动自动化浏览器）
    """
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化浏览器管理器"""
        self.config = load_config(config_path)
        self.user_agents = load_json("config/user_agents.json")
        # 移除旧的cookies配置加载
        # self.cookies_config = load_json("config/cookies.json")
        
        # 使用CookieManager来管理cookies
        from .cookie_manager import CookieManager
        self.cookie_manager = CookieManager()
        
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # CDP模式相关
        self.cdp_config = self.config.get("cdp", {})
        self.use_cdp_mode = self.cdp_config.get("enabled", True)
        self.cdp_manager: Optional[CDPBrowserManager] = None
        
        # Stealth管理器
        self.stealth_manager = StealthManager()
        
        # 反检测配置
        self.stealth_enabled = self.config.get("stealth", {}).get("enabled", True)
        self.current_user_agent = None
        
        # 使用轻量级指纹管理器
        from .lightweight_fingerprint import LightweightFingerprintManager
        from .user_agent_manager import UserAgentManager
        
        self.fingerprint_manager = LightweightFingerprintManager(config_path)
        self.user_agent_manager = UserAgentManager(config_path)
        
        # 当前设备信息
        self.current_device_info = None
        self.current_fingerprint = None
        
        logger.info(f"浏览器管理器初始化完成（CDP模式: {self.use_cdp_mode}）")
    
    async def start(self) -> None:
        """启动浏览器 - 优先使用CDP模式"""
        if self.playwright:
            return
        
        try:
            # 启动 Playwright
            self.playwright = await async_playwright().start()
            
            if self.use_cdp_mode:
                # 尝试使用CDP模式
                try:
                    logger.info("尝试使用CDP模式连接浏览器...")
                    self.cdp_manager = CDPBrowserManager(self.config._path if hasattr(self.config, '_path') else "config/settings.yaml")
                    self.context = await self.cdp_manager.launch_and_connect(
                        playwright=self.playwright,
                        playwright_proxy=None,  # CDP模式下代理需要在浏览器启动前配置
                        user_agent=None,  # 保持浏览器原有UA
                        headless=self.cdp_config.get("headless", False)
                    )
                    
                    # 应用stealth脚本
                    if self.stealth_enabled:
                        await self.stealth_manager.apply_stealth_to_context(self.context)
                    
                    logger.info("CDP模式启动成功")
                    return
                except Exception as e:
                    logger.warning(f"CDP模式启动失败，将回退到标准模式: {e}")
                    self.cdp_manager = None
            
            # 回退到标准模式（MediaCrawler式的极简参数）
            await self._start_standard_mode()
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            raise
    
    async def _start_standard_mode(self) -> None:
        """标准模式启动 - 使用极简参数"""
        browser_config = self.config.get("browser", {})
        browser_type = browser_config.get("type", "chromium")
        
        # 完整启动参数（与CDP模式保持一致）
        launch_options = {
            "headless": browser_config.get("headless", False),
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-web-security",  # 关键参数
                "--disable-features=IsolateOrigins,site-per-process",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--disable-features=VizDisplayCompositor",
                "--disable-infobars"
            ]
        }
        
        # 启动浏览器
        if browser_type == "chromium":
            self.browser = await self.playwright.chromium.launch(**launch_options)
        elif browser_type == "firefox":
            self.browser = await self.playwright.firefox.launch(**launch_options)
        elif browser_type == "webkit":
            self.browser = await self.playwright.webkit.launch(**launch_options)
        else:
            raise ValueError(f"不支持的浏览器类型: {browser_type}")
        
        logger.info(f"标准模式浏览器 {browser_type} 启动成功（极简参数）")
    
    async def create_context(self, **kwargs) -> BrowserContext:
        """创建新的浏览器上下文"""
        # 如果是CDP模式且已经有上下文，直接返回
        if self.use_cdp_mode and self.context:
            return self.context
        
        if not self.browser and not self.context:
            await self.start()
        
        # 如果是标准模式，需要创建上下文
        if not self.use_cdp_mode or not self.context:
            # 获取最优User-Agent和设备信息
            self.current_device_info = self.user_agent_manager.get_optimal_user_agent(
                target_site="pdd", 
                prefer_device="desktop"  # 使用桌面模式
            )
            
            # 生成轻量级设备指纹
            self.current_fingerprint = self.fingerprint_manager.generate_lightweight_fingerprint(
                self.current_device_info["user_agent"]
            )
            
            # 浏览器上下文配置
            browser_config = self.config.get("browser", {})
            
            # 获取locale信息（修复错误）
            locale_info = self.current_fingerprint.get("locale_info", {})
            if not locale_info:
                locale_info = {"language": "zh-CN", "languages": ["zh-CN", "zh", "en"]}
            
            # 使用设备信息配置上下文
            context_options = {
                "user_agent": self.current_device_info["user_agent"],
                "viewport": {
                    "width": self.current_device_info["screen"]["width"],
                    "height": self.current_device_info["screen"]["height"]
                },
                # 真实浏览器特征
                "has_touch": self.current_device_info["device_type"] in ["mobile", "tablet"],
                "is_mobile": self.current_device_info["device_type"] == "mobile",
                "ignore_https_errors": True,
                "bypass_csp": True,
                "device_scale_factor": self.current_device_info["screen"].get("pixel_ratio", 1),
                "locale": locale_info.get("language", "zh-CN"),
                "timezone_id": "Asia/Shanghai",
                "geolocation": {"longitude": 121.4737, "latitude": 31.2304},  # 上海坐标
                "permissions": ["geolocation", "notifications"],
                "color_scheme": "light",
                "reduced_motion": "no-preference",
                "forced_colors": "none",
                "extra_http_headers": self._generate_enhanced_headers(),
                "java_script_enabled": True,
                "accept_downloads": False,
                **kwargs
            }
            
            # 创建上下文
            self.context = await self.browser.new_context(**context_options)
            
            # 应用stealth脚本
            if self.stealth_enabled:
                await self.stealth_manager.apply_stealth_to_context(self.context)
            
            logger.info(f"浏览器上下文创建成功 - 设备: {self.current_device_info['device_type']}, "
                       f"浏览器: {self.current_device_info['browser']}")
        
        # 注入Cookie
        await self._inject_cookies()
        
        return self.context


    
    # 方法已被移除，不再需要补充脚本
    # playwright-stealth 将处理所有反检测需求
    
    async def rotate_identity(self) -> None:
        """轮换身份信息"""
        logger.info("开始轮换身份信息...")
        
        try:
            # 关闭当前上下文
            if self.context:
                await self.context.close()
            
            # 强制轮换User-Agent
            self.user_agent_manager.force_rotation()
            
            # 创建新的上下文和页面
            await self.create_context()
            await self.create_page()
            
            logger.info("身份轮换完成")
            
        except Exception as e:
            logger.error(f"身份轮换失败: {e}")
            raise
    
    def get_current_device_info(self) -> Optional[Dict]:
        """获取当前设备信息"""
        return self.current_device_info
    
    def get_current_fingerprint(self) -> Optional[Dict]:
        """获取当前设备指纹"""
        return self.current_fingerprint
    
    def _generate_enhanced_headers(self) -> Dict[str, str]:
        """生成增强版HTTP头（基于browserscan.net测试优化）"""
        # 增强版头部 - 更接近真实浏览器
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }

        # 根据设备类型添加适当的头部
        if self.current_device_info and self.current_device_info["device_type"] == "mobile":
            headers["Sec-CH-UA-Mobile"] = "?1"
        else:
            headers["Sec-CH-UA-Mobile"] = "?0"

        return headers
    
    async def create_page(self) -> Page:
        """创建新页面"""
        if not self.context:
            await self.create_context()
        
        self.page = await self.context.new_page()

        # CDP模式下stealth脚本已在上下文级别应用
        # 标准模式下应用额外的反检测措施
        if not self.use_cdp_mode:
            await self.stealth_manager.apply_additional_evasions(self.page)
            
            # 如果有playwright-stealth，也应用它
            if self.stealth_enabled and HAS_STEALTH:
                try:
                    stealth_config = Stealth(
                        navigator_languages=("zh-CN", "zh", "en"),
                        navigator_vendor="Google Inc.",
                        navigator_webdriver=True,
                        chrome_app=True,
                        chrome_runtime=True,
                        init_scripts_only=True
                    )
                    await stealth_config.apply_stealth_async(self.page)
                    logger.debug("已应用 playwright-stealth 反检测")
                except Exception as e:
                    logger.warning(f"应用 playwright-stealth 失败: {e}")

        # 设置页面错误处理（简化）
        self.page.on("pageerror", lambda error: logger.debug(f"页面JS错误: {error}"))
        self.page.on("console", lambda msg: self._handle_console_message(msg))

        # 标准模式下应用轻量级指纹
        if not self.use_cdp_mode and self.current_fingerprint:
            await self.fingerprint_manager.apply_lightweight_fingerprint(
                self.page, self.current_fingerprint
            )

        # 设置页面超时
        self.page.set_default_timeout(30000)
        self.page.set_default_navigation_timeout(30000)

        logger.info(f"页面创建完成（{'CDP' if self.use_cdp_mode else '标准'}模式）")
        return self.page


    def _handle_console_message(self, msg):
        """处理控制台消息"""
        # 过滤掉一些常见的无关错误
        text = msg.text
        if any(keyword in text.lower() for keyword in [
            "favicon", "manifest", "service worker", "sw.js",
            "chrome-extension", "extension", "devtools",
            "failed to load resource", "net::err_failed",
            "mixed content", "cors", "csp",
            # 过滤playwright-stealth相关错误
            "utils is not defined",
            "opts is not defined",
            "generatemagicarray is not defined",
            "skipping chrome loadtimes update",
            "uncaught referenceerror",
            "uncaught error"
        ]):
            return  # 忽略这些常见的资源加载错误和playwright-stealth错误
        
        # 只记录重要的错误
        if msg.type == "error":
            logger.debug(f"页面控制台错误: {text}")
        elif msg.type == "warning":
            logger.debug(f"页面控制台警告: {text}")
    
    async def refresh_identity(self) -> None:
        """刷新浏览器身份（反风控）"""
        logger.info("开始刷新浏览器身份...")
        
        try:
            # 关闭当前上下文
            if self.context:
                await self.context.close()
                self.context = None
                self.page = None
            
            # 等待一段时间
            await asyncio.sleep(random.uniform(2, 5))
            
            # 创建新的上下文
            await self.create_context()
            await self.create_page()
            
            logger.info("浏览器身份刷新完成")
            
        except Exception as e:
            logger.error(f"刷新浏览器身份失败: {e}")
            raise
    
    def _get_random_user_agent(self) -> str:
        """获取随机 User-Agent"""
        # 优先使用移动端 User-Agent（拼多多主要是移动端）
        mobile_agents = self.user_agents.get("mobile", [])
        desktop_agents = self.user_agents.get("desktop", [])
        
        # 80% 概率使用移动端，20% 概率使用桌面端
        if random.random() < 0.8 and mobile_agents:
            return random.choice(mobile_agents)
        elif desktop_agents:
            return random.choice(desktop_agents)
        else:
            # 默认 User-Agent
            return "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
    
    async def _inject_cookies(self) -> None:
        """注入 Cookie"""
        if not self.context:
            return
        
        # 使用CookieManager加载cookies
        success = await self.cookie_manager.load_cookies(self.context, "pdd")
        
        if not success:
            # 如果从browser_data加载失败，尝试从config/cookies.json加载（兼容性）
            logger.info("尝试从配置文件加载Cookie...")
            cookies = self.cookie_manager.load_cookies_from_config("config/cookies.json")
            
            if cookies:
                # 注入cookies
                await self.context.add_cookies(cookies)
                logger.info(f"从配置文件成功注入 {len(cookies)} 个 Cookie")
                
                # 保存到新位置以便下次使用
                await self.cookie_manager.save_cookies(self.context, "pdd")
            else:
                logger.warning("没有找到有效的 Cookie，可能影响登录状态")
    
    async def close(self) -> None:
        """关闭浏览器"""
        try:
            # 关闭页面
            if self.page and not self.page.is_closed():
                await self.page.close()
                self.page = None
            
            # CDP模式下的清理
            if self.cdp_manager:
                await self.cdp_manager.cleanup()
                self.cdp_manager = None
                self.context = None
            else:
                # 标准模式下的清理
                # 关闭上下文
                if self.context:
                    await self.context.close()
                    self.context = None
                
                # 关闭浏览器
                if self.browser:
                    await self.browser.close()
                    self.browser = None
            
            # 停止Playwright
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            logger.info("浏览器已关闭")
            
        except Exception as e:
            logger.error(f"关闭浏览器时出错: {e}")
            # 强制清理引用
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            self.cdp_manager = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
