# 统计型商品筛选器功能说明

## 功能概述

统计型商品筛选器是一个智能的两阶段筛选系统，能够自动识别商品集合中的主要产品类型，并过滤掉少数不相关的商品。

## 工作原理

### 第一阶段：基础筛选
- 使用原有的品牌、型号、规格匹配逻辑
- 支持海尔-统帅等主子品牌关系
- 基于关键词进行初步筛选

### 第二阶段：统计分析与智能过滤
1. **产品类型统计**：分析所有商品的产品类型分布
2. **主要类型识别**：识别占比超过70%的主要产品类型
3. **智能过滤**：过滤掉占比低于10%的少数类型商品

## 使用场景

当您搜索"海尔476"时：
- 如果60个商品中有50个是冰箱，8个是洗衣机，2个是其他
- 系统会识别冰箱为主要类型（83.3%）
- 自动过滤掉洗衣机和其他商品
- 只保留冰箱相关商品

## 配置说明

```yaml
product_filter:
  enabled: true                    # 启用筛选器
  match_threshold: 0.6            # 匹配阈值（降低以更包容）
  strict_mode: false              # 关闭严格模式
  statistical:
    enabled: true                 # 启用统计型筛选
    min_sample_size: 10          # 最小样本量
    major_type_threshold: 0.7    # 主要类型阈值（70%）
    minor_type_threshold: 0.1    # 少数类型阈值（10%）
    confidence_threshold: 0.8    # 置信度阈值
    log_statistics: true         # 记录统计信息
```

## 优势

1. **智能识别**：自动识别搜索结果中的主要商品类型
2. **准确过滤**：基于统计分析，而非硬编码规则
3. **灵活配置**：可调整各项阈值以适应不同场景
4. **保留相关性**：不会过滤掉统帅等子品牌商品

## 测试方法

运行测试脚本验证功能：
```bash
python test_statistical_filter.py
```

## 注意事项

1. 需要足够的样本量（默认最少10个）才能进行统计分析
2. 对于样本量较小的情况，会退回到基础筛选模式
3. 未识别类型的商品会被保留，避免误过滤特殊商品