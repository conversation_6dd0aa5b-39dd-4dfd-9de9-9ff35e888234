.parameter-settings {
  margin-bottom: 24px;
}

.settings-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 16px 0;
}

.parameters-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.parameter-card {
  background: var(--secondary);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.parameter-card:hover {
  border-color: rgba(238, 77, 45, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.parameter-header {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.parameter-icon {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  background: var(--surface);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
}

.parameter-info {
  flex: 1;
}

.parameter-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
  margin-bottom: 2px;
}

.parameter-description {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.parameter-control {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.control-button {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border);
  background: var(--surface);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:hover:not(:disabled) {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.control-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.parameter-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.parameter-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
  text-align: center;
  outline: none;
}

.parameter-input::-webkit-inner-spin-button,
.parameter-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.parameter-unit {
  position: absolute;
  right: 12px;
  font-size: 14px;
  color: var(--text-secondary);
  pointer-events: none;
}

.parameter-help {
  font-size: 11px;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
}

.parameter-progress {
  height: 4px;
  background: var(--border);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.parameters-validation {
  margin-top: 16px;
}

.validation-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
  border-radius: 8px;
  color: #ff9800;
  font-size: 13px;
}

.validation-warning svg {
  flex-shrink: 0;
}

/* 暗色主题适配 */
[data-theme="dark"] .parameter-card {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .parameter-icon {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .control-button {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .parameter-input-wrapper {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .validation-warning {
  background: rgba(255, 152, 0, 0.15);
  border-color: rgba(255, 152, 0, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .parameters-grid {
    grid-template-columns: 1fr;
  }
}