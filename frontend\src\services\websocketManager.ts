interface WebSocketMessage {
  type: 'progress' | 'data' | 'completed' | 'error' | 'heartbeat';
  data: any;
  timestamp: number;
}

interface WebSocketCallbacks {
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: any) => void;
  onProgress?: (data: any) => void;
  onData?: (data: any) => void;
  onCompleted?: (data: any) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

interface WebSocketConfig {
  url: string;
  taskId: string;
  callbacks: WebSocketCallbacks;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  heartbeatInterval?: number;
  messageBufferSize?: number;
}

class WebSocketManager {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectCount = 0;
  private heartbeatTimer: number | null = null;
  private reconnectTimer: number | null = null;
  private messageBuffer: WebSocketMessage[] = [];
  private isManualClose = false;
  private lastMessageTime = Date.now();
  private connectionStartTime = Date.now();

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 2000,
      heartbeatInterval: 30000,
      messageBufferSize: 100,
      ...config
    };
  }

  connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    this.isManualClose = false;
    this.connectionStartTime = Date.now();

    try {
      this.ws = new WebSocket(`${this.config.url}/${this.config.taskId}`);
      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    }
  }

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectCount = 0;
      this.startHeartbeat();
      this.flushMessageBuffer();
      this.config.callbacks.onOpen?.();
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        message.timestamp = Date.now();
        
        this.lastMessageTime = message.timestamp;
        this.handleMessage(message);
        this.addToBuffer(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.config.callbacks.onError?.(error);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      this.stopHeartbeat();
      
      if (!this.isManualClose && event.code !== 1000) {
        this.handleReconnect();
      }
      
      this.config.callbacks.onClose?.();
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    // 根据消息类型分发
    switch (message.type) {
      case 'progress':
        this.config.callbacks.onProgress?.(message.data);
        break;
      case 'data':
        this.config.callbacks.onData?.(message.data);
        break;
      case 'completed':
        this.config.callbacks.onCompleted?.(message.data);
        break;
      case 'error':
        this.config.callbacks.onError?.(message.data);
        break;
      case 'heartbeat':
        // 心跳响应，不需要特殊处理
        break;
      default:
        this.config.callbacks.onMessage?.(message);
    }
  }

  private addToBuffer(message: WebSocketMessage): void {
    this.messageBuffer.push(message);
    
    // 限制缓冲区大小
    if (this.messageBuffer.length > this.config.messageBufferSize!) {
      this.messageBuffer = this.messageBuffer.slice(-this.config.messageBufferSize!);
    }
  }

  private flushMessageBuffer(): void {
    // 如果有未处理的消息，重新处理
    const unprocessedMessages = this.messageBuffer.filter(
      msg => msg.timestamp > this.connectionStartTime
    );

    unprocessedMessages.forEach(msg => {
      this.handleMessage(msg);
    });
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        // 检查是否长时间没有收到消息
        const timeSinceLastMessage = Date.now() - this.lastMessageTime;
        if (timeSinceLastMessage > this.config.heartbeatInterval! * 2) {
          console.warn('No messages received for a while, reconnecting...');
          this.reconnect();
          return;
        }

        // 发送心跳
        this.send({ type: 'heartbeat', data: { timestamp: Date.now() } });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private handleReconnect(): void {
    if (this.reconnectCount >= this.config.reconnectAttempts!) {
      console.error('Max reconnection attempts reached');
      this.config.callbacks.onError?.({
        message: '无法连接到服务器，请检查网络连接',
        type: 'connection_failed'
      });
      return;
    }

    this.reconnectCount++;
    const delay = this.config.reconnectDelay! * Math.pow(1.5, this.reconnectCount - 1);
    
    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectCount}/${this.config.reconnectAttempts})`);
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect();
    }, delay);
  }

  send(data: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket is not connected, message not sent:', data);
    }
  }

  reconnect(): void {
    this.close();
    this.connect();
  }

  close(): void {
    this.isManualClose = true;
    this.stopHeartbeat();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.ws) {
      this.ws.close(1000, 'Manual close');
      this.ws = null;
    }
  }

  getState(): {
    connected: boolean;
    reconnectCount: number;
    messageCount: number;
    lastMessageTime: number;
  } {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      reconnectCount: this.reconnectCount,
      messageCount: this.messageBuffer.length,
      lastMessageTime: this.lastMessageTime
    };
  }

  getMessageBuffer(): WebSocketMessage[] {
    return [...this.messageBuffer];
  }

  clearMessageBuffer(): void {
    this.messageBuffer = [];
  }
}

export default WebSocketManager;
export type { WebSocketMessage, WebSocketCallbacks, WebSocketConfig };