import React, { useState, useEffect, useCallback } from 'react'
import Header from './components/Header'
import SearchConfig from './components/SearchConfig'
import CrawlControl from './components/CrawlControl'
import ProgressMonitor from './components/ProgressMonitor.jsx'
import DataPreviewEnhanced from './components/DataPreview'
import CookieManager from './components/CookieManager'
import ToastContainer, { showToast } from './components/Toast'
import HelpModal from './components/HelpModal'
import ConfirmDialog from './components/ConfirmDialog'
import Tooltip from './components/Tooltip'
import LogViewer from './components/LogViewer'
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts'
import './App.css'
import apiClient from './services/apiClient'
import logger from './utils/logger'

function App() {
  const [theme, setTheme] = useState('light')
  const [crawlConfig, setCrawlConfig] = useState({
    keywords: '',  // 支持多关键词
    maxPages: 5,
    targetCount: 100,
    sortType: 'default',
    filters: {},  // 高级筛选选项
    headless: true,  // 默认开启无头模式
    enableFilter: false  // 默认关闭精确筛选
  })
  const [crawlStatus, setCrawlStatus] = useState('idle') // idle, running, paused, completed
  const [progress, setProgress] = useState({
    currentPage: 0,
    totalPages: 0,
    itemsCollected: 0,
    targetItems: 0,
    startTime: null
  })
  const [previewData, setPreviewData] = useState([])
  const [error, setError] = useState(null)
  const [apiHealth, setApiHealth] = useState(false)
  const [cookieValid, setCookieValid] = useState(null) // null = loading, true/false = loaded
  const [showCookieManager, setShowCookieManager] = useState(false)
  const [showHelpModal, setShowHelpModal] = useState(false)
  const [showLogViewer, setShowLogViewer] = useState(false)
  const [confirmDialog, setConfirmDialog] = useState(null)

  // 键盘快捷键设置
  const shortcuts = [
    {
      keys: { ctrl: true, key: 's' },
      handler: () => {
        if (crawlStatus === 'idle' && apiHealth && cookieValid) {
          handleStartCrawl()
        }
      }
    },
    {
      keys: { ctrl: true, shift: true, key: 's' },
      handler: () => {
        if (crawlStatus === 'running') {
          handleStopCrawl()
        }
      }
    },
    {
      keys: { ctrl: true, key: 'e' },
      handler: () => {
        if (previewData.length > 0) {
          handleExportData()
        }
      }
    },
    {
      keys: { ctrl: true, key: 'm' },
      handler: () => setShowCookieManager(true)
    },
    {
      keys: { ctrl: true, shift: true, key: 't' },
      handler: () => setTheme(prev => prev === 'light' ? 'dark' : 'light')
    },
    {
      keys: { key: '?' },
      handler: () => setShowHelpModal(true)
    },
    {
      keys: { ctrl: true, key: 'l' },
      handler: () => setShowLogViewer(true)
    },
    {
      keys: { key: 'Escape' },
      handler: () => {
        setShowCookieManager(false)
        setShowHelpModal(false)
        setShowLogViewer(false)
        setConfirmDialog(null)
      }
    }
  ]
  
  useKeyboardShortcuts(shortcuts)

  // 检查API健康状态和Cookie状态
  useEffect(() => {
    const checkHealth = async () => {
      const healthy = await apiClient.checkHealth()
      setApiHealth(healthy)
      if (!healthy) {
        logger.error('App', '后端API服务未启动')
        setError('后端API服务未启动，请先运行启动脚本')
      } else if (healthy && error === '后端API服务未启动，请先运行启动脚本') {
        logger.success('App', 'API服务连接成功')
        setError(null)
        showToast('API服务连接成功', 'success')
      }
    }
    
    const checkCookie = async () => {
      try {
        const result = await apiClient.checkCookieStatus()
        logger.info('App', 'Cookie状态检查结果', result)
        setCookieValid(result.valid)
        if (result.valid) {
          logger.info('App', 'Cookie有效', { expiresAt: result.expiresAt })
        } else {
          logger.warn('App', 'Cookie无效')
        }
      } catch (error) {
        logger.error('App', 'Cookie状态检查失败', { error: error.message })
        setCookieValid(false)
      }
    }
    
    checkHealth()
    checkCookie()
    const interval = setInterval(() => {
      checkHealth()
      checkCookie()
    }, 30000) // 每30秒检查一次
    
    return () => clearInterval(interval)
  }, [])

  // 主题切换
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme)
  }, [theme])

  // 清理函数
  useEffect(() => {
    return () => {
      // 组件卸载时清理
      if (window.previewIntervalId) {
        clearInterval(window.previewIntervalId)
      }
      apiClient.disconnectWebSocket()
    }
  }, [])

  // 配置更新处理
  const handleConfigChange = (config) => {
    setCrawlConfig(prev => ({
      ...prev,
      ...config
    }))
  }

  // 排序方式更新
  const handleSortChange = (sortType) => {
    setCrawlConfig(prev => ({
      ...prev,
      sortType
    }))
  }

  // 开始爬取
  const handleStartCrawl = async () => {
    console.log('Starting crawl, cookieValid:', cookieValid, 'apiHealth:', apiHealth)
    
    if (!crawlConfig.keywords) {
      setError('请输入搜索关键词')
      return
    }
    
    if (!apiHealth) {
      setError('后端API服务未启动，请先运行启动脚本')
      return
    }
    
    if (!cookieValid) {
      setError('请先配置有效的Cookie')
      setShowCookieManager(true)
      return
    }
    
    try {
      setError(null)
      setCrawlStatus('running')
      setPreviewData([])
      setProgress({
        currentPage: 0,
        totalPages: crawlConfig.maxPages,
        itemsCollected: 0,
        targetItems: crawlConfig.targetCount,
        startTime: Date.now()
      })
      
      // 调用真实API启动爬虫
      const result = await apiClient.startCrawl(crawlConfig)
      
      if (result.success) {
        console.log('爬虫启动成功:', result.taskId)
        showToast('爬虫已启动', 'success')
        
        // 连接WebSocket获取实时进度
        apiClient.connectWebSocket({
          onProgress: (progressData) => {
            setProgress(prevProgress => ({
              ...prevProgress,
              currentPage: Math.ceil((progressData.current / 20)), // 估算页数
              totalPages: crawlConfig.maxPages,
              itemsCollected: progressData.current,
              targetItems: progressData.total
            }))
          },
          onData: (newData) => {
            // 实时更新预览数据
            console.log('WebSocket onData 接收到新数据:', newData)
            logger.info('App', '接收到商品数据', { 
              count: newData?.length || 0,
              firstItem: newData?.[0]
            })
            
            if (newData && Array.isArray(newData) && newData.length > 0) {
              setPreviewData(prev => {
                console.log('当前预览数据数量:', prev.length)
                // 使用goods_id作为唯一标识符
                const existingIds = new Set(prev.map(item => item.goods_id || item.id))
                const uniqueNewData = newData.filter(item => 
                  item && (item.goods_id || item.id) && !existingIds.has(item.goods_id || item.id)
                )
                const combined = [...prev, ...uniqueNewData]
                logger.info('App', '更新预览数据', { 
                  prev: prev.length, 
                  new: uniqueNewData.length, 
                  total: combined.length 
                })
                console.log('更新后预览数据数量:', combined.length)
                // 保留最新的500条以提供更好的预览体验
                return combined.slice(-500)
              })
            } else {
              console.log('WebSocket onData 接收到的数据格式不正确:', newData)
            }
          },
          onCompleted: (result) => {
            setCrawlStatus('completed')
            setError(null)
            console.log('爬取完成:', result)
            showToast('爬取完成', 'success')
            // 清理定时器
            if (window.previewIntervalId) {
              clearInterval(window.previewIntervalId)
            }
            // 1秒后自动重置为idle状态，允许新的爬取
            setTimeout(() => {
              setCrawlStatus('idle')
            }, 1000)
          },
          onError: (error) => {
            setError(error.message || '爬取过程中出现错误')
            setCrawlStatus('idle')
          }
        })
        
        // 定期获取预览数据和进度（作为WebSocket的补充）
        const intervalId = setInterval(async () => {
          // 使用apiClient.taskId检查，而不是crawlStatus
          if (apiClient.taskId) {
            try {
              const statusResult = await apiClient.getCrawlStatus()
              
              // 更新进度信息
              if (statusResult.progress) {
                setProgress(prevProgress => ({
                  ...prevProgress,
                  itemsCollected: statusResult.progress.current || 0,
                  targetItems: statusResult.progress.total || 0,
                  currentPage: statusResult.currentPage || 1,
                  totalPages: statusResult.totalPages || 1,
                  currentKeyword: statusResult.currentKeyword || crawlConfig.keywords
                }))
              }
              
              // 更新状态
              if (statusResult.status !== crawlStatus) {
                setCrawlStatus(statusResult.status)
                
                // 如果完成或出错，清理定时器
                if (statusResult.status === 'completed' || statusResult.status === 'error') {
                  clearInterval(intervalId)
                  if (statusResult.status === 'completed') {
                    showToast('爬取完成！', 'success')
                    // 1秒后自动重置为idle状态
                    setTimeout(() => {
                      setCrawlStatus('idle')
                    }, 1000)
                  }
                }
              }
              
              // 只在运行状态下获取预览数据
              // 注释掉定期获取，依赖WebSocket实时更新
              // if (statusResult.status === 'running') {
              //   try {
              //     const previewResult = await apiClient.getPreviewData(50)
              //     if (previewResult.data && previewResult.data.length > 0) {
              //       setPreviewData(previewResult.data)
              //     }
              //   } catch (previewError) {
              //     console.error('获取预览数据失败:', previewError)
              //   }
              // }
            } catch (error) {
              console.error('获取状态失败:', error)
              // 不要因为单次失败就停止轮询
            }
          }
        }, 2000) // 每2秒更新一次，更频繁地获取进度
        
        // 保存定时器ID以便清理
        window.previewIntervalId = intervalId
      } else {
        throw new Error(result.message || '启动爬虫失败')
      }
    } catch (error) {
      setError(error.message)
      setCrawlStatus('idle')
    }
  }

  // 暂停爬取
  const handlePauseCrawl = async () => {
    try {
      const result = await apiClient.pauseCrawl()
      if (result.success) {
        setCrawlStatus('paused')
      }
    } catch (error) {
      setError(error.message)
    }
  }

  // 恢复爬取
  const handleResumeCrawl = async () => {
    try {
      const result = await apiClient.resumeCrawl()
      if (result.success) {
        setCrawlStatus('running')
      }
    } catch (error) {
      setError(error.message)
    }
  }

  // 停止爬取
  const handleStopCrawl = async () => {
    try {
      const result = await apiClient.stopCrawl()
      if (result.success) {
        setCrawlStatus('idle')
        setProgress({
          currentPage: 0,
          totalPages: 0,
          itemsCollected: 0,
          targetItems: 0,
          startTime: null
        })
        // 清理定时器
        if (window.previewIntervalId) {
          clearInterval(window.previewIntervalId)
        }
        apiClient.disconnectWebSocket()
        showToast('爬取已停止', 'info')
      }
    } catch (error) {
      setError(error.message)
    }
  }

  // 导出数据
  const handleExportData = useCallback(async (format = 'xlsx') => {
    setConfirmDialog({
      title: '导出数据',
      message: `确认要导出 ${previewData.length} 条数据为 ${format.toUpperCase()} 格式吗？`,
      type: 'info',
      onConfirm: async () => {
        setConfirmDialog(null)
        try {
          setError(null)
          showToast(`正在导出${format.toUpperCase()}数据...`, 'info')
          const result = await apiClient.exportData(format)
          
          if (result.success) {
            showToast(`数据导出成功`, 'success')
          } else {
            throw new Error(result.message || '导出失败')
          }
        } catch (error) {
          setError(error.message)
          showToast(error.message, 'error')
        }
      },
      onCancel: () => setConfirmDialog(null)
    })
  }, [previewData.length])
  
  // CSV导出处理函数
  const handleExportCSV = useCallback(() => {
    handleExportData('csv')
  }, [handleExportData])

  return (
    <div className={`app ${theme}`}>
      <Header theme={theme} onThemeToggle={() => setTheme(theme === 'light' ? 'dark' : 'light')} />
      
      <main className="main-content">
        <div className="container">
          {/* API状态提示 */}
          {!apiHealth && (
            <div className="api-warning">
              <p>⚠️ 后端API服务未启动</p>
              <p>请运行 <code>python start.py</code> 启动服务</p>
            </div>
          )}
          
          {/* Cookie状态提示 */}
          {apiHealth && cookieValid === false && (
            <div className="cookie-warning">
              <p>⚠️ 未配置有效的Cookie</p>
              <Tooltip content="快捷键: Ctrl+M">
                <button onClick={() => setShowCookieManager(true)}>配置Cookie</button>
              </Tooltip>
            </div>
          )}
          
          {/* 错误提示 */}
          {error && (
            <div className="error-message">
              <p>❌ {error}</p>
              <button onClick={() => setError(null)}>关闭</button>
            </div>
          )}
          
          {/* 搜索配置 */}
          <section className="config-section">
            <SearchConfig 
              config={crawlConfig}
              onChange={handleConfigChange}
              disabled={crawlStatus !== 'idle'}
            />
          </section>
          
          {/* 控制按钮 */}
          <section className="control-section">
            <CrawlControl 
              status={crawlStatus}
              onStart={handleStartCrawl}
              onPause={handlePauseCrawl}
              onResume={handleResumeCrawl}
              onStop={handleStopCrawl}
              onExport={handleExportData}
              onExportCSV={handleExportCSV}
              hasData={previewData.length > 0}
              disabled={!apiHealth}
            />
          </section>
          
          {/* 进度显示 */}
          {crawlStatus !== 'idle' && (
            <section className="progress-section">
              <ProgressMonitor 
                progress={progress}
                status={crawlStatus}
                onError={(error) => setError(error)}
              />
            </section>
          )}
          
          {/* 数据预览 */}
          {previewData.length > 0 && (
            <section className="preview-section">
              <DataPreviewEnhanced 
                data={previewData}
                loading={crawlStatus === 'running'}
                onExport={handleExportData}
                keywords={crawlConfig.keywords || crawlConfig.keyword}
              />
            </section>
          )}
          
          {/* 日志面板 */}
          <section className="log-section">
            <div className="section-header">
              <h3>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" style={{ marginRight: '8px' }}>
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                </svg>
                系统日志
              </h3>
              <button 
                className="btn btn-sm btn-ghost"
                onClick={() => setShowLogViewer(true)}
                style={{ padding: '4px 12px', fontSize: '14px' }}
              >
                <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                </svg>
                全屏查看
              </button>
            </div>
            <LogViewer inline={true} isOpen={true} height="350px" />
          </section>
          
          {/* 使用说明 */}
          <section className="help-section">
            <div className="help-section-header">
              <h3>使用说明</h3>
              <div style={{ display: 'flex', gap: '8px' }}>
                <Tooltip content="查看系统日志 (Ctrl+L)">
                  <button 
                    className="btn btn-ghost"
                    onClick={() => setShowLogViewer(true)}
                    style={{ padding: '4px 12px', fontSize: '14px' }}
                  >
                    📋 日志
                  </button>
                </Tooltip>
                <Tooltip content="查看键盘快捷键 (?)">
                  <button 
                    className="btn btn-ghost"
                    onClick={() => setShowHelpModal(true)}
                    style={{ padding: '4px 12px', fontSize: '14px' }}
                  >
                    ⌨️ 快捷键
                  </button>
                </Tooltip>
              </div>
            </div>
            <ol>
              <li>首次使用请点击"配置Cookie"按钮设置有效的Cookie</li>
              <li>输入搜索关键词（支持多个关键词，用逗号分隔）</li>
              <li>设置目标商品数量</li>
              <li>选择排序方式（可选）</li>
              <li>点击"开始爬取"按钮</li>
              <li>等待爬取完成后，点击"导出数据"下载Excel文件</li>
            </ol>
            <div className="help-actions">
              <Tooltip content="快捷键: Ctrl+M">
                <button 
                  className="cookie-btn"
                  onClick={() => setShowCookieManager(true)}
                  disabled={!apiHealth}
                >
                  {cookieValid ? '更新Cookie' : '配置Cookie'}
                </button>
              </Tooltip>
            </div>
          </section>
        </div>
      </main>
      
      {/* Cookie管理弹窗 */}
      {showCookieManager && (
        <div className="modal-overlay" onClick={() => setShowCookieManager(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <button 
              className="modal-close"
              onClick={() => setShowCookieManager(false)}
            >
              ×
            </button>
            <CookieManager 
              onCookieSave={async () => {
                // 在保存成功后才关闭弹窗
                try {
                  const result = await apiClient.checkCookieStatus()
                  logger.info('App', 'Cookie保存后状态', result)
                  setCookieValid(result.valid)
                  if (result.valid) {
                    setShowCookieManager(false)
                    setError(null)
                    showToast('Cookie保存成功', 'success')
                  }
                } catch (error) {
                  logger.error('App', 'Cookie状态更新失败', { error: error.message })
                }
              }}
              onCookieUpdate={(cookies) => {
                // Cookie更新时不自动关闭弹窗，只更新状态
                logger.info('App', 'Cookie已更新', { count: cookies.length })
              }}
            />
          </div>
        </div>
      )}
      
      {/* 帮助弹窗 */}
      <HelpModal 
        isOpen={showHelpModal}
        onClose={() => setShowHelpModal(false)}
      />
      
      {/* 日志查看器 */}
      <LogViewer 
        isOpen={showLogViewer}
        onClose={() => setShowLogViewer(false)}
      />
      
      {/* 确认对话框 */}
      {confirmDialog && (
        <ConfirmDialog 
          isOpen={true}
          {...confirmDialog}
        />
      )}
      
      {/* Toast 通知 */}
      <ToastContainer />
    </div>
  )
}

export default App