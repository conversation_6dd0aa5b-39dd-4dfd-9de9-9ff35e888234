.app {
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color var(--duration-300) var(--ease-out),
              color var(--duration-300) var(--ease-out);
}

.main-content {
  padding: var(--spacing-6) 0;
  animation: fadeIn var(--duration-500) var(--ease-out);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-5);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-10);
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-5);
}

.display-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-5);
}

/* Section样式 */
section {
  margin-bottom: var(--spacing-8);
  animation: slideInUp var(--duration-300) var(--ease-out);
  animation-fill-mode: both;
}

section:nth-child(1) { animation-delay: 0ms; }
section:nth-child(2) { animation-delay: 50ms; }
section:nth-child(3) { animation-delay: 100ms; }
section:nth-child(4) { animation-delay: 150ms; }
section:nth-child(5) { animation-delay: 200ms; }

.error-message {
  background-color: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInDown var(--duration-300) var(--ease-spring);
  box-shadow: var(--shadow-sm);
}

.error-message p {
  margin: 0;
  color: var(--color-error-700);
  font-weight: var(--font-weight-medium);
}

.error-message button {
  background: var(--color-error-500);
  color: white;
  border: none;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-base);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-200) var(--ease-out);
}

.error-message button:hover {
  background: var(--color-error-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.error-message button:active {
  transform: translateY(0);
}

/* API状态提示 */
.api-warning {
  background-color: var(--color-warning-50);
  border: 1px solid var(--color-warning-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-5);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  animation: slideInDown var(--duration-300) var(--ease-spring);
  box-shadow: var(--shadow-sm);
}

.api-warning p {
  margin: 0;
  color: var(--color-warning-700);
  font-weight: var(--font-weight-medium);
}

.api-warning code {
  background-color: var(--color-warning-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  color: var(--error);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.error-close:hover {
  background: rgba(244, 67, 54, 0.2);
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

/* 加载动画 */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--secondary) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    var(--secondary) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 过渡动画 */
.fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-exit {
  opacity: 1;
  transform: translateY(0);
}

.fade-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Cookie相关样式 */
.cookie-warning {
  background-color: var(--color-warning-50);
  border: 1px solid var(--color-warning-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  margin-bottom: var(--spacing-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInDown var(--duration-300) var(--ease-spring);
  box-shadow: var(--shadow-sm);
}

.cookie-warning p {
  margin: 0;
  color: var(--color-warning-700);
  font-weight: var(--font-weight-medium);
}

.cookie-warning button {
  background-color: var(--color-warning-500);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-200) var(--ease-out);
}

.cookie-warning button:hover {
  background-color: var(--color-warning-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.cookie-warning button:active {
  transform: translateY(0);
}

/* 日志部分 */
.log-section {
  margin-top: var(--spacing-6);
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  animation: slideInUp var(--duration-300) var(--ease-out);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-secondary);
}

.section-header h3 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  color: var(--text-primary);
}

.help-section {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.help-section h3 {
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.help-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.help-section-header h3 {
  margin: 0;
}

.help-section ol {
  margin: 0 0 var(--spacing-4) var(--spacing-5);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.help-section li {
  margin-bottom: var(--spacing-2);
}

.help-actions {
  margin-top: var(--spacing-4);
}

.cookie-btn {
  background-color: var(--color-info-500);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-5);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-200) var(--ease-out);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.cookie-btn:hover {
  background-color: var(--color-info-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.cookie-btn:active {
  transform: translateY(0);
}

.cookie-btn:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  animation: fadeIn var(--duration-200) var(--ease-out);
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-6);
  animation: scaleIn var(--duration-300) var(--ease-spring);
  border: 1px solid var(--border-primary);
}

.modal-close {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.modal-close:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  transform: rotate(90deg);
}

/* 深色模式特殊处理 */
[data-theme="dark"] .api-warning,
[data-theme="dark"] .cookie-warning {
  background-color: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.3);
}

[data-theme="dark"] .error-message {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top-color: var(--color-primary-500);
  border-radius: 50%;
  animation: spin var(--duration-700) linear infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-4);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .modal-content {
    width: 95%;
    padding: var(--spacing-4);
  }
}