import React from 'react'
import './SortSelectorEnhanced.css'

export default function SortSelectorEnhanced({ sortType, onChange, disabled }) {
  const sortOptions = [
    { 
      value: 'default', 
      label: '综合排序',
      description: '根据商品相关性和质量综合排序',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <rect x="3" y="3" width="14" height="2" rx="1" fill="currentColor"/>
          <rect x="3" y="8" width="10" height="2" rx="1" fill="currentColor"/>
          <rect x="3" y="13" width="7" height="2" rx="1" fill="currentColor"/>
        </svg>
      ),
      color: 'blue'
    },
    { 
      value: 'sales_desc', 
      label: '销量优先',
      description: '优先展示销量高的热门商品',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M3 17V7L7 3L11 8L15 5L17 7V17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="7" cy="3" r="1.5" fill="currentColor"/>
          <circle cx="11" cy="8" r="1.5" fill="currentColor"/>
          <circle cx="15" cy="5" r="1.5" fill="currentColor"/>
        </svg>
      ),
      color: 'green'
    },
    { 
      value: 'price_asc', 
      label: '价格从低到高',
      description: '优先展示价格较低的实惠商品',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M10 15L15 10M15 10L10 5M15 10H3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <text x="3" y="8" fontSize="10" fill="currentColor">￥</text>
        </svg>
      ),
      color: 'orange'
    },
    { 
      value: 'price_desc', 
      label: '价格从高到低',
      description: '优先展示价格较高的品质商品',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M10 5L15 10M15 10L10 15M15 10H3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <text x="3" y="12" fontSize="10" fill="currentColor">￥</text>
        </svg>
      ),
      color: 'purple'
    },
    {
      value: 'new',
      label: '新品优先',
      description: '优先展示最新上架的商品',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M10 2L12 8H18L13 12L15 18L10 14L5 18L7 12L2 8H8L10 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/>
        </svg>
      ),
      color: 'pink'
    },
    {
      value: 'rating',
      label: '评价最高',
      description: '优先展示用户评价好的商品',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" fill="currentColor"/>
        </svg>
      ),
      color: 'yellow'
    }
  ]

  const selectedOption = sortOptions.find(opt => opt.value === sortType) || sortOptions[0]

  return (
    <div className="sort-selector-enhanced">
      <div className="sort-header">
        <div className="sort-header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M3 4H16M3 8H12M3 12H9M13 12L17 8M17 8L21 12M17 8V20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <h3 className="sort-title">排序方式</h3>
      </div>

      <div className="sort-options-grid">
        {sortOptions.map(option => (
          <button
            key={option.value}
            className={`sort-option-card ${sortType === option.value ? 'active' : ''} ${option.color}`}
            onClick={() => onChange(option.value)}
            disabled={disabled}
            type="button"
          >
            <div className="option-icon">
              {option.icon}
            </div>
            <div className="option-content">
              <h4 className="option-label">{option.label}</h4>
              <p className="option-description">{option.description}</p>
            </div>
            {sortType === option.value && (
              <div className="option-check">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M13 4L6 11L3 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            )}
          </button>
        ))}
      </div>

      <div className="sort-info">
        <div className="info-icon">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <circle cx="8" cy="8" r="7" stroke="currentColor" strokeWidth="1.5" fill="none"/>
            <path d="M8 12V7M8 4V4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          </svg>
        </div>
        <p>当前选择：<strong>{selectedOption.label}</strong> - {selectedOption.description}</p>
      </div>
    </div>
  )
}