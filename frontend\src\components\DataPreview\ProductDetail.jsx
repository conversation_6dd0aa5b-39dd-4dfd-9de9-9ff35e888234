import React, { useState } from 'react'
import clsx from 'clsx'
import './ProductDetail.css'

export default function ProductDetail({ product, visible, onClose }) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [imageError, setImageError] = useState(false)

  if (!visible || !product) return null

  // 格式化价格
  const formatPrice = (price) => {
    return `¥${parseFloat(price).toFixed(2)}`
  }

  // 格式化销量
  const formatSales = (sales) => {
    if (sales >= 10000) {
      return `${(sales / 10000).toFixed(1)}万+`
    }
    if (sales >= 1000) {
      return `${(sales / 1000).toFixed(1)}k+`
    }
    return sales.toString()
  }

  // 计算折扣
  const calculateDiscount = () => {
    if (product.market_price && product.market_price > product.price) {
      const discount = ((1 - product.price / product.market_price) * 100).toFixed(0)
      return `${discount}%OFF`
    }
    return null
  }

  // 获取所有图片
  const getAllImages = () => {
    const images = []
    if (product.hd_url) images.push(product.hd_url)
    if (product.image_url && product.image_url !== product.hd_url) {
      images.push(product.image_url)
    }
    if (product.thumb_url && product.thumb_url !== product.image_url) {
      images.push(product.thumb_url)
    }
    if (product.gallery && Array.isArray(product.gallery)) {
      images.push(...product.gallery)
    }
    return images.length > 0 ? images : ['https://via.placeholder.com/400x400']
  }

  const images = getAllImages()

  return (
    <>
      <div className="product-detail-overlay" onClick={onClose} />
      <div className="product-detail-modal">
        <button className="modal-close-btn" onClick={onClose}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>

        <div className="detail-container">
          {/* 左侧图片区域 */}
          <div className="detail-images">
            <div className="main-image">
              {imageError ? (
                <div className="image-error">
                  <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                    <rect x="8" y="8" width="32" height="32" rx="4" stroke="currentColor" strokeWidth="2"/>
                    <path d="M16 32l8-8 8 8m-16-16l4-4m8 4l8-8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <p>图片加载失败</p>
                </div>
              ) : (
                <img
                  src={images[selectedImage]}
                  alt={product.goods_name}
                  onError={() => setImageError(true)}
                />
              )}
              {calculateDiscount() && (
                <div className="discount-badge">{calculateDiscount()}</div>
              )}
            </div>
            
            {images.length > 1 && (
              <div className="thumbnail-list">
                {images.map((img, index) => (
                  <div
                    key={index}
                    className={clsx('thumbnail-item', {
                      'active': selectedImage === index
                    })}
                    onClick={() => {
                      setSelectedImage(index)
                      setImageError(false)
                    }}
                  >
                    <img src={img} alt={`${product.goods_name} ${index + 1}`} />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 右侧信息区域 */}
          <div className="detail-info">
            <div className="detail-header">
              <h2 className="detail-title">{product.goods_name}</h2>
              {product.brand_name && (
                <div className="detail-brand">
                  <span className="brand-label">品牌</span>
                  <span className="brand-name">{product.brand_name}</span>
                </div>
              )}
            </div>

            <div className="detail-price-section">
              <div className="price-row">
                <span className="price-label">当前价格</span>
                <span className="current-price">{formatPrice(product.price)}</span>
                {product.market_price && product.market_price > product.price && (
                  <span className="original-price">¥{product.market_price}</span>
                )}
              </div>
              {product.coupon_price && product.coupon_price < product.price && (
                <div className="coupon-row">
                  <span className="coupon-label">券后价</span>
                  <span className="coupon-price">{formatPrice(product.coupon_price)}</span>
                  <span className="coupon-amount">
                    {formatPrice(product.price - product.coupon_price)} 优惠
                  </span>
                </div>
              )}
            </div>

            <div className="detail-stats-grid">
              <div className="stat-card">
                <div className="stat-value">{formatSales(product.sales)}</div>
                <div className="stat-label">销量</div>
              </div>
              {product.comment_count && (
                <div className="stat-card">
                  <div className="stat-value">{product.comment_count}</div>
                  <div className="stat-label">评价数</div>
                </div>
              )}
              {product.rating && (
                <div className="stat-card">
                  <div className="stat-value">{product.rating}</div>
                  <div className="stat-label">评分</div>
                </div>
              )}
              {product.views && (
                <div className="stat-card">
                  <div className="stat-value">{formatSales(product.views)}</div>
                  <div className="stat-label">浏览量</div>
                </div>
              )}
            </div>

            <div className="detail-info-list">
              <div className="info-row">
                <span className="info-label">商品ID</span>
                <span className="info-value">{product.goods_id || product.id}</span>
              </div>
              <div className="info-row">
                <span className="info-label">店铺名称</span>
                <span className="info-value">{product.shop_name}</span>
              </div>
              {product.merchant_type_name && (
                <div className="info-row">
                  <span className="info-label">店铺类型</span>
                  <span className="info-value">{product.merchant_type_name}</span>
                </div>
              )}
              {product.category && (
                <div className="info-row">
                  <span className="info-label">商品分类</span>
                  <span className="info-value">{product.category}</span>
                </div>
              )}
              {product.delivery_type && (
                <div className="info-row">
                  <span className="info-label">配送方式</span>
                  <span className="info-value">{product.delivery_type}</span>
                </div>
              )}
            </div>

            {product.tags && product.tags.length > 0 && (
              <div className="detail-tags-section">
                <h3 className="section-title">商品标签</h3>
                <div className="tags-list">
                  {product.tags.map((tag, index) => (
                    <span key={index} className="tag-badge">{tag}</span>
                  ))}
                </div>
              </div>
            )}

            {product.service_tags && product.service_tags.length > 0 && (
              <div className="detail-services">
                <h3 className="section-title">服务保障</h3>
                <div className="service-list">
                  {product.service_tags.map((service, index) => (
                    <div key={index} className="service-item">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M13.5 4.5L6 12 2.5 8.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                      </svg>
                      <span>{service}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="detail-actions">
              <button className="action-btn primary">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M3 3h14l-1 9H4L3 3zm0 0l-.5-2h18l-.5 2M7 17a1 1 0 100-2 1 1 0 000 2zm6 0a1 1 0 100-2 1 1 0 000 2z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                查看商品
              </button>
              <button className="action-btn">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M14.5 7v6m0 0l-2-2m2 2l2-2M10 3H4a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1v-6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                导出数据
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}