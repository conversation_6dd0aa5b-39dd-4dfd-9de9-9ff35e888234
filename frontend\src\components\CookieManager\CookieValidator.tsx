import React, { useCallback, useEffect, useState } from 'react';
import { Button, Space, Alert, Spin, Typography } from 'antd';
import { CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { Cookie, CookieInputFormat, CookieValidationResult } from '../../types/cookie';
import './CookieValidator.css';

const { Text } = Typography;

interface CookieValidatorProps {
  input: string;
  format: CookieInputFormat;
  onValidate: (result: CookieValidationResult) => void;
  isValidating: boolean;
  onValidatingChange: (validating: boolean) => void;
}

const CookieValidator: React.FC<CookieValidatorProps> = ({
  input,
  format,
  onValidate,
  isValidating,
  onValidatingChange
}) => {
  const [lastValidatedInput, setLastValidatedInput] = useState<string>('');
  const [validationResult, setValidationResult] = useState<CookieValidationResult | null>(null);

  // Cookie名称验证
  const isValidCookieName = (name: string): boolean => {
    // Cookie名称不能包含的字符
    const invalidChars = /[\s=;,\(\)<>@:\\"\/\[\]\?{}]/;
    return name.length > 0 && !invalidChars.test(name);
  };

  // Cookie值验证
  const isValidCookieValue = (value: string): boolean => {
    // Cookie值不能包含的字符（除非被引号包围）
    const invalidChars = /[\s;,]/;
    if (value.startsWith('"') && value.endsWith('"')) {
      return true; // 被引号包围的值可以包含空格等字符
    }
    return !invalidChars.test(value);
  };

  // 解析字符串格式的Cookie
  const parseStringFormat = (input: string): Cookie[] => {
    const cookies: Cookie[] = [];
    const errors: string[] = [];
    
    // 移除可能的 "Cookie: " 前缀
    const cleanInput = input.replace(/^Cookie:\s*/i, '');
    
    // 按分号分割
    const parts = cleanInput.split(';').map(part => part.trim()).filter(part => part);
    
    parts.forEach((part, index) => {
      const equalIndex = part.indexOf('=');
      if (equalIndex === -1) {
        errors.push(`第${index + 1}个Cookie格式错误：缺少等号`);
        return;
      }
      
      const name = part.substring(0, equalIndex).trim();
      const value = part.substring(equalIndex + 1).trim();
      
      if (!isValidCookieName(name)) {
        errors.push(`Cookie名称 "${name}" 包含非法字符`);
        return;
      }
      
      if (!isValidCookieValue(value)) {
        errors.push(`Cookie "${name}" 的值包含非法字符`);
        return;
      }
      
      cookies.push({ name, value });
    });
    
    return cookies;
  };

  // 解析JSON格式的Cookie
  const parseJSONFormat = (input: string): { cookies: Cookie[], errors: string[] } => {
    const errors: string[] = [];
    
    try {
      const parsed = JSON.parse(input);
      
      if (!Array.isArray(parsed)) {
        errors.push('JSON必须是一个数组');
        return { cookies: [], errors };
      }
      
      const cookies: Cookie[] = parsed.map((item, index) => {
        if (!item.name || typeof item.name !== 'string') {
          errors.push(`第${index + 1}个Cookie缺少有效的name字段`);
          return null;
        }
        
        if (!item.value || typeof item.value !== 'string') {
          errors.push(`第${index + 1}个Cookie缺少有效的value字段`);
          return null;
        }
        
        if (!isValidCookieName(item.name)) {
          errors.push(`Cookie名称 "${item.name}" 包含非法字符`);
          return null;
        }
        
        return {
          name: item.name,
          value: item.value,
          domain: item.domain,
          path: item.path,
          expires: item.expires,
          secure: item.secure,
          httpOnly: item.httpOnly,
          sameSite: item.sameSite
        };
      }).filter(Boolean) as Cookie[];
      
      return { cookies, errors };
    } catch (error) {
      errors.push('JSON格式错误：' + (error as Error).message);
      return { cookies: [], errors };
    }
  };

  // 解析浏览器格式的Cookie
  const parseBrowserFormat = (input: string): Cookie[] => {
    // 浏览器格式通常包含额外的属性，如 path、domain 等
    const cookies: Cookie[] = [];
    const mainParts = input.split(';').map(part => part.trim()).filter(part => part);
    
    let currentCookie: Partial<Cookie> | null = null;
    
    mainParts.forEach(part => {
      const equalIndex = part.indexOf('=');
      
      if (equalIndex === -1) {
        // 可能是属性（如 Secure、HttpOnly）
        const lowerPart = part.toLowerCase();
        if (currentCookie) {
          if (lowerPart === 'secure') currentCookie.secure = true;
          if (lowerPart === 'httponly') currentCookie.httpOnly = true;
        }
      } else {
        const key = part.substring(0, equalIndex).trim();
        const value = part.substring(equalIndex + 1).trim();
        const lowerKey = key.toLowerCase();
        
        if (lowerKey === 'path' && currentCookie) {
          currentCookie.path = value;
        } else if (lowerKey === 'domain' && currentCookie) {
          currentCookie.domain = value;
        } else if (lowerKey === 'expires' && currentCookie) {
          currentCookie.expires = value;
        } else if (lowerKey === 'samesite' && currentCookie) {
          currentCookie.sameSite = value as 'Strict' | 'Lax' | 'None';
        } else {
          // 这是一个新的Cookie
          if (currentCookie && currentCookie.name && currentCookie.value) {
            cookies.push(currentCookie as Cookie);
          }
          currentCookie = { name: key, value };
        }
      }
    });
    
    // 添加最后一个Cookie
    if (currentCookie && currentCookie.name && currentCookie.value) {
      cookies.push(currentCookie as Cookie);
    }
    
    return cookies;
  };

  const validateCookies = useCallback(() => {
    if (!input.trim()) {
      setValidationResult(null);
      return;
    }

    onValidatingChange(true);
    
    // 模拟异步验证
    setTimeout(() => {
      const errors: string[] = [];
      const warnings: string[] = [];
      let parsedCookies: Cookie[] = [];
      
      try {
        switch (format) {
          case 'string':
          case 'header':
            parsedCookies = parseStringFormat(input);
            break;
          case 'json':
            const jsonResult = parseJSONFormat(input);
            parsedCookies = jsonResult.cookies;
            errors.push(...jsonResult.errors);
            break;
          case 'browser':
            parsedCookies = parseBrowserFormat(input);
            break;
        }
        
        // 检查重复的Cookie名称
        const nameCount: Record<string, number> = {};
        parsedCookies.forEach(cookie => {
          nameCount[cookie.name] = (nameCount[cookie.name] || 0) + 1;
        });
        
        Object.entries(nameCount).forEach(([name, count]) => {
          if (count > 1) {
            warnings.push(`Cookie "${name}" 出现了 ${count} 次`);
          }
        });
        
        // 检查Cookie过期时间
        parsedCookies.forEach(cookie => {
          if (cookie.expires) {
            const expiryDate = new Date(cookie.expires);
            if (expiryDate < new Date()) {
              warnings.push(`Cookie "${cookie.name}" 已过期`);
            }
          }
        });
        
        // 检查必要的Cookie
        const requiredCookies = ['PDDAccessToken', 'api_uid']; // 拼多多必需的Cookie
        const foundCookies = new Set(parsedCookies.map(c => c.name));
        requiredCookies.forEach(required => {
          if (!foundCookies.has(required)) {
            warnings.push(`缺少推荐的Cookie: ${required}`);
          }
        });
        
      } catch (error) {
        errors.push('解析错误：' + (error as Error).message);
      }
      
      const result: CookieValidationResult = {
        isValid: errors.length === 0 && parsedCookies.length > 0,
        errors,
        warnings,
        parsedCookies
      };
      
      setValidationResult(result);
      setLastValidatedInput(input);
      onValidate(result);
      onValidatingChange(false);
    }, 500);
  }, [input, format, onValidate, onValidatingChange]);

  // 当输入改变时自动验证
  useEffect(() => {
    if (input !== lastValidatedInput && input.trim()) {
      const timer = setTimeout(() => {
        validateCookies();
      }, 1000); // 防抖，1秒后自动验证
      
      return () => clearTimeout(timer);
    }
  }, [input, lastValidatedInput, validateCookies]);

  return (
    <div className="cookie-validator">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div className="cookie-validator-header">
          <Button
            type="primary"
            icon={isValidating ? <LoadingOutlined /> : <CheckCircleOutlined />}
            onClick={validateCookies}
            disabled={!input.trim() || isValidating}
          >
            {isValidating ? '验证中...' : '验证Cookie'}
          </Button>
          
          {validationResult && (
            <Text type={validationResult.isValid ? 'success' : 'danger'}>
              {validationResult.isValid 
                ? `验证成功，解析出 ${validationResult.parsedCookies.length} 个Cookie`
                : '验证失败，请检查输入格式'
              }
            </Text>
          )}
        </div>
        
        {validationResult && validationResult.errors.length > 0 && (
          <Alert
            type="error"
            message="验证错误"
            description={
              <ul className="cookie-validator-list">
                {validationResult.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            }
            showIcon
          />
        )}
        
        {validationResult && validationResult.warnings.length > 0 && (
          <Alert
            type="warning"
            message="验证警告"
            description={
              <ul className="cookie-validator-list">
                {validationResult.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            }
            showIcon
          />
        )}
      </Space>
    </div>
  );
};

export default CookieValidator;