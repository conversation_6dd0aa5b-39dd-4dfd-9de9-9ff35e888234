.cookie-display {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.cookie-display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cookie-display .ant-table {
  background-color: white;
}

.cookie-display .cookie-value {
  font-family: '<PERSON><PERSON><PERSON>', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
}

.cookie-display-footer {
  margin-top: 16px;
  text-align: center;
}

.cookie-display .ant-tag {
  margin-right: 4px;
}

.cookie-display .ant-btn-text {
  padding: 0 4px;
  height: 22px;
}

@media (max-width: 768px) {
  .cookie-display-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .cookie-display .cookie-value {
    max-width: 150px;
  }
}