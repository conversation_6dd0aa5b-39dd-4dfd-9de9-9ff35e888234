#!/usr/bin/env python3
"""
修复百亿补贴识别逻辑
问题：数据处理器的补贴识别逻辑没有检查iconIds字段
解决：增加基于iconIds的补贴识别
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def fix_subsidy_detection():
    """修复百亿补贴识别逻辑"""
    
    processor_file = Path("src/data/processor.py")
    
    print("=== 修复百亿补贴识别逻辑 ===")
    print("当前问题：补贴识别只检查文本内容，忽略了iconIds字段")
    print("修复方案：在_process_subsidy_fields方法中增加iconIds检查")
    
    # 读取文件内容
    content = processor_file.read_text(encoding='utf-8')
    
    # 查找需要修改的代码位置
    old_code = """        else:
            # API监听器未处理，使用备用判断逻辑
            price_type = formatted_item.get("price_type", 0)
            activity_type_name = formatted_item.get("activity_type_name", "")
            goods_name = formatted_item.get("goods_name", "")
            price_type_name = formatted_item.get("price_type_name", "")

            # 判断是否为百亿补贴
            # 🎯 修复：移除基于错误映射的判断逻辑
            # price_type=3 不是百亿补贴，只通过文本内容判断
            is_subsidy = (
                "百亿补贴" in activity_type_name or
                "百亿补贴" in price_type_name or
                "百亿补贴" in goods_name
            )"""
    
    new_code = """        else:
            # API监听器未处理，使用备用判断逻辑
            price_type = formatted_item.get("price_type", 0)
            activity_type_name = formatted_item.get("activity_type_name", "")
            goods_name = formatted_item.get("goods_name", "")
            price_type_name = formatted_item.get("price_type_name", "")
            
            # 🎯 修复：检查iconIds字段中是否包含20001（百亿补贴标识）
            icon_ids = formatted_item.get("iconIds", [])
            if isinstance(icon_ids, str):
                # 如果是字符串格式，尝试解析
                try:
                    import json
                    icon_ids = json.loads(icon_ids)
                except:
                    icon_ids = []
            
            # 判断是否为百亿补贴
            # 优先检查iconIds，这是最准确的方法
            is_subsidy = (
                20001 in icon_ids or  # iconId=20001 是百亿补贴的标准标识
                "百亿补贴" in activity_type_name or
                "百亿补贴" in price_type_name or
                "百亿补贴" in goods_name
            )"""
    
    if old_code in content:
        # 替换代码
        new_content = content.replace(old_code, new_code)
        
        # 备份原文件
        backup_file = processor_file.with_suffix('.py.bak2')
        import shutil
        shutil.copy(processor_file, backup_file)
        print(f"\n✅ 已备份原文件到: {backup_file}")
        
        # 写入新内容
        processor_file.write_text(new_content, encoding='utf-8')
        print("✅ 补贴识别逻辑已修复")
        print("\n修复内容：")
        print("  - 增加了iconIds字段检查")
        print("  - iconId=20001 表示百亿补贴")
        print("  - 保留了原有的文本匹配作为备用")
        
        return True
    else:
        print("\n❌ 未找到需要修改的代码，可能代码结构已变化")
        print("请手动检查 _process_subsidy_fields 方法")
        return False

if __name__ == "__main__":
    if fix_subsidy_detection():
        print("\n下一步：")
        print("1. 重新运行爬虫测试")
        print("2. 检查CSV/Excel中的百亿补贴字段是否有数据")
    else:
        print("\n请联系开发者手动修复")