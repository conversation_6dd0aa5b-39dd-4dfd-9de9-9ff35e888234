"""
排序功能管理器模块
负责处理拼多多搜索结果的排序操作
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from loguru import logger

from src.utils.helpers import load_config


class SortManager:
    """排序功能管理器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化排序管理器"""
        self.config = load_config(config_path)
        self.sorting_config = self.config.get("sorting", {})
        
        # 排序配置
        self.enabled = self.sorting_config.get("enabled", True)
        self.sort_types = self.sorting_config.get("types", [])
        self.timeout = self.sorting_config.get("timeout", 10)
        self.retry_on_fail = self.sorting_config.get("retry_on_fail", False)
        
        logger.info("排序管理器初始化完成")
    
    async def apply_sort(self, page: Page, sort_type: str) -> bool:
        """
        应用指定排序
        
        Args:
            page: Playwright页面对象
            sort_type: 排序类型值
            
        Returns:
            bool: 是否成功应用排序
        """
        if not self.enabled:
            logger.info("排序功能已禁用")
            return True
        
        # 查找排序配置
        sort_config = None
        for sort_item in self.sort_types:
            if sort_item.get("value") == sort_type:
                sort_config = sort_item
                break
        
        if not sort_config:
            logger.warning(f"未找到排序类型配置: {sort_type}")
            return False
        
        try:
            logger.info(f"开始应用排序: {sort_config.get('name', sort_type)}")
            
            # 等待页面加载完成
            await page.wait_for_load_state("networkidle", timeout=5000)
            
            # 查找并点击排序按钮
            success = await self._click_sort_button(page, sort_config)
            
            if success:
                # 等待排序完成
                await self._wait_for_sort_complete(page)
                logger.info(f"排序应用成功: {sort_config.get('name')}")
                return True
            else:
                logger.warning(f"排序应用失败: {sort_config.get('name')}")
                return False
                
        except Exception as e:
            logger.error(f"应用排序时出错: {e}")
            return False
    
    async def _click_sort_button(self, page: Page, sort_config: Dict) -> bool:
        """
        点击排序按钮
        
        Args:
            page: 页面对象
            sort_config: 排序配置
            
        Returns:
            bool: 是否成功点击
        """
        try:
            selector = sort_config.get("selector", "")
            sub_selector = sort_config.get("sub_selector", "")
            
            if not selector:
                logger.error("排序选择器为空")
                return False
            
            # 等待主排序按钮出现
            try:
                await page.wait_for_selector(selector, timeout=self.timeout * 1000)
            except PlaywrightTimeoutError:
                logger.warning(f"排序按钮未找到: {selector}")
                return False
            
            # 点击主排序按钮
            await page.click(selector)
            logger.debug(f"已点击主排序按钮: {selector}")
            
            # 如果有子选择器（处理二级菜单）
            if sub_selector:
                await asyncio.sleep(0.5)  # 等待菜单展开
                
                try:
                    await page.wait_for_selector(sub_selector, timeout=3000)
                    await page.click(sub_selector)
                    logger.debug(f"已点击子排序按钮: {sub_selector}")
                except PlaywrightTimeoutError:
                    logger.warning(f"子排序按钮未找到: {sub_selector}")
                    # 子按钮失败不算失败，可能是单级菜单
            
            return True
            
        except Exception as e:
            logger.error(f"点击排序按钮时出错: {e}")
            return False
    
    async def _wait_for_sort_complete(self, page: Page) -> bool:
        """
        等待排序完成
        
        Args:
            page: 页面对象
            
        Returns:
            bool: 是否排序完成
        """
        try:
            # 等待页面重新加载
            await asyncio.sleep(2)
            
            # 等待网络空闲，表示排序数据已加载
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            logger.debug("排序完成，页面已更新")
            return True
            
        except PlaywrightTimeoutError:
            logger.warning("等待排序完成超时")
            return False
        except Exception as e:
            logger.error(f"等待排序完成时出错: {e}")
            return False
    
    async def get_available_sorts(self, page: Page) -> List[str]:
        """
        获取页面可用的排序选项
        
        Args:
            page: 页面对象
            
        Returns:
            List[str]: 可用的排序类型值列表
        """
        available_sorts = []
        
        for sort_config in self.sort_types:
            selector = sort_config.get("selector", "")
            sort_value = sort_config.get("value", "")
            
            if not selector or not sort_value:
                continue
            
            try:
                # 检查排序按钮是否存在
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    available_sorts.append(sort_value)
                    logger.debug(f"发现可用排序: {sort_config.get('name')}")
            except Exception as e:
                logger.debug(f"检查排序选项时出错: {e}")
                continue
        
        logger.info(f"发现 {len(available_sorts)} 个可用排序选项")
        return available_sorts
    
    def get_default_sort(self) -> Optional[str]:
        """
        获取默认排序类型
        
        Returns:
            Optional[str]: 默认排序类型值
        """
        for sort_config in self.sort_types:
            if sort_config.get("default", False):
                return sort_config.get("value")
        
        # 如果没有设置默认值，返回第一个
        if self.sort_types:
            return self.sort_types[0].get("value")
        
        return None
    
    def get_sort_name(self, sort_value: str) -> str:
        """
        根据排序值获取排序名称
        
        Args:
            sort_value: 排序类型值
            
        Returns:
            str: 排序名称
        """
        for sort_config in self.sort_types:
            if sort_config.get("value") == sort_value:
                return sort_config.get("name", sort_value)
        
        return sort_value
    
    def is_enabled(self) -> bool:
        """
        检查排序功能是否启用
        
        Returns:
            bool: 是否启用
        """
        return self.enabled