"""
拼多多爬虫API服务器
提供RESTful API和WebSocket服务，连接前端与爬虫系统
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from uuid import uuid4

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from loguru import logger

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 切换到项目根目录，确保配置文件路径正确
os.chdir(str(PROJECT_ROOT))

from src.main import PDDCrawler
from src.utils.helpers import load_config
from src.core.cookie_manager import CookieManager

# FastAPI应用实例
app = FastAPI(title="拼多多爬虫API", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174", "http://localhost:5175", "http://localhost:5176", "http://localhost:3000"],  # 前端开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
crawl_tasks: Dict[str, Dict[str, Any]] = {}  # 任务存储
websocket_connections: Dict[str, List[WebSocket]] = {}  # WebSocket连接
# 使用项目根目录的browser_data
cookie_manager = CookieManager(cookie_dir=str(PROJECT_ROOT / "browser_data" / "cookies"))  # Cookie管理器实例


# 请求模型
class CrawlRequest(BaseModel):
    keywords: List[str]
    targetCount: int
    sortMethod: Optional[str] = "default"
    maxPages: Optional[int] = 5
    headless: Optional[bool] = True  # 是否使用无头模式，默认为True
    enableFilter: Optional[bool] = False  # 是否启用精确商品筛选，默认为False


class CrawlControlRequest(BaseModel):
    taskId: str


class CookieRequest(BaseModel):
    cookies: List[Dict[str, Any]]


class CookieImportRequest(BaseModel):
    cookies: Optional[List[Dict[str, Any]]] = None
    cookieString: Optional[str] = None


# API路由

@app.get("/")
async def root():
    """API根路径"""
    return {"message": "拼多多爬虫API服务运行中", "version": "1.0.0"}


@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "crawler": "ready"
        }
    }


@app.post("/api/crawl/start")
async def start_crawl(request: CrawlRequest):
    """启动爬虫任务"""
    task_id = str(uuid4())
    
    try:
        # 创建爬虫实例
        crawler = PDDCrawler()
        
        # 配置爬虫参数
        crawler.keywords = request.keywords
        crawler.target_count = request.targetCount
        
        # 设置无头模式
        if hasattr(request, 'headless'):
            crawler.set_headless(request.headless)
        
        # 设置商品筛选
        if hasattr(request, 'enableFilter'):
            crawler.set_filter_enabled(request.enableFilter)
        
        # 存储任务信息
        crawl_tasks[task_id] = {
            "id": task_id,
            "crawler": crawler,
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "config": request.dict(),
            "progress": {
                "current": 0,
                "total": request.targetCount,
                "percentage": 0
            }
        }
        
        # 异步启动爬虫
        asyncio.create_task(run_crawler(task_id))
        
        return {
            "success": True,
            "taskId": task_id,
            "message": "爬虫任务已启动"
        }
        
    except Exception as e:
        logger.error(f"启动爬虫失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/crawl/{task_id}/pause")
async def pause_crawl(task_id: str):
    """暂停爬虫任务"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    if task["status"] != "running":
        raise HTTPException(status_code=400, detail="任务未在运行中")
    
    # 暂停逻辑（需要在爬虫中实现暂停功能）
    task["status"] = "paused"
    task["crawler"].is_running = False
    
    return {"success": True, "message": "任务已暂停"}


@app.post("/api/crawl/{task_id}/resume")
async def resume_crawl(task_id: str):
    """恢复爬虫任务"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    if task["status"] != "paused":
        raise HTTPException(status_code=400, detail="任务未暂停")
    
    # 恢复逻辑
    task["status"] = "running"
    task["crawler"].is_running = True
    
    return {"success": True, "message": "任务已恢复"}


@app.post("/api/crawl/{task_id}/stop")
async def stop_crawl(task_id: str):
    """停止爬虫任务"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    task["crawler"].stop_crawling()
    task["status"] = "stopped"
    
    return {"success": True, "message": "任务已停止"}


@app.get("/api/crawl/{task_id}/status")
async def get_crawl_status(task_id: str):
    """获取爬虫任务状态"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    crawler = task["crawler"]
    
    # 获取实时进度
    progress_info = crawler.get_progress()
    
    return {
        "taskId": task_id,
        "status": task["status"],
        "progress": {
            "current": progress_info["collected_count"],
            "total": crawler.target_count,
            "percentage": (progress_info["collected_count"] / crawler.target_count * 100) if crawler.target_count > 0 else 0
        },
        "startTime": task["start_time"],
        "config": task["config"]
    }


@app.get("/api/crawl/{task_id}/preview")
async def get_preview_data(task_id: str, limit: int = 20):
    """获取预览数据"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    crawler = task["crawler"]
    
    # 获取最新的数据
    preview_data = crawler.collected_data[-limit:] if crawler.collected_data else []
    
    return {
        "data": preview_data,
        "total": len(crawler.collected_data)
    }


@app.post("/api/export/{task_id}")
async def export_data(task_id: str):
    """导出数据为Excel"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    crawler = task["crawler"]
    
    if not crawler.collected_data:
        raise HTTPException(status_code=400, detail="没有可导出的数据")
    
    try:
        # 处理和导出数据
        result = await crawler._process_and_export_data()
        
        if result.get("success") and result.get("file_path"):
            task["export_file"] = result["file_path"]
            return {
                "success": True,
                "exportId": task_id,
                "fileName": Path(result["file_path"]).name,
                "fileSize": result.get("file_size_mb", 0)
            }
        else:
            raise HTTPException(status_code=500, detail="导出失败")
            
    except Exception as e:
        logger.error(f"导出数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/export/{task_id}/csv")
async def export_data_csv(task_id: str):
    """导出数据为CSV"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    crawler = task["crawler"]
    
    if not crawler.collected_data:
        raise HTTPException(status_code=400, detail="没有可导出的数据")
    
    try:
        # 处理和导出数据为CSV
        result = await crawler._process_and_export_data(format="csv")
        
        if result.get("success") and result.get("file_path"):
            task["export_file_csv"] = result["file_path"]
            return {
                "success": True,
                "exportId": task_id,
                "fileName": Path(result["file_path"]).name,
                "fileSize": result.get("file_size_mb", 0)
            }
        else:
            raise HTTPException(status_code=500, detail="导出失败")
            
    except Exception as e:
        logger.error(f"导出CSV数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/export/{task_id}/download")
async def download_export(task_id: str, format: str = "xlsx"):
    """下载导出的文件"""
    if task_id not in crawl_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = crawl_tasks[task_id]
    
    # 根据格式选择文件
    if format == "csv":
        export_file = task.get("export_file_csv")
        media_type = "text/csv"
    else:
        export_file = task.get("export_file")
        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    
    if not export_file or not Path(export_file).exists():
        raise HTTPException(status_code=404, detail="导出文件不存在")
    
    return FileResponse(
        path=export_file,
        media_type=media_type,
        filename=Path(export_file).name
    )


# Cookie管理API路由

@app.get("/api/cookie/status")
async def get_cookie_status():
    """获取Cookie状态"""
    try:
        # 检查Cookie文件是否存在
        cookie_file = cookie_manager.get_cookie_file_path("pdd")
        exists = cookie_file.exists()
        
        cookies = []
        valid = False
        expires_at = None
        
        if exists:
            # 加载Cookie - 从browser_data文件加载，这个文件包含完整的cookie信息包括expires
            try:
                logger.info(f"Loading cookies from: {cookie_file}")
                with open(str(cookie_file), 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                    valid = len(cookies) > 0
                    
                    # 检查PDDAccessToken是否存在
                    pdd_token = next((c for c in cookies if c.get("name") == "PDDAccessToken"), None)
                    if pdd_token and pdd_token.get("expires"):
                        # 转换过期时间
                        import time
                        expires_timestamp = pdd_token.get("expires", 0)
                        if expires_timestamp > time.time():
                            expires_at = datetime.fromtimestamp(expires_timestamp).isoformat()
                            valid = True
                        else:
                            valid = False
            except Exception as e:
                logger.error(f"加载Cookie文件失败: {e}")
                # 如果browser_data文件加载失败，尝试从config加载
                cookies = cookie_manager.load_cookies_from_config(str(PROJECT_ROOT / "config" / "cookies.json"))
                valid = len(cookies) > 0
        
        return {
            "exists": exists,
            "valid": valid,
            "expiresAt": expires_at,
            "cookies": cookies if exists else []
        }
        
    except Exception as e:
        logger.error(f"获取Cookie状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/cookie/validate")
async def validate_cookie(request: CookieRequest):
    """验证Cookie有效性"""
    try:
        cookies = request.cookies
        
        if not cookies:
            return {"valid": False, "message": "Cookie列表为空"}
        
        # 验证必要的Cookie字段
        required_cookies = ["PDDAccessToken", "pdd_user_id"]
        cookie_names = [c.get("name") for c in cookies]
        
        missing_cookies = [name for name in required_cookies if name not in cookie_names]
        if missing_cookies:
            return {
                "valid": False,
                "message": f"缺少必要的Cookie: {', '.join(missing_cookies)}"
            }
        
        # 检查Cookie是否过期
        import time
        current_time = time.time()
        for cookie in cookies:
            if cookie.get("expires") and cookie.get("expires") < current_time:
                return {
                    "valid": False,
                    "message": f"Cookie {cookie.get('name')} 已过期"
                }
        
        return {"valid": True, "message": "Cookie验证成功"}
        
    except Exception as e:
        logger.error(f"验证Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/cookie/save")
async def save_cookie(request: CookieRequest):
    """保存Cookie"""
    try:
        cookies = request.cookies
        
        if not cookies:
            return {"success": False, "message": "Cookie列表为空"}
        
        # 保存到Cookie管理器
        cookie_file = cookie_manager.get_cookie_file_path("pdd")
        
        # 写入Cookie文件
        with open(cookie_file, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, indent=2, ensure_ascii=False)
        
        # 同时更新config/cookies.json（兼容性）
        config_path = PROJECT_ROOT / "config" / "cookies.json"
        # 确保目录存在
        config_path.parent.mkdir(parents=True, exist_ok=True)
        config_data = {"cookies": cookies}
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"成功保存 {len(cookies)} 个Cookie")
        
        return {
            "success": True,
            "message": f"成功保存 {len(cookies)} 个Cookie"
        }
        
    except Exception as e:
        logger.error(f"保存Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/cookie/clear")
async def clear_cookie():
    """清除Cookie"""
    try:
        # 清除Cookie文件
        success = cookie_manager.clear_cookies("pdd")
        
        # 同时清除config/cookies.json
        config_path = PROJECT_ROOT / "config" / "cookies.json"
        if config_path.exists():
            config_data = {"cookies": []}
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        return {
            "success": success,
            "message": "Cookie已清除" if success else "没有Cookie可清除"
        }
        
    except Exception as e:
        logger.error(f"清除Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/cookie/export")
async def export_cookie():
    """导出Cookie"""
    try:
        # 加载当前Cookie - 使用正确的配置路径
        cookies = cookie_manager.load_cookies_from_config(str(PROJECT_ROOT / "config" / "cookies.json"))
        
        if not cookies:
            return {
                "success": False,
                "message": "没有Cookie可导出"
            }
        
        return {
            "success": True,
            "data": {"cookies": cookies},
            "message": f"成功导出 {len(cookies)} 个Cookie"
        }
        
    except Exception as e:
        logger.error(f"导出Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/cookie/import")
async def import_cookie(request: CookieImportRequest):
    """导入Cookie"""
    try:
        cookies = []
        
        # 优先使用cookies字段
        if request.cookies:
            cookies = request.cookies
        elif request.cookieString:
            # 使用CookieManager的智能解析功能
            cookies = cookie_manager.parse_cookie_input(request.cookieString)
        else:
            # 尝试从请求体解析
            return {"success": False, "message": "没有提供Cookie数据"}
        
        if not cookies:
            return {"success": False, "message": "无法解析Cookie数据"}
        
        # 保存Cookie
        cookie_file = cookie_manager.get_cookie_file_path("pdd")
        with open(cookie_file, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, indent=2, ensure_ascii=False)
        
        # 同时更新config/cookies.json
        config_path = PROJECT_ROOT / "config" / "cookies.json"
        # 确保目录存在
        config_path.parent.mkdir(parents=True, exist_ok=True)
        config_data = {"cookies": cookies}
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"成功导入 {len(cookies)} 个Cookie")
        
        return {
            "success": True,
            "message": f"成功导入 {len(cookies)} 个Cookie"
        }
        
    except Exception as e:
        logger.error(f"导入Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# WebSocket路由
@app.websocket("/ws/crawl/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket连接处理"""
    # 先接受连接，再验证任务
    await websocket.accept()
    logger.info(f"WebSocket连接已建立: task_id={task_id}")
    
    # 验证任务是否存在
    if task_id not in crawl_tasks:
        logger.warning(f"WebSocket连接请求的任务ID不存在: {task_id}")
        await websocket.send_json({
            "type": "error",
            "data": {"message": "Task not found"}
        })
        await websocket.close(code=1008, reason="Task not found")
        return
    
    # 添加到连接池
    if task_id not in websocket_connections:
        websocket_connections[task_id] = []
    websocket_connections[task_id].append(websocket)
    
    # 立即发送初始状态
    try:
        task = crawl_tasks[task_id]
        crawler = task["crawler"]
        progress_info = crawler.get_progress()
        
        await websocket.send_json({
            "type": "connected",
            "data": {
                "taskId": task_id,
                "status": task["status"],
                "startTime": task["start_time"]
            }
        })
        
        # 发送当前进度
        await websocket.send_json({
            "type": "progress",
            "data": {
                "current": progress_info["collected_count"],
                "total": crawler.target_count,
                "percentage": (progress_info["collected_count"] / crawler.target_count * 100) if crawler.target_count > 0 else 0,
                "status": task["status"]
            }
        })
    except Exception as e:
        logger.error(f"发送初始状态失败: {e}")
    
    # 创建两个任务：发送进度和接收消息
    send_task = None
    receive_task = None
    
    try:
        # 发送进度的任务
        async def send_progress():
            last_count = 0
            while True:
                try:
                    await asyncio.sleep(1)  # 每秒更新一次
                    
                    if task_id not in crawl_tasks:
                        logger.warning(f"任务不存在，停止发送进度: {task_id}")
                        break
                        
                    task = crawl_tasks[task_id]
                    crawler = task["crawler"]
                    
                    # 确保crawler存在并且有get_progress方法
                    if not hasattr(crawler, 'get_progress'):
                        logger.error(f"Crawler没有get_progress方法: {task_id}")
                        break
                        
                    progress_info = crawler.get_progress()
                    current_count = progress_info.get("collected_count", 0)
                    
                    # 只在进度有变化时发送更新
                    if current_count != last_count or task["status"] != "running":
                        await websocket.send_json({
                            "type": "progress",
                            "data": {
                                "current": current_count,
                                "total": crawler.target_count,
                                "percentage": (current_count / crawler.target_count * 100) if crawler.target_count > 0 else 0,
                                "status": task["status"]
                            }
                        })
                        last_count = current_count
                        
                    # 如果任务完成或停止，退出循环
                    if task["status"] in ["completed", "stopped", "failed"]:
                        break
                except WebSocketDisconnect:
                    logger.info(f"WebSocket断开连接，停止发送进度: {task_id}")
                    break
                except Exception as e:
                    logger.error(f"发送进度更新失败: {e}")
                    await asyncio.sleep(2)  # 错误后等待更长时间
        
        # 接收消息的任务（处理心跳等）
        async def receive_messages():
            while True:
                try:
                    # 等待客户端消息，设置超时避免无限等待
                    message = await asyncio.wait_for(websocket.receive_json(), timeout=30.0)
                    
                    # 处理心跳消息
                    if message.get("type") == "ping":
                        await websocket.send_json({"type": "pong"})
                    
                    # 可以添加其他消息类型的处理
                    
                except asyncio.TimeoutError:
                    # 超时但连接仍然有效，发送心跳
                    try:
                        await websocket.send_json({"type": "heartbeat"})
                    except WebSocketDisconnect:
                        logger.info("WebSocket在发送心跳时断开")
                        break
                    except Exception as e:
                        logger.error(f"发送心跳失败: {e}")
                        break
                except WebSocketDisconnect:
                    logger.info("WebSocket断开连接")
                    break
                except Exception as e:
                    logger.error(f"接收消息失败: {e}")
                    break
        
        # 并行运行两个任务
        send_task = asyncio.create_task(send_progress())
        receive_task = asyncio.create_task(receive_messages())
        
        # 等待任一任务完成
        done, pending = await asyncio.wait(
            [send_task, receive_task],
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # 取消未完成的任务
        for task in pending:
            task.cancel()
            
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: task_id={task_id}")
    except Exception as e:
        logger.error(f"WebSocket处理错误: {e}")
    finally:
        # 清理任务
        if send_task and not send_task.done():
            send_task.cancel()
        if receive_task and not receive_task.done():
            receive_task.cancel()
        
        # 移除断开的连接
        if task_id in websocket_connections:
            try:
                websocket_connections[task_id].remove(websocket)
                if not websocket_connections[task_id]:
                    del websocket_connections[task_id]
            except ValueError:
                pass


# 辅助函数
async def run_crawler(task_id: str):
    """异步运行爬虫"""
    if task_id not in crawl_tasks:
        logger.error(f"任务不存在: {task_id}")
        return
    
    task = crawl_tasks[task_id]
    crawler = task["crawler"]
    
    try:
        logger.info(f"开始运行爬虫任务: {task_id}")
        
        # 确保爬虫有必要的方法
        if not hasattr(crawler, 'get_progress'):
            logger.error(f"Crawler缺少get_progress方法")
            raise AttributeError("Crawler missing get_progress method")
        
        # 设置进度更新定时器
        async def update_progress():
            while task["status"] == "running" and crawler.is_running:
                try:
                    # 获取进度信息
                    progress_info = crawler.get_progress()
                    
                    # 更新任务进度
                    task["progress"] = {
                        "current": progress_info["collected_count"],
                        "total": crawler.target_count,
                        "percentage": (progress_info["collected_count"] / crawler.target_count * 100) if crawler.target_count > 0 else 0
                    }
                    
                    # 广播进度更新
                    await broadcast_to_websockets(task_id, {
                        "type": "progress",
                        "data": task["progress"]
                    })
                    
                    # 如果达到目标数量，停止
                    if progress_info["collected_count"] >= crawler.target_count:
                        logger.info(f"已达到目标数量: {progress_info['collected_count']}/{crawler.target_count}")
                        break
                    
                    await asyncio.sleep(1)  # 每秒更新一次
                except Exception as e:
                    logger.error(f"更新进度失败: {e}")
                    await asyncio.sleep(1)
        
        # 启动进度更新任务
        progress_task = asyncio.create_task(update_progress())
        
        try:
            # 设置数据回调（如果API监控器存在）
            if hasattr(crawler, 'api_monitor') and crawler.api_monitor:
                original_callback = getattr(crawler.api_monitor, '_data_callback', None)
                
                async def websocket_callback(goods_list):
                    # 调用原始回调
                    if original_callback:
                        if asyncio.iscoroutinefunction(original_callback):
                            await original_callback(goods_list)
                        else:
                            original_callback(goods_list)
                    
                    # 推送到WebSocket - 发送所有新数据
                    if goods_list:
                        logger.info(f"通过WebSocket发送 {len(goods_list)} 条新数据")
                        logger.debug(f"第一条数据示例: {goods_list[0] if goods_list else None}")
                        await broadcast_to_websockets(task_id, {
                            "type": "data",
                            "data": goods_list  # 发送所有新数据，前端会去重
                        })
                    else:
                        logger.debug("websocket_callback 收到空的 goods_list")
                
                # 设置回调
                if hasattr(crawler.api_monitor, 'set_data_callback'):
                    crawler.api_monitor.set_data_callback(websocket_callback)
        except Exception as e:
            logger.warning(f"设置数据回调失败: {e}")
        
        # 运行爬虫
        result = await crawler.start_crawling()
        
        # 停止进度更新
        progress_task.cancel()
        try:
            await progress_task
        except asyncio.CancelledError:
            pass
        
        # 更新任务状态
        task["status"] = "completed"
        task["result"] = result
        task["end_time"] = datetime.now().isoformat()
        
        logger.info(f"爬虫任务完成: {task_id}, 结果: {result}")
        
        # 通知完成
        await broadcast_to_websockets(task_id, {
            "type": "completed",
            "data": {
                "success": result.get("success", False),
                "message": result.get("message", ""),
                "totalGoods": result.get("total_goods", 0)
            }
        })
        
    except Exception as e:
        logger.error(f"爬虫运行失败: {e}", exc_info=True)
        task["status"] = "failed"
        task["error"] = str(e)
        
        # 停止进度更新任务
        if 'progress_task' in locals():
            progress_task.cancel()
            try:
                await progress_task
            except asyncio.CancelledError:
                pass
        
        # 通知错误
        await broadcast_to_websockets(task_id, {
            "type": "error",
            "data": {
                "message": str(e)
            }
        })


async def broadcast_to_websockets(task_id: str, message: dict):
    """向所有WebSocket连接广播消息"""
    if task_id not in websocket_connections:
        logger.debug(f"没有WebSocket连接需要广播: task_id={task_id}")
        return
    
    logger.debug(f"广播消息到 {len(websocket_connections[task_id])} 个连接: {message['type']}")
    
    # 移除断开的连接
    disconnected = []
    for websocket in websocket_connections[task_id].copy():  # 使用copy避免迭代时修改
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.warning(f"发送消息失败: {e}")
            disconnected.append(websocket)
    
    # 清理断开的连接
    for websocket in disconnected:
        try:
            websocket_connections[task_id].remove(websocket)
        except ValueError:
            pass
    
    # 如果没有连接了，删除键
    if task_id in websocket_connections and not websocket_connections[task_id]:
        del websocket_connections[task_id]


if __name__ == "__main__":
    import uvicorn
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='拼多多爬虫API服务器')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务器地址')
    parser.add_argument('--reload', action='store_true', help='开启自动重载')
    args = parser.parse_args()
    
    # 运行服务器
    uvicorn.run(
        "api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )