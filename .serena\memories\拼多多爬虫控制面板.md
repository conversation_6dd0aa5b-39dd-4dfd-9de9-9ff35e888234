# 拼多多爬虫控制面板

## 项目结构
- **位置**: `C:\Users\<USER>\Desktop\pdd2\frontend`
- **技术栈**: React + Vite + CSS3
- **启动命令**: `cd frontend && npm run dev`
- **访问地址**: http://localhost:3000

## 主要功能
1. **搜索配置组件**: 关键词输入、爬取参数设置
2. **数据表格组件**: 实时商品数据展示、图片懒加载
3. **进度显示组件**: 圆形进度环、实时统计
4. **现代化UI**: 拼多多品牌色设计、响应式布局

## 文件说明
- `frontend/启动说明.md`: 详细启动步骤
- `frontend/拼多多爬虫控制面板-优化版说明.md`: 功能特性说明
- `frontend/src/components/`: 增强版组件目录

## 与后端集成
- 需要实现API接口: `/api/crawl/start`, WebSocket `/ws`, `/api/crawl/export`
- 支持实时数据推送和导出功能