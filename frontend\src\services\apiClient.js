// API服务配置 - 连接真实的爬虫后端
import logger from '../utils/logger'

const API_BASE_URL = 'http://localhost:8000/api'
const WS_BASE_URL = 'ws://localhost:8000/ws'

// API客户端
class ApiClient {
  constructor() {
    this.taskId = null
    this.websocket = null
    logger.info('ApiClient', '初始化API客户端', { API_BASE_URL, WS_BASE_URL })
  }

  // 开始爬取
  async startCrawl(config) {
    logger.info('ApiClient', '开始爬取任务', { config })
    try {
      const response = await fetch(`${API_BASE_URL}/crawl/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keywords: config.keywords ? config.keywords.split(',').map(k => k.trim()) : [config.keyword],
          targetCount: config.targetCount || 100,
          sortMethod: config.sortType || 'default',
          maxPages: config.maxPages || 5,
          headless: config.headless !== false,  // 默认为true
          enableFilter: config.enableFilter === true  // 精确筛选功能
        })
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || '开始爬取失败')
      }
      
      const data = await response.json()
      this.taskId = data.taskId
      logger.success('ApiClient', '爬取任务启动成功', { taskId: data.taskId, data })
      return data
    } catch (error) {
      logger.error('ApiClient', 'API调用失败', { error: error.message, stack: error.stack })
      throw error
    }
  }
  
  // 暂停爬取
  async pauseCrawl() {
    if (!this.taskId) throw new Error('没有活动的任务')
    
    const response = await fetch(`${API_BASE_URL}/crawl/${this.taskId}/pause`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!response.ok) {
      const error = await response.json()
      logger.error('ApiClient', '暂停任务失败', { taskId: this.taskId, error })
      throw new Error(error.detail || '暂停爬取失败')
    }
    
    const result = await response.json()
    logger.success('ApiClient', '任务已暂停', { taskId: this.taskId, result })
    return result
  }
  
  // 恢复爬取
  async resumeCrawl() {
    if (!this.taskId) {
      logger.warn('ApiClient', '恢复失败: 没有活动的任务')
      throw new Error('没有活动的任务')
    }
    
    logger.info('ApiClient', '恢复爬取任务', { taskId: this.taskId })
    
    const response = await fetch(`${API_BASE_URL}/crawl/${this.taskId}/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || '恢复爬取失败')
    }
    
    return await response.json()
  }
  
  // 停止爬取
  async stopCrawl() {
    if (!this.taskId) throw new Error('没有活动的任务')
    
    const response = await fetch(`${API_BASE_URL}/crawl/${this.taskId}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || '停止爬取失败')
    }
    
    // 清理任务ID
    const result = await response.json()
    this.taskId = null
    return result
  }
  
  // 获取爬取状态
  async getCrawlStatus() {
    if (!this.taskId) throw new Error('没有活动的任务')
    
    const response = await fetch(`${API_BASE_URL}/crawl/${this.taskId}/status`, {
      headers: {
        'Origin': window.location.origin
      }
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || '获取状态失败')
    }
    
    return await response.json()
  }
  
  // 获取预览数据
  async getPreviewData(limit = 20) {
    if (!this.taskId) throw new Error('没有活动的任务')
    
    const response = await fetch(`${API_BASE_URL}/crawl/${this.taskId}/preview?limit=${limit}`)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || '获取预览数据失败')
    }
    
    return await response.json()
  }
  
  // 导出数据
  async exportData(format = 'xlsx') {
    if (!this.taskId) throw new Error('没有活动的任务')
    
    const endpoint = format === 'csv' ? 
      `${API_BASE_URL}/export/${this.taskId}/csv` : 
      `${API_BASE_URL}/export/${this.taskId}`
    
    const response = await fetch(endpoint, {
      method: 'POST'
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || '导出数据失败')
    }
    
    const data = await response.json()
    
    // 如果导出成功，下载文件
    if (data.success && data.exportId) {
      const downloadUrl = `${API_BASE_URL}/export/${data.exportId}/download?format=${format}`
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = data.fileName || `拼多多商品数据.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    
    return data
  }
  
  // 连接WebSocket
  connectWebSocket(callbacks) {
    if (!this.taskId) throw new Error('没有活动的任务')
    
    // 如果已有连接，先关闭
    if (this.websocket) {
      this.websocket.close()
    }
    
    logger.info('WebSocket', '正在连接WebSocket', { 
      url: `${WS_BASE_URL}/crawl/${this.taskId}`,
      taskId: this.taskId 
    })
    this.websocket = new WebSocket(`${WS_BASE_URL}/crawl/${this.taskId}`)
    
    // 心跳定时器
    let heartbeatInterval = null
    let reconnectTimeout = null
    let reconnectAttempts = 0
    const maxReconnectAttempts = 5
    
    // 启动心跳
    const startHeartbeat = () => {
      if (heartbeatInterval) clearInterval(heartbeatInterval)
      
      heartbeatInterval = setInterval(() => {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          logger.debug('WebSocket', '发送心跳', { taskId: this.taskId })
          this.websocket.send(JSON.stringify({ type: 'ping' }))
        }
      }, 20000) // 每20秒发送一次心跳
    }
    
    // 停止心跳
    const stopHeartbeat = () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
        heartbeatInterval = null
      }
    }
    
    // 重连机制
    const reconnect = () => {
      if (reconnectAttempts >= maxReconnectAttempts) {
        logger.error('WebSocket', 'WebSocket重连失败，超过最大重试次数', { 
          taskId: this.taskId,
          attempts: reconnectAttempts 
        })
        callbacks.onError && callbacks.onError({message: 'WebSocket连接失败，请刷新页面重试'})
        return
      }
      
      reconnectAttempts++
      logger.warn('WebSocket', `WebSocket重连中... (第${reconnectAttempts}次)`, { 
        taskId: this.taskId,
        attempt: reconnectAttempts 
      })
      
      reconnectTimeout = setTimeout(() => {
        this.connectWebSocket(callbacks)
      }, 1000 * reconnectAttempts) // 递增重连延迟
    }
    
    this.websocket.onopen = () => {
      logger.success('WebSocket', 'WebSocket连接已建立', { 
        taskId: this.taskId,
        readyState: this.websocket.readyState 
      })
      reconnectAttempts = 0 // 重置重连次数
      startHeartbeat() // 启动心跳
      callbacks.onOpen && callbacks.onOpen()
    }
    
    this.websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        // 根据消息类型处理
        switch (data.type) {
          case 'connected':
            logger.info('WebSocket', 'WebSocket连接确认', { 
              taskId: this.taskId,
              data: data.data 
            })
            break
          case 'pong':
            // 心跳响应，不需要处理
            break
          case 'heartbeat':
            // 服务器心跳
            break
          case 'progress':
            logger.debug('WebSocket', '接收到进度更新', { 
              taskId: this.taskId,
              progress: data.data 
            })
            callbacks.onProgress && callbacks.onProgress(data.data)
            break
          case 'data':
            logger.debug('WebSocket', '接收到数据', { 
              taskId: this.taskId,
              dataCount: data.data?.length || 0 
            })
            callbacks.onData && callbacks.onData(data.data)
            break
          case 'completed':
            logger.success('WebSocket', '任务完成', { 
              taskId: this.taskId,
              result: data.data 
            })
            callbacks.onCompleted && callbacks.onCompleted(data.data)
            stopHeartbeat() // 完成后停止心跳
            break
          case 'error':
            logger.error('WebSocket', '接收到错误', { 
              taskId: this.taskId,
              error: data.data 
            })
            callbacks.onError && callbacks.onError(data.data)
            break
          default:
            callbacks.onMessage && callbacks.onMessage(data)
        }
      } catch (error) {
        logger.error('WebSocket', 'WebSocket消息解析错误', { 
          taskId: this.taskId,
          error: error.message,
          rawData: event.data 
        })
      }
    }
    
    this.websocket.onerror = (error) => {
      logger.error('WebSocket', 'WebSocket错误', { 
        taskId: this.taskId,
        error: error,
        readyState: this.websocket?.readyState 
      })
      stopHeartbeat()
      callbacks.onError && callbacks.onError({message: 'WebSocket连接错误'})
    }
    
    this.websocket.onclose = (event) => {
      logger.warn('WebSocket', 'WebSocket连接已关闭', {
        taskId: this.taskId,
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      })
      
      stopHeartbeat()
      
      // 如果是非正常关闭且任务还在运行，尝试重连
      if (!event.wasClean && this.taskId && callbacks.shouldReconnect !== false) {
        reconnect()
      } else {
        callbacks.onClose && callbacks.onClose()
        this.websocket = null
      }
    }
    
    // 清理函数
    this.websocket._cleanup = () => {
      stopHeartbeat()
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout)
        reconnectTimeout = null
      }
    }
    
    return this.websocket
  }
  
  // 断开WebSocket
  disconnectWebSocket() {
    if (this.websocket) {
      logger.info('WebSocket', '主动断开WebSocket连接', { 
        taskId: this.taskId,
        readyState: this.websocket.readyState 
      })
      
      // 清理心跳等资源
      if (this.websocket._cleanup) {
        this.websocket._cleanup()
      }
      
      // 关闭连接
      if (this.websocket.readyState === WebSocket.OPEN || 
          this.websocket.readyState === WebSocket.CONNECTING) {
        this.websocket.close(1000, 'Normal closure')
      }
      
      this.websocket = null
    }
  }
  
  // 检查API健康状态
  async checkHealth() {
    try {
      logger.debug('ApiClient', '检查API健康状态')
      const response = await fetch(`${API_BASE_URL}/health`)
      if (!response.ok) {
        logger.warn('ApiClient', 'API健康检查失败', { status: response.status })
        return false
      }
      const data = await response.json()
      const isHealthy = data.status === 'healthy'
      logger.debug('ApiClient', 'API健康状态', { healthy: isHealthy, data })
      return isHealthy
    } catch (error) {
      logger.error('ApiClient', 'API健康检查异常', { error: error.message })
      return false
    }
  }
  
  // ========== Cookie管理API ==========
  
  // 验证Cookie
  async validateCookie(cookies) {
    logger.info('Cookie', '开始验证Cookie', { cookieLength: cookies?.length })
    try {
      const response = await fetch(`${API_BASE_URL}/cookie/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cookies })
      })
      
      if (!response.ok) {
        const error = await response.json()
        logger.error('Cookie', 'Cookie验证失败', { error })
        throw new Error(error.detail || 'Cookie验证失败')
      }
      
      const result = await response.json()
      logger.success('Cookie', 'Cookie验证成功', { valid: result.valid, count: result.count })
      return result
    } catch (error) {
      logger.error('Cookie', 'Cookie验证异常', { error: error.message })
      throw error
    }
  }
  
  // 保存Cookie
  async saveCookie(cookies) {
    logger.info('Cookie', '保存Cookie', { cookieLength: cookies?.length })
    try {
      const response = await fetch(`${API_BASE_URL}/cookie/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cookies })
      })
      
      if (!response.ok) {
        const error = await response.json()
        logger.error('Cookie', 'Cookie保存失败', { error })
        throw new Error(error.detail || 'Cookie保存失败')
      }
      
      const result = await response.json()
      logger.success('Cookie', 'Cookie保存成功', result)
      return result
    } catch (error) {
      logger.error('Cookie', 'Cookie保存异常', { error: error.message })
      throw error
    }
  }
  
  // 获取Cookie状态
  async getCookieStatus() {
    logger.debug('Cookie', '检查Cookie状态')
    try {
      const response = await fetch(`${API_BASE_URL}/cookie/status`)
      
      if (!response.ok) {
        const error = await response.json()
        logger.error('Cookie', '获取Cookie状态失败', { error })
        throw new Error(error.detail || '获取Cookie状态失败')
      }
      
      const result = await response.json()
      logger.debug('Cookie', 'Cookie状态', { valid: result.valid, expiresAt: result.expiresAt })
      return result
    } catch (error) {
      logger.error('Cookie', '获取Cookie状态异常', { error: error.message })
      throw error
    }
  }
  
  // 检查Cookie状态的别名方法 (为了兼容性)
  async checkCookieStatus() {
    return this.getCookieStatus()
  }
  
  // 清除Cookie
  async clearCookie() {
    logger.warn('Cookie', '清除Cookie')
    try {
      const response = await fetch(`${API_BASE_URL}/cookie/clear`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        const error = await response.json()
        logger.error('Cookie', 'Cookie清除失败', { error })
        throw new Error(error.detail || 'Cookie清除失败')
      }
      
      const result = await response.json()
      logger.success('Cookie', 'Cookie已清除', result)
      return result
    } catch (error) {
      logger.error('Cookie', 'Cookie清除异常', { error: error.message })
      throw error
    }
  }
  
  // 导出Cookie
  async exportCookie() {
    logger.info('Cookie', '导出Cookie')
    try {
      const response = await fetch(`${API_BASE_URL}/cookie/export`)
      
      if (!response.ok) {
        const error = await response.json()
        logger.error('Cookie', 'Cookie导出失败', { error })
        throw new Error(error.detail || 'Cookie导出失败')
      }
      
      const result = await response.json()
      logger.success('Cookie', 'Cookie导出成功', { count: result.cookies?.length })
      return result
    } catch (error) {
      logger.error('Cookie', 'Cookie导出异常', { error: error.message })
      throw error
    }
  }
  
  // 导入Cookie
  async importCookie(cookieData) {
    try {
      const response = await fetch(`${API_BASE_URL}/cookie/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cookieData)
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Cookie导入失败')
      }
      
      return await response.json()
    } catch (error) {
      console.error('Cookie导入失败:', error)
      throw error
    }
  }
}

// 导出单例
export default new ApiClient()