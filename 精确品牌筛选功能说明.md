# 精确品牌筛选功能说明

## 新增功能

### 1. 精确品牌匹配

当搜索关键词明确包含品牌和产品类型时（如"海尔冰箱476"），系统会进行精确品牌匹配：

- **搜索"海尔476"**：会包含海尔和统帅品牌商品（统帅是海尔的子品牌）
- **搜索"海尔冰箱476"**：只包含海尔品牌商品，过滤掉统帅品牌

### 2. 混合品牌检测

系统会自动识别并过滤包含多个品牌的混淆商品：

- 检测标题中同时出现的多个品牌（如"海尔统帅美的格力"）
- 这类商品通常是标题党或聚合商品，会被自动过滤

### 3. 多型号商品过滤

识别并降低多型号混合商品的权重：

- 检测"476/539/468"这样的多型号格式
- 包含多个型号的商品匹配度会降低70%
- 避免不精确的多型号聚合商品

## 工作原理

### 品牌匹配规则

1. **通用搜索**（如"海尔"）：
   - 海尔品牌：100%匹配
   - 统帅品牌：80%匹配（子品牌）

2. **精确搜索**（如"海尔冰箱"）：
   - 海尔品牌：100%匹配
   - 统帅品牌：0%匹配（精确模式）

### 过滤规则

1. **混合品牌商品**：直接拒绝（得分0.1）
2. **多型号商品**：降低70%权重
3. **产品类型不匹配**：降低80%权重

## 测试方法

运行测试脚本查看效果：
```bash
python test_statistical_filter.py
```

测试场景：
- 测试1：搜索"海尔476" - 包含统帅
- 测试3：搜索"海尔冰箱476" - 不包含统帅
- 测试4：验证混合品牌过滤