.search-config-enhanced {
  background: var(--surface);
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-config-enhanced:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 标题部分 */
.config-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(238, 77, 45, 0.05) 0%, rgba(238, 77, 45, 0.02) 100%);
  border-bottom: 1px solid var(--border);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.config-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

/* 表单内容 */
.config-body {
  padding: 24px;
}

.form-field {
  margin-bottom: 20px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
  margin-bottom: 8px;
}

.required-mark {
  color: var(--primary);
  font-weight: 600;
}

/* 输入框容器 */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--secondary);
  border: 2px solid transparent;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.input-container:hover {
  border-color: rgba(238, 77, 45, 0.2);
}

.input-container.focused {
  border-color: var(--primary);
  background: var(--surface);
  box-shadow: 0 0 0 4px rgba(238, 77, 45, 0.1);
}

.input-icon {
  position: absolute;
  left: 16px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  pointer-events: none;
  transition: color 0.2s ease;
}

.input-container.focused .input-icon {
  color: var(--primary);
}

.enhanced-input {
  width: 100%;
  padding: 12px 44px 12px 44px;
  background: transparent;
  border: none;
  font-size: 15px;
  color: var(--text);
  outline: none;
}

.enhanced-input::placeholder {
  color: var(--text-secondary);
}

.clear-button {
  position: absolute;
  right: 12px;
  width: 28px;
  height: 28px;
  border: none;
  background: var(--surface);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: var(--border);
  color: var(--text);
}

.field-hint {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 6px;
}

/* 数量配置网格 */
.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.number-input-wrapper {
  display: flex;
  align-items: center;
  background: var(--secondary);
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.number-input-wrapper:hover {
  border-color: rgba(238, 77, 45, 0.2);
}

.number-input-wrapper:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(238, 77, 45, 0.1);
}

.number-button {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.number-button:hover:not(:disabled) {
  background: var(--border);
  color: var(--primary);
}

.number-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.number-input {
  flex: 1;
  text-align: center;
  border: none;
  background: transparent;
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
  outline: none;
  padding: 0 8px;
}

/* 统计信息 */
.stats-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(238, 77, 45, 0.03) 0%, rgba(238, 77, 45, 0.01) 100%);
  border-radius: 12px;
  border: 1px solid rgba(238, 77, 45, 0.1);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.time {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.stat-icon.items {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text);
}

/* 暗色主题适配 */
[data-theme="dark"] .search-config-enhanced {
  background: var(--surface);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .config-header {
  background: linear-gradient(135deg, rgba(255, 104, 84, 0.1) 0%, rgba(255, 104, 84, 0.05) 100%);
}

[data-theme="dark"] .input-container {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .input-container.focused {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .clear-button {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .clear-button:hover {
  background: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .number-input-wrapper {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .stats-container {
  background: linear-gradient(135deg, rgba(255, 104, 84, 0.08) 0%, rgba(255, 104, 84, 0.03) 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .config-header {
    padding: 16px 20px;
  }
  
  .config-body {
    padding: 20px;
  }
}