/* 动画效果库 */

/* ===== 基础动画 ===== */

/* 淡入淡出 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 滑入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinReverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes pulseScale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 震动动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-20px);
  }
  50% {
    transform: translateY(0);
  }
  75% {
    transform: translateY(-10px);
  }
}

/* 闪烁动画 */
@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* 涟漪效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 加载动画 */
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 进度条动画 */
@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes progressStripe {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 0;
  }
}

/* ===== 动画类 ===== */

/* 淡入淡出类 */
.fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out) forwards;
}

.fade-out {
  animation: fadeOut var(--duration-300) var(--ease-out) forwards;
}

/* 滑入类 */
.slide-in-up {
  animation: slideInUp var(--duration-300) var(--ease-out) forwards;
}

.slide-in-down {
  animation: slideInDown var(--duration-300) var(--ease-out) forwards;
}

.slide-in-left {
  animation: slideInLeft var(--duration-300) var(--ease-out) forwards;
}

.slide-in-right {
  animation: slideInRight var(--duration-300) var(--ease-out) forwards;
}

/* 缩放类 */
.scale-in {
  animation: scaleIn var(--duration-300) var(--ease-out) forwards;
}

.scale-out {
  animation: scaleOut var(--duration-300) var(--ease-out) forwards;
}

/* 旋转类 */
.spin {
  animation: spin var(--duration-1000) linear infinite;
}

.spin-slow {
  animation: spin 3s linear infinite;
}

.spin-reverse {
  animation: spinReverse var(--duration-1000) linear infinite;
}

/* 脉冲类 */
.pulse {
  animation: pulse 2s var(--ease-in-out) infinite;
}

.pulse-scale {
  animation: pulseScale 2s var(--ease-in-out) infinite;
}

/* 震动类 */
.shake {
  animation: shake var(--duration-500) var(--ease-in-out);
}

/* 弹跳类 */
.bounce {
  animation: bounce 1s var(--ease-in-out) infinite;
}

/* 闪烁类 */
.blink {
  animation: blink 1s var(--ease-in-out) infinite;
}

/* ===== 过渡效果 ===== */

/* 通用过渡 */
.transition-all {
  transition: all var(--duration-300) var(--ease-out);
}

.transition-colors {
  transition: color var(--duration-300) var(--ease-out),
              background-color var(--duration-300) var(--ease-out),
              border-color var(--duration-300) var(--ease-out);
}

.transition-opacity {
  transition: opacity var(--duration-300) var(--ease-out);
}

.transition-transform {
  transition: transform var(--duration-300) var(--ease-out);
}

.transition-shadow {
  transition: box-shadow var(--duration-300) var(--ease-out);
}

/* 过渡时长变体 */
.duration-75 { transition-duration: var(--duration-75); }
.duration-100 { transition-duration: var(--duration-100); }
.duration-150 { transition-duration: var(--duration-150); }
.duration-200 { transition-duration: var(--duration-200); }
.duration-300 { transition-duration: var(--duration-300); }
.duration-500 { transition-duration: var(--duration-500); }
.duration-700 { transition-duration: var(--duration-700); }
.duration-1000 { transition-duration: var(--duration-1000); }

/* ===== 特殊效果 ===== */

/* 涟漪效果容器 */
.ripple-container {
  position: relative;
  overflow: hidden;
}

.ripple-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.ripple-container:active::before {
  width: 100px;
  height: 100px;
  opacity: 0;
  transition: width var(--duration-500) var(--ease-out),
              height var(--duration-500) var(--ease-out),
              opacity var(--duration-500) var(--ease-out);
}

/* 加载动画容器 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-top-color: var(--color-primary-500);
  border-radius: 50%;
  animation: loading var(--duration-700) linear infinite;
}

/* 加载点动画 */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--color-primary-500);
  border-radius: 50%;
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

/* 骨架屏效果 */
.skeleton {
  position: relative;
  overflow: hidden;
  background-color: var(--bg-secondary);
}

.skeleton::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: skeleton 1.5s infinite;
}

@keyframes skeleton {
  100% {
    transform: translateX(100%);
  }
}

/* 悬浮效果 */
.hover-lift {
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 按压效果 */
.active-scale {
  transition: transform var(--duration-100) var(--ease-out);
}

.active-scale:active {
  transform: scale(0.98);
}

/* 发光效果 */
.glow {
  transition: box-shadow var(--duration-300) var(--ease-out);
}

.glow:hover {
  box-shadow: 0 0 20px rgba(238, 77, 45, 0.3);
}

/* ===== 页面过渡 ===== */

/* 页面进入 */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--duration-300) var(--ease-out);
}

/* 页面离开 */
.page-leave {
  opacity: 1;
  transform: translateY(0);
}

.page-leave-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: all var(--duration-300) var(--ease-out);
}