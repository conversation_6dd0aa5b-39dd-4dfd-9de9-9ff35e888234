{"desktop": [{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "device_type": "desktop", "browser": "Chrome", "browser_version": {"major": 121, "minor": 0, "build": 0, "patch": 0}, "os": "Windows", "os_version": "10", "platform": "Win32", "weight": 1.0, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 1920, "height": 1080, "pixel_ratio": 1}, {"width": 1366, "height": 768, "pixel_ratio": 1}, {"width": 1536, "height": 864, "pixel_ratio": 1.25}], "hardware_profiles": [{"cpu_cores": 8, "memory": 16, "gpu": "NVIDIA GeForce RTX 3060"}, {"cpu_cores": 6, "memory": 16, "gpu": "AMD Radeon RX 6600"}, {"cpu_cores": 4, "memory": 8, "gpu": "Intel UHD Graphics 630"}]}, {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "device_type": "desktop", "browser": "Chrome", "browser_version": {"major": 120, "minor": 0, "build": 0, "patch": 0}, "os": "Windows", "os_version": "10", "platform": "Win32", "weight": 0.9, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 1920, "height": 1080, "pixel_ratio": 1}, {"width": 2560, "height": 1440, "pixel_ratio": 1}], "hardware_profiles": [{"cpu_cores": 8, "memory": 16, "gpu": "NVIDIA GeForce RTX 3070"}, {"cpu_cores": 12, "memory": 32, "gpu": "NVIDIA GeForce RTX 3080"}]}, {"user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "device_type": "desktop", "browser": "Chrome", "browser_version": {"major": 121, "minor": 0, "build": 0, "patch": 0}, "os": "macOS", "os_version": "10.15.7", "platform": "MacIntel", "weight": 0.8, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 1440, "height": 900, "pixel_ratio": 2}, {"width": 1680, "height": 1050, "pixel_ratio": 2}, {"width": 2560, "height": 1600, "pixel_ratio": 2}], "hardware_profiles": [{"cpu_cores": 8, "memory": 16, "gpu": "Apple M1"}, {"cpu_cores": 10, "memory": 32, "gpu": "Apple M1 Pro"}]}, {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0", "device_type": "desktop", "browser": "Firefox", "browser_version": {"major": 121, "minor": 0}, "os": "Windows", "os_version": "10", "platform": "Win32", "weight": 0.7, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 1920, "height": 1080, "pixel_ratio": 1}, {"width": 1366, "height": 768, "pixel_ratio": 1}], "hardware_profiles": [{"cpu_cores": 6, "memory": 16, "gpu": "AMD Radeon RX 580"}, {"cpu_cores": 8, "memory": 16, "gpu": "NVIDIA GeForce GTX 1660"}]}], "mobile": [{"user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1", "device_type": "mobile", "browser": "Safari", "browser_version": {"major": 17, "minor": 2}, "os": "iOS", "os_version": "17.2", "platform": "iPhone", "weight": 1.0, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 390, "height": 844, "pixel_ratio": 3}, {"width": 375, "height": 812, "pixel_ratio": 3}, {"width": 414, "height": 896, "pixel_ratio": 2}], "hardware_profiles": [{"cpu_cores": 6, "memory": 6, "gpu": "Apple A16"}, {"cpu_cores": 6, "memory": 8, "gpu": "Apple A17"}]}, {"user_agent": "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "device_type": "mobile", "browser": "Chrome", "browser_version": {"major": 121, "minor": 0, "build": 0, "patch": 0}, "os": "Android", "os_version": "14", "platform": "Linux armv7l", "weight": 0.9, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 412, "height": 915, "pixel_ratio": 2.6}, {"width": 360, "height": 800, "pixel_ratio": 3}, {"width": 393, "height": 851, "pixel_ratio": 2.75}], "hardware_profiles": [{"cpu_cores": 8, "memory": 8, "gpu": "Adreno 660"}, {"cpu_cores": 8, "memory": 12, "gpu": "Mali-G78"}]}, {"user_agent": "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "device_type": "mobile", "browser": "Chrome", "browser_version": {"major": 120, "minor": 0, "build": 0, "patch": 0}, "os": "Android", "os_version": "13", "platform": "Linux armv7l", "weight": 0.8, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 412, "height": 915, "pixel_ratio": 2.6}], "hardware_profiles": [{"cpu_cores": 8, "memory": 8, "gpu": "Mali-G710"}]}], "tablet": [{"user_agent": "Mozilla/5.0 (iPad; CPU OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1", "device_type": "tablet", "browser": "Safari", "browser_version": {"major": 17, "minor": 2}, "os": "iOS", "os_version": "17.2", "platform": "iPad", "weight": 1.0, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 820, "height": 1180, "pixel_ratio": 2}, {"width": 1024, "height": 1366, "pixel_ratio": 2}, {"width": 768, "height": 1024, "pixel_ratio": 2}], "hardware_profiles": [{"cpu_cores": 8, "memory": 8, "gpu": "Apple M1"}, {"cpu_cores": 10, "memory": 16, "gpu": "Apple M2"}]}, {"user_agent": "Mozilla/5.0 (Linux; Android 13; SM-T870) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "device_type": "tablet", "browser": "Chrome", "browser_version": {"major": 121, "minor": 0, "build": 0, "patch": 0}, "os": "Android", "os_version": "13", "platform": "Linux armv7l", "weight": 0.8, "last_used": 0, "usage_count": 0, "screen_resolutions": [{"width": 800, "height": 1280, "pixel_ratio": 1.5}, {"width": 1200, "height": 1920, "pixel_ratio": 2}], "hardware_profiles": [{"cpu_cores": 8, "memory": 8, "gpu": "Adreno 650"}]}]}