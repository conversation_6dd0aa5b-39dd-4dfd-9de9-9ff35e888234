.advanced-filters {
  margin-bottom: 24px;
}

.filters-toggle {
  width: 100%;
  padding: 12px 16px;
  background: var(--secondary);
  border: 1px solid var(--border);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filters-toggle:hover {
  background: var(--surface);
  border-color: rgba(238, 77, 45, 0.2);
}

.filters-toggle.expanded {
  background: var(--surface);
  border-color: var(--primary);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.toggle-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text);
  font-size: 14px;
  font-weight: 500;
}

.active-count {
  padding: 2px 8px;
  background: var(--primary);
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.toggle-arrow {
  color: var(--text-secondary);
  transition: transform 0.2s ease;
}

.filters-toggle.expanded .toggle-arrow {
  transform: rotate(180deg);
}

.filters-content {
  background: var(--surface);
  border: 1px solid var(--primary);
  border-top: none;
  border-radius: 0 0 10px 10px;
  padding: 20px;
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: var(--text);
  margin-bottom: 8px;
}

/* 价格范围 */
.price-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.price-input {
  flex: 1;
  padding: 8px 12px;
  background: var(--secondary);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text);
  outline: none;
  transition: all 0.2s ease;
}

.price-input:focus {
  border-color: var(--primary);
  background: var(--surface);
}

.price-separator {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 销量筛选 */
.sales-input {
  width: 100%;
  padding: 8px 12px;
  background: var(--secondary);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text);
  outline: none;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.sales-input:focus {
  border-color: var(--primary);
  background: var(--surface);
}

/* 预设按钮 */
.filter-presets {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.preset-button {
  padding: 6px 12px;
  background: var(--secondary);
  border: 1px solid var(--border);
  border-radius: 16px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-button:hover:not(:disabled) {
  border-color: var(--primary);
  color: var(--primary);
}

.preset-button.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.preset-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 商品类型 */
.type-filter {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text);
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary);
  cursor: pointer;
}

.checkbox-text {
  user-select: none;
}

/* 发货地 */
.location-input {
  width: 100%;
  padding: 8px 12px;
  background: var(--secondary);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text);
  outline: none;
  transition: all 0.2s ease;
}

.location-input:focus {
  border-color: var(--primary);
  background: var(--surface);
}

/* 清除筛选 */
.clear-filters {
  width: 100%;
  padding: 10px 16px;
  background: transparent;
  border: 1px solid var(--border);
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 16px;
}

.clear-filters:hover:not(:disabled) {
  border-color: var(--primary);
  color: var(--primary);
  background: rgba(238, 77, 45, 0.05);
}

.clear-filters:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 暗色主题适配 */
[data-theme="dark"] .filters-toggle {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .filters-toggle:hover {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .filters-content {
  background: var(--surface);
}

[data-theme="dark"] .price-input,
[data-theme="dark"] .sales-input,
[data-theme="dark"] .location-input {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .preset-button {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .clear-filters:hover:not(:disabled) {
  background: rgba(238, 77, 45, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .price-filter {
    flex-direction: column;
  }
  
  .price-separator {
    display: none;
  }
  
  .filter-presets {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}