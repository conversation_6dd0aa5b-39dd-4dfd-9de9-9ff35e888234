# 拼多多 Icon ID 映射参考文档

基于实际API响应数据分析，整理了拼多多商品列表中的icon ID映射关系。

## Icon ID 映射表

| Icon ID | 含义 | 英文名称 | URL 示例 | 说明 |
|---------|------|----------|----------|------|
| **0** | 秒杀 | Flash Sale | `https://funimg.pddpic.com/hot_friends/xxx.png` | 限时秒杀活动标识 |
| **10** | 旗舰店 | Flagship Store | `https://promotion.pddpic.com/promo/index/xxx.png` | 品牌旗舰店标识 |
| **20001** | 百亿补贴 | Billion Subsidy | `http://img.pddpic.com/social/pincard/1/share.png` | 百亿补贴商品的唯一准确标识 |
| **20013** | 夏清仓 | Summer Clearance | `https://commimg.pddpic.com/oms_img_ng/xxx.png` | 季节性清仓活动 |
| **10014** | 品牌黑标 | Brand Black Label | `https://promotion.pddpic.com/promo/gexinghua/xxx.png` | 品牌认证标识 |

## 在代码中的应用

### 1. IconIds 数组
```javascript
"iconIds": [20001, 0]  // 表示该商品同时有百亿补贴和秒杀标识
```

### 2. Icon_list 数组
```javascript
"icon_list": [
    {
        "width": 156,
        "id": 20001,  // 百亿补贴
        "tag_track_info": "2&20001",
        "url": "http://img.pddpic.com/social/pincard/1/share.png",
        "height": 42
    }
]
```

## 补贴识别逻辑

### 百亿补贴识别
- **主要方法**: 检查 `iconIds` 数组中是否包含 `20001`
- **备用方法**: 检查 `icon_list` 中是否有 `id: 20001` 的记录

### 政府补贴识别
- **主要方法**: 检查商品名称中的关键词
  - "国补"
  - "政府补贴"
  - "政府消费补贴"
- **注意**: `activity_type=34` 不是可靠的政府补贴标识（仅7%相关性）

## 商户类型映射

| merchant_type | 含义 |
|---------------|------|
| 0 | 自营/官方店铺 |
| 1 | 企业店铺 |
| 2 | 品牌店铺 |
| 3 | 品牌授权店 |
| 4 | 专营店 |
| 5 | 普通商家 |

## 使用建议

1. **优先使用iconIds**: 这是最准确的商品属性标识方式
2. **icon_list作为补充**: 提供了更多图标信息，包括URL和尺寸
3. **组合判断**: 某些商品可能同时具有多个图标，如"百亿补贴+秒杀"
4. **定期更新**: 平台可能会新增icon ID，需要定期分析和更新映射

## 更新历史

- 2025-07-29: 初始版本，基于响应2.js和响应3.js分析
- 2025-07-29: 更新ID 10为"旗舰店"，ID 20013为"夏清仓"