"""
数据处理器模块
负责数据清洗、验证和格式化
"""

import re
import datetime
from typing import Dict, List, Any, Optional, Set, Tuple
from loguru import logger

from src.utils.helpers import load_config, format_price, format_sales, clean_text, validate_url

# 导入RapidFuzz用于高性能字符串匹配
try:
    from rapidfuzz import fuzz, process
    RAPIDFUZZ_AVAILABLE = True
    logger.info("RapidFuzz库已成功导入，启用高性能品牌识别")
except ImportError:
    RAPIDFUZZ_AVAILABLE = False
    logger.warning("RapidFuzz库未安装，使用传统品牌识别方法")


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化数据处理器"""
        self.config = load_config(config_path)
        self.export_config = self.config.get("export", {})
        self.column_mapping = self.export_config.get("excel", {}).get("column_mapping", {})
        
        # 数据验证规则
        self.required_fields = ["goods_id", "goods_name"]
        self.numeric_fields = ["price", "original_price", "sales", "comment_count", "rating"]
        self.url_fields = ["goods_url", "image_url"]
        
        # 初始化ID解码映射
        self._init_id_mappings()

        # 预构建品牌候选列表（提高RapidFuzz性能）
        self._build_brand_candidates()

        # 初始化营销标签系统
        self._init_marketing_system()
        
        # 初始化精确商品筛选器
        self._init_product_filter()

        logger.info("数据处理器初始化完成")
    
    async def process_goods_data(self, raw_data: List[Dict[str, Any]], search_keyword: str = None) -> List[Dict[str, Any]]:
        """
        处理商品数据
        
        Args:
            raw_data: 原始商品数据列表
            search_keyword: 搜索关键词（用于精确筛选）
            
        Returns:
            List[Dict[str, Any]]: 处理后的商品数据
        """
        processed_data = []
        
        for item in raw_data:
            try:
                # 数据清洗
                cleaned_item = await self._clean_data(item)
                
                # 数据验证
                if await self._validate_data(cleaned_item):
                    # 数据格式化
                    formatted_item = await self._format_data(cleaned_item)
                    processed_data.append(formatted_item)
                else:
                    logger.warning(f"数据验证失败，跳过商品: {item.get('goods_id', 'unknown')}")
                    
            except Exception as e:
                logger.error(f"处理商品数据时出错: {e}, 数据: {item}")
                continue
        
        # 去重
        deduplicated_data = await self._deduplicate_data(processed_data)
        
        # 精确商品筛选（如果启用且提供了搜索关键词）
        if self.product_filter and search_keyword:
            try:
                filtered_data = self.product_filter.filter_products(deduplicated_data, search_keyword)
                logger.info(f"精确筛选: {len(deduplicated_data)} -> {len(filtered_data)} 条商品匹配关键词 '{search_keyword}'")
                deduplicated_data = filtered_data
            except Exception as e:
                logger.error(f"商品筛选时出错: {e}")
                # 筛选失败时继续使用原数据
        
        logger.info(f"数据处理完成: 原始 {len(raw_data)} 条 -> 处理后 {len(deduplicated_data)} 条")
        return deduplicated_data
    
    async def _clean_data(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗数据 - 优化版，过滤空值和无意义字段
        
        Args:
            item: 原始数据项
            
        Returns:
            Dict[str, Any]: 清洗后的数据
        """
        cleaned_item = {}
        
        # 获取配置的导出字段列表
        export_fields = set(self.column_mapping.keys())
        
        for field, value in item.items():
            # 只处理配置中需要导出的字段，但保留某些处理器必需的字段
            if field not in export_fields and field not in ['merchant_type', 'price_type', 'activity_type', 'event_type', 'brand_id', 'iconIds', 'icon_list']:
                continue
                
            # 跳过完全空值字段，但保留有意义的0值
            if value is None or value == "" or value == "null" or value == "undefined":
                continue

            # 对于数字字段，0是有效值，不应跳过
            if field in ["price", "sales", "rating", "comment_count"] and value == 0:
                cleaned_item[field] = value
                continue

            # 跳过无意义的占位符值
            if isinstance(value, str):
                value_lower = value.lower().strip()
                if value_lower in ["", "null", "undefined", "none", "暂无", "无", "未知", "n/a"]:
                    continue
            
            # 文本字段清洗
            if field in ["goods_name", "category", "tags", "sales_tip", "special_text"]:
                cleaned_value = clean_text(str(value))
                if cleaned_value and len(cleaned_value.strip()) > 0:
                    cleaned_item[field] = cleaned_value
            
            # 价格字段清洗（新增多个价格字段）
            elif field in ["price", "original_price", "coupon_price", "market_price"]:
                price_value = format_price(value)
                if price_value and float(price_value) > 0:
                    cleaned_item[field] = price_value
            
            # 销量字段清洗
            elif field in ["sales", "comment_count"]:
                sales_value = format_sales(value)
                if sales_value is not None:
                    cleaned_item[field] = sales_value
            
            # URL字段清洗
            elif field in ["goods_url", "image_url", "hd_thumb_url"]:
                url_str = str(value).strip()
                if url_str and not url_str.startswith(('http://', 'https://')):
                    if url_str.startswith('//'):
                        url_str = 'https:' + url_str
                    elif url_str.startswith('/'):
                        base_url = self.config.get("target", {}).get("base_url", "https://mobile.yangkeduo.com")
                        url_str = base_url + url_str
                if url_str and len(url_str) > 10:  # 确保URL有效
                    cleaned_item[field] = url_str
            
            # 评分字段清洗
            elif field == "rating":
                try:
                    if isinstance(value, str):
                        # 提取数字
                        numbers = re.findall(r'\d+\.?\d*', value)
                        if numbers:
                            rating = float(numbers[0])
                            # 评分通常在0-5之间
                            if rating > 5:
                                rating = rating / 10  # 可能是10分制
                            if rating > 0:  # 只保留有效评分
                                cleaned_item[field] = f"{rating:.1f}"
                    elif isinstance(value, (int, float)):
                        rating = float(value)
                        if rating > 5:
                            rating = rating / 10
                        if rating > 0:
                            cleaned_item[field] = f"{rating:.1f}"
                except:
                    pass  # 跳过无效评分
            
            # 品牌名称字段
            elif field == "brand_name":
                brand_value = str(value).strip()
                # 跳过无意义的品牌名称
                if brand_value and not brand_value.startswith(("品牌_", "品牌ID", "未知品牌")):
                    cleaned_item[field] = brand_value

            # 编码字段对应的名称字段（文本格式）
            elif field in ["merchant_type_name", "activity_type_name", "price_type_name"]:
                name_value = str(value).strip()
                # 跳过无意义的类型名称
                if name_value and not name_value.startswith(("类型", "未知", "无")):
                    cleaned_item[field] = name_value
            
            # 标签字段清洗
            elif field == "tags":
                if isinstance(value, list):
                    tag_list = [str(v).strip() for v in value if v and str(v).strip()]
                    if tag_list:
                        cleaned_item[field] = ",".join(tag_list[:5])  # 最多保留5个标签
                elif isinstance(value, str):
                    tag_value = value.strip()
                    if tag_value and len(tag_value) > 0:
                        cleaned_item[field] = tag_value
            
            # 其他有效字段
            else:
                str_value = str(value).strip()
                if str_value and len(str_value) > 0:
                    cleaned_item[field] = str_value
        
        return cleaned_item
    
    async def _validate_data(self, item: Dict[str, Any]) -> bool:
        """
        验证数据
        
        Args:
            item: 数据项
            
        Returns:
            bool: 是否通过验证
        """
        # 检查必需字段
        for field in self.required_fields:
            if not item.get(field):
                logger.debug(f"缺少必需字段: {field}")
                return False
        
        # 验证商品ID格式（更宽松的验证）
        goods_id = item.get("goods_id", "")
        if not goods_id:
            logger.debug(f"商品ID为空")
            return False
        # 允许包含字母数字的ID（如 generated_xxx）
        if not re.match(r'^[\w\-_]+$', str(goods_id)):
            logger.debug(f"商品ID格式无效: {goods_id}")
            return False
        
        # 验证商品名称长度
        goods_name = item.get("goods_name", "")
        if len(goods_name) < 2 or len(goods_name) > 200:
            logger.debug(f"商品名称长度无效: {len(goods_name)}")
            return False
        
        # 验证价格
        price = item.get("price", "0")
        try:
            price_float = float(price)
            if price_float < 0 or price_float > 10000000:  # 允许更高价格（1万元以内）
                logger.debug(f"价格超出合理范围: {price_float}")
                return False
        except:
            logger.debug(f"价格格式无效: {price}")
            return False
        
        # 验证URL格式
        for url_field in self.url_fields:
            url = item.get(url_field, "")
            if url and not validate_url(url):
                logger.debug(f"URL格式无效: {url_field}={url}")
                # URL格式错误不阻止数据，只是警告
                item[url_field] = ""
        
        return True
    
    async def _format_data(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化数据
        
        Args:
            item: 数据项
            
        Returns:
            Dict[str, Any]: 格式化后的数据
        """
        formatted_item = item.copy()
        
        # 确保所有映射字段都存在
        for field in self.column_mapping.keys():
            if field not in formatted_item:
                formatted_item[field] = ""

        # 处理品牌名称映射和解码
        brand_id = formatted_item.get("brand_id", "")
        brand_name = formatted_item.get("brand_name", "")
        
        # 如果品牌名称无效，尝试从商品名称提取
        if not self._is_valid_brand_name(brand_name):
            goods_name = formatted_item.get("goods_name", "")
            extracted_brand = self._extract_brand_name_from_goods_name(goods_name, brand_id)
            if extracted_brand:
                brand_name = extracted_brand
        
        # 应用品牌ID解码和格式化
        formatted_item["brand_name"] = self._decode_brand_id(brand_id, brand_name)

        # 处理类型名称转换和ID解码
        # 价格类型
        price_type = formatted_item.get("price_type", 0)
        formatted_item["price_type_name"] = self._decode_price_type(price_type)
        
        # 活动类型
        activity_type = formatted_item.get("activity_type", 0)
        formatted_item["activity_type_name"] = self._decode_activity_type(activity_type)
        
        # 事件类型
        event_type = formatted_item.get("event_type", 0)
        formatted_item["event_type_name"] = self._get_event_type_name(event_type)
        
        # 商家类型
        merchant_type = formatted_item.get("merchant_type", 0)
        formatted_item["merchant_type_name"] = self._decode_merchant_type(merchant_type)

        # 处理商品链接
        if "goods_url" not in formatted_item or not formatted_item["goods_url"]:
            goods_id = formatted_item.get("goods_id", "")
            if goods_id:
                formatted_item["goods_url"] = f"https://mobile.yangkeduo.com/goods.html?goods_id={goods_id}"
        
        # 提取营销标签（清除可能的污染数据）
        # 先清除可能被错误设置的营销标签字段
        if "marketing_tags" in formatted_item:
            del formatted_item["marketing_tags"]
        
        marketing_tags = self.extract_marketing_tags(formatted_item)
        if marketing_tags:
            formatted_item["marketing_tags"] = ",".join(marketing_tags)
        else:
            formatted_item["marketing_tags"] = ""
        
        # 添加采集时间
        if "created_time" not in formatted_item or not formatted_item["created_time"]:
            formatted_item["created_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 格式化商品名称（移除多余空格和特殊字符）
        if "goods_name" in formatted_item:
            name = formatted_item["goods_name"]
            # 移除HTML标签
            name = re.sub(r'<[^>]+>', '', name)
            # 移除多余空格
            name = re.sub(r'\s+', ' ', name).strip()
            formatted_item["goods_name"] = name
        
        # 格式化店铺名称
        if "shop_name" in formatted_item:
            shop_name = formatted_item["shop_name"]
            if shop_name:
                shop_name = clean_text(shop_name)
                formatted_item["shop_name"] = shop_name
            else:
                formatted_item["shop_name"] = "未知店铺"
        
        # 格式化标签
        if "tags" in formatted_item:
            tags = formatted_item["tags"]
            if tags:
                # 分割标签并去重
                tag_list = []
                for tag in tags.split(','):
                    tag = tag.strip()
                    if tag and tag not in tag_list:
                        tag_list.append(tag)
                formatted_item["tags"] = ",".join(tag_list[:5])  # 最多保留5个标签

        # 🎯 添加补贴字段判断逻辑
        self._add_subsidy_fields(formatted_item)

        return formatted_item

    def _add_subsidy_fields(self, formatted_item: Dict[str, Any]) -> None:
        """
        添加补贴字段判断逻辑

        Args:
            formatted_item: 格式化后的数据项
        """
        # 🎯 优先使用API响应监听器中已经设置的补贴信息（更准确）
        existing_is_subsidy = formatted_item.get("is_subsidy")
        existing_is_government_subsidy = formatted_item.get("is_government_subsidy")
        existing_subsidy_info = formatted_item.get("subsidy_info", "")

        # 如果API监听器已经设置了补贴信息，优先使用
        if existing_is_subsidy is not None or existing_is_government_subsidy is not None:
            # API监听器已经处理过，保持原有值
            is_subsidy = existing_is_subsidy if existing_is_subsidy is not None else False
            is_government_subsidy = existing_is_government_subsidy if existing_is_government_subsidy is not None else False
            subsidy_info = existing_subsidy_info
            
            # 调试日志：跟踪API监听器设置的值
            if is_subsidy or is_government_subsidy:
                logger.info(f"✅ 使用API监听器设置的补贴信息 - 商品: {formatted_item.get('goods_name', '')[:30]}... "
                           f"百亿补贴: {is_subsidy}, 国补: {is_government_subsidy}, "
                           f"详情: {subsidy_info}")
        else:
            # API监听器未处理，使用备用判断逻辑
            price_type = formatted_item.get("price_type", 0)
            goods_name = formatted_item.get("goods_name", "")
            activity_type_name = formatted_item.get("activity_type_name", "")
            price_type_name = formatted_item.get("price_type_name", "")

            # 🎯 修复：检查icon_ids字段（使用下划线命名，不是驼峰命名）
            icon_ids = formatted_item.get("icon_ids", "")  # 注意：是icon_ids，不是iconIds
            icon_ids_list = []
            
            if isinstance(icon_ids, str) and icon_ids:
                # 如果是字符串格式，尝试解析
                try:
                    # icon_ids可能是逗号分隔的字符串，如"20001,10014"
                    if ',' in icon_ids:
                        icon_ids_list = [int(id.strip()) for id in icon_ids.split(',') if id.strip().isdigit()]
                    # 或者是JSON数组格式
                    elif icon_ids.startswith('['):
                        import json
                        icon_ids_list = json.loads(icon_ids)
                    # 或者是单个数字
                    elif icon_ids.isdigit():
                        icon_ids_list = [int(icon_ids)]
                except Exception as e:
                    logger.debug(f"解析icon_ids失败: {e}, icon_ids={icon_ids}")
                    icon_ids_list = []
            elif isinstance(icon_ids, list):
                icon_ids_list = icon_ids
            
            # 判断是否为百亿补贴
            # 优先检查iconIds（最准确），然后检查文本内容
            is_subsidy = (
                20001 in icon_ids_list or  # iconId=20001 是百亿补贴的标准标识
                "百亿补贴" in activity_type_name or
                "百亿补贴" in price_type_name or
                "百亿补贴" in goods_name
            )
            
            # 调试日志
            if icon_ids_list and 20001 in icon_ids_list:
                logger.info(f"✅ 通过iconId=20001识别到百亿补贴: {formatted_item.get('goods_name', '')[:30]}...")
            elif is_subsidy:
                logger.info(f"✅ 通过文本内容识别到百亿补贴: {formatted_item.get('goods_name', '')[:30]}...")

            # 判断是否为国补商品
            # 检查商品名称中是否包含国补相关关键词
            government_subsidy_keywords = ["国补", "国家补贴", "政府补贴", "以旧换新", "家电下乡"]
            is_government_subsidy = any(keyword in goods_name for keyword in government_subsidy_keywords)

            # 生成补贴详情信息
            subsidy_info_parts = []
            if is_subsidy:
                subsidy_info_parts.append("百亿补贴")
            if is_government_subsidy:
                # 找出具体的国补关键词
                found_keywords = [keyword for keyword in government_subsidy_keywords if keyword in goods_name]
                if found_keywords:
                    subsidy_info_parts.append(f"国补({','.join(found_keywords)})")
                else:
                    subsidy_info_parts.append("国补")

            subsidy_info = " + ".join(subsidy_info_parts) if subsidy_info_parts else ""

        # 设置最终的补贴字段
        formatted_item["is_subsidy"] = is_subsidy
        formatted_item["is_government_subsidy"] = is_government_subsidy
        formatted_item["subsidy_info"] = subsidy_info
        
        # 调试日志：追踪补贴字段的最终值
        if is_subsidy or is_government_subsidy:
            logger.info(f"📊 最终补贴字段设置 - 商品: {formatted_item.get('goods_name', '')[:30]}... "
                       f"百亿补贴: {is_subsidy}, 国补: {is_government_subsidy}, "
                       f"详情: {subsidy_info}")



    def _extract_brand_name_from_goods_name(self, goods_name: str, brand_id: str) -> str:
        """
        从商品名称中提取品牌名称 - RapidFuzz增强版
        优先识别子品牌，支持中英文混合识别，提高准确率和性能
        """
        if not goods_name:
            return ""

        # 预处理商品名称 - 清理特殊字符和营销标签
        goods_name_clean = self._clean_goods_name_for_brand_extraction(goods_name)

        # 使用RapidFuzz进行高性能品牌识别
        if RAPIDFUZZ_AVAILABLE:
            return self._extract_brand_with_rapidfuzz(goods_name_clean, brand_id)
        else:
            # 回退到传统方法
            return self._extract_brand_traditional(goods_name_clean, brand_id)

    def _extract_brand_with_rapidfuzz(self, goods_name_clean: str, brand_id: str) -> str:
        """
        使用RapidFuzz进行高性能品牌识别 - 增强子品牌优先识别
        """
        # 第一层：子品牌专用精确匹配（最高优先级）
        sub_brand_match = self._sub_brand_priority_match(goods_name_clean)
        if sub_brand_match:
            return sub_brand_match

        # 第二层：精确匹配（优先级高）
        exact_match = self._exact_brand_match_rapidfuzz(goods_name_clean)
        if exact_match:
            return exact_match

        # 第三层：模糊匹配（处理变体和错别字）
        fuzzy_match = self._fuzzy_brand_match_rapidfuzz(goods_name_clean)
        if fuzzy_match:
            return fuzzy_match

        # 第四层：部分匹配（处理复杂商品名称）
        partial_match = self._partial_brand_match_rapidfuzz(goods_name_clean)
        if partial_match:
            return partial_match

        # 第五层：从品牌ID映射表获取
        if brand_id and str(brand_id) in self.brand_id_mapping:
            return self.brand_id_mapping[str(brand_id)]

        return ""

    def _sub_brand_priority_match(self, goods_name: str) -> str:
        """
        子品牌优先匹配 - 解决子品牌被主品牌覆盖的问题
        当商品名称同时包含主品牌和子品牌时，优先识别子品牌
        """
        if not hasattr(self, 'sub_brand_keywords') or not self.sub_brand_keywords:
            return ""

        # 检查是否包含子品牌关键词
        found_sub_brands = []

        for keyword in self.sub_brand_keywords:
            # 使用多种匹配策略检查子品牌
            if (keyword in goods_name or
                keyword.lower() in goods_name.lower() or
                keyword.upper() in goods_name.upper()):

                brand = self.keyword_to_brand[keyword]

                # 使用RapidFuzz验证匹配质量
                score = fuzz.partial_ratio(keyword, goods_name)
                if score >= 85:  # 高质量匹配
                    found_sub_brands.append((keyword, brand, score))
                    logger.debug(f"发现子品牌候选: {brand} (关键词: {keyword}, 得分: {score})")

        if found_sub_brands:
            # 按匹配得分排序，选择最佳匹配
            found_sub_brands.sort(key=lambda x: x[2], reverse=True)
            best_keyword, best_brand, best_score = found_sub_brands[0]

            main_brand = self.sub_brand_mapping[best_brand]
            logger.info(f"子品牌优先匹配成功: {best_brand} (主品牌: {main_brand}, 得分: {best_score})")
            return self._format_brand_display(best_brand, main_brand)

        return ""

    def _exact_brand_match_rapidfuzz(self, goods_name: str) -> str:
        """
        使用RapidFuzz进行精确品牌匹配
        注意：此方法只匹配主品牌，子品牌已在优先级更高的方法中处理
        """
        # 检查是否已经包含子品牌，如果是则跳过主品牌匹配
        if self._contains_sub_brand(goods_name):
            logger.debug("商品名称包含子品牌，跳过主品牌精确匹配")
            return ""

        # 匹配主品牌
        if hasattr(self, 'main_brand_keywords') and self.main_brand_keywords:
            match_result = process.extractOne(
                goods_name,
                self.main_brand_keywords,
                scorer=fuzz.ratio,
                score_cutoff=90  # 精确匹配阈值
            )

            if match_result:
                matched_keyword, score, _ = match_result
                brand = self.keyword_to_brand[matched_keyword]
                logger.debug(f"RapidFuzz精确匹配主品牌: {brand} (关键词: {matched_keyword}, 得分: {score})")
                return brand

        return ""

    def _contains_sub_brand(self, goods_name: str) -> bool:
        """
        检查商品名称是否包含子品牌关键词
        """
        if not hasattr(self, 'sub_brand_keywords') or not self.sub_brand_keywords:
            return False

        goods_name_lower = goods_name.lower()
        for keyword in self.sub_brand_keywords:
            if (keyword in goods_name or
                keyword.lower() in goods_name_lower or
                keyword.upper() in goods_name.upper()):
                return True
        return False

    def _fuzzy_brand_match_rapidfuzz(self, goods_name: str) -> str:
        """
        使用RapidFuzz进行模糊品牌匹配
        处理错别字、变体等情况，优先匹配子品牌
        """
        if not hasattr(self, 'all_brand_keywords') or not self.all_brand_keywords:
            return ""

        # 使用多种匹配策略
        strategies = [
            (fuzz.token_sort_ratio, 85, "token_sort"),  # 词序无关匹配
            (fuzz.token_set_ratio, 85, "token_set"),    # 集合匹配
            (fuzz.partial_ratio, 90, "partial"),        # 部分匹配
            (fuzz.WRatio, 85, "weighted")               # 加权匹配
        ]

        best_sub_brand_match = None
        best_main_brand_match = None
        best_sub_score = 0
        best_main_score = 0
        best_strategy = ""

        for scorer, threshold, strategy_name in strategies:
            match_result = process.extractOne(
                goods_name,
                self.all_brand_keywords,
                scorer=scorer,
                score_cutoff=threshold
            )

            if match_result and match_result[1] > 0:
                matched_keyword, score, _ = match_result
                brand = self.keyword_to_brand[matched_keyword]

                # 分别记录子品牌和主品牌的最佳匹配
                if brand in self.sub_brand_mapping:
                    if score > best_sub_score:
                        best_sub_brand_match = match_result
                        best_sub_score = score
                        best_strategy = strategy_name
                else:
                    if score > best_main_score:
                        best_main_brand_match = match_result
                        best_main_score = score

        # 优先返回子品牌匹配结果
        if best_sub_brand_match:
            matched_keyword, score, _ = best_sub_brand_match
            brand = self.keyword_to_brand[matched_keyword]
            main_brand = self.sub_brand_mapping[brand]
            logger.debug(f"RapidFuzz模糊匹配子品牌: {brand} (关键词: {matched_keyword}, 得分: {score}, 策略: {best_strategy})")
            return self._format_brand_display(brand, main_brand)

        # 如果没有子品牌匹配，返回主品牌匹配
        elif best_main_brand_match:
            matched_keyword, score, _ = best_main_brand_match
            brand = self.keyword_to_brand[matched_keyword]
            logger.debug(f"RapidFuzz模糊匹配主品牌: {brand} (关键词: {matched_keyword}, 得分: {score})")
            return brand

        return ""

    def _partial_brand_match_rapidfuzz(self, goods_name: str) -> str:
        """
        使用RapidFuzz进行部分品牌匹配
        处理复杂商品名称中的品牌提取
        """
        if not hasattr(self, 'all_brand_keywords') or not self.all_brand_keywords:
            return ""

        # 将商品名称分词，逐个词进行匹配
        words = re.findall(r'[\w]+', goods_name)

        # 优先匹配较长的词
        words.sort(key=len, reverse=True)

        for word in words:
            if len(word) >= 2:  # 过滤太短的词
                # 对单个词进行高精度匹配
                match_result = process.extractOne(
                    word,
                    self.all_brand_keywords,
                    scorer=fuzz.ratio,
                    score_cutoff=95  # 部分匹配需要更高精度
                )

                if match_result:
                    matched_keyword, score, _ = match_result
                    brand = self.keyword_to_brand[matched_keyword]
                    logger.debug(f"RapidFuzz部分匹配: {brand} (词: {word}, 关键词: {matched_keyword}, 得分: {score})")

                    # 如果是子品牌，返回格式化显示
                    if brand in self.sub_brand_mapping:
                        main_brand = self.sub_brand_mapping[brand]
                        return self._format_brand_display(brand, main_brand)
                    else:
                        return brand

        return ""

    def _extract_brand_traditional(self, goods_name_clean: str, brand_id: str) -> str:
        """
        传统品牌识别方法（RapidFuzz不可用时的回退方案）
        """
        goods_name_upper = goods_name_clean.upper()
        goods_name_lower = goods_name_clean.lower()

        # 第一轮：优先匹配子品牌（更具体的品牌）
        sub_brand_keywords = []
        main_brand_keywords = []

        # 分离子品牌和主品牌关键词
        for keyword, brand in self.brand_keywords.items():
            if brand in self.sub_brand_mapping:
                sub_brand_keywords.append((keyword, brand))
            else:
                main_brand_keywords.append((keyword, brand))

        # 按长度排序，优先匹配长关键词
        sub_brand_keywords.sort(key=lambda x: len(x[0]), reverse=True)
        main_brand_keywords.sort(key=lambda x: len(x[0]), reverse=True)

        # 优先匹配子品牌
        for keyword, brand in sub_brand_keywords:
            if len(keyword) >= 2:
                if self._is_brand_match_strict(keyword, goods_name_clean, goods_name_upper, goods_name_lower):
                    main_brand = self.sub_brand_mapping[brand]
                    logger.debug(f"传统方法识别到子品牌: {brand} (主品牌: {main_brand})")
                    return self._format_brand_display(brand, main_brand)

        # 然后匹配主品牌
        for keyword, brand in main_brand_keywords:
            if len(keyword) >= 2:
                has_sub_brand = any(
                    self._is_brand_match_strict(sub_keyword, goods_name_clean, goods_name_upper, goods_name_lower)
                    for sub_keyword, _ in sub_brand_keywords
                    if len(sub_keyword) >= 2
                )
                if not has_sub_brand and self._is_brand_match(keyword, goods_name_clean, goods_name_upper, goods_name_lower):
                    return brand

        # 模糊匹配
        fuzzy_match = self._fuzzy_brand_match(goods_name_clean)
        if fuzzy_match:
            brand = fuzzy_match
            if brand in self.sub_brand_mapping:
                main_brand = self.sub_brand_mapping[brand]
                return self._format_brand_display(brand, main_brand)
            else:
                return brand

        # 从品牌ID映射表获取
        if brand_id and str(brand_id) in self.brand_id_mapping:
            return self.brand_id_mapping[str(brand_id)]

        return ""

    def _clean_goods_name_for_brand_extraction(self, goods_name: str) -> str:
        """
        清理商品名称，移除干扰品牌识别的内容
        """
        # 移除常见的营销标签和特殊字符，但保留品牌信息
        marketing_patterns = [
            r'【[^】]*】',  # 【百亿补贴】等
            r'\[[^\]]*\]',  # [限时特价]等
            r'[★☆⭐]+',    # 星号
            r'[❤♥💖]+',    # 爱心
            r'[🔥💥⚡]+',   # 火焰等emoji
            r'限时(?![^，。]*[美海格小华云石追戴])',  # 限时但不包含品牌
            r'特价(?![^，。]*[美海格小华云石追戴])',  # 特价但不包含品牌
            r'包邮(?![^，。]*[美海格小华云石追戴])',  # 包邮但不包含品牌
            r'正品(?![^，。]*[美海格小华云石追戴])',  # 正品但不包含品牌
            r'官方(?![^，。]*[美海格小华云石追戴])',  # 官方但不包含品牌
        ]
        
        cleaned_name = goods_name
        for pattern in marketing_patterns:
            cleaned_name = re.sub(pattern, ' ', cleaned_name)
        
        # 单独处理感叹号 - 只移除不包含品牌的感叹号部分
        cleaned_name = re.sub(r'[！!]+(?![^，。]*[美海格小华云石追戴])', ' ', cleaned_name)
        
        # 移除多余空格
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()
        
        return cleaned_name
    
    def _is_brand_match(self, keyword: str, goods_name: str, goods_name_upper: str, goods_name_lower: str = None) -> bool:
        """
        检查品牌关键词是否匹配商品名称 - 增强版
        支持多种匹配模式，提高识别准确率
        """
        if goods_name_lower is None:
            goods_name_lower = goods_name.lower()

        keyword_upper = keyword.upper()
        keyword_lower = keyword.lower()

        # 1. 精确匹配（原始、大写、小写）
        if (keyword in goods_name or
            keyword_upper in goods_name_upper or
            keyword_lower in goods_name_lower):
            return True

        # 2. 词边界匹配（避免部分匹配误判）
        try:
            import re
            # 匹配词边界，确保是完整的品牌名
            pattern = r'\b' + re.escape(keyword) + r'\b'
            if re.search(pattern, goods_name, re.IGNORECASE):
                return True
        except:
            pass

        # 3. 带分隔符的匹配（如 "海尔/Haier"、"【统帅】"）
        separators = ['/', '-', '·', '•', '|', '\\', '【', '】', '[', ']', '(', ')', '（', '）']
        for sep in separators:
            patterns = [
                f"{keyword}{sep}",
                f"{sep}{keyword}",
                f"{keyword_upper}{sep}",
                f"{sep}{keyword_upper}",
                f"{keyword_lower}{sep}",
                f"{sep}{keyword_lower}"
            ]
            for pattern in patterns:
                if pattern in goods_name or pattern in goods_name_upper or pattern in goods_name_lower:
                    return True

        # 4. 开头匹配（品牌通常在商品名开头）
        if (goods_name.startswith(keyword) or
            goods_name_upper.startswith(keyword_upper) or
            goods_name_lower.startswith(keyword_lower)):
            return True

        return False

    def _is_brand_match_strict(self, keyword: str, goods_name: str, goods_name_upper: str, goods_name_lower: str = None) -> bool:
        """
        严格的品牌关键词匹配 - 用于子品牌优先识别

        Args:
            keyword: 品牌关键词
            goods_name: 商品名称
            goods_name_upper: 大写商品名称
            goods_name_lower: 小写商品名称

        Returns:
            bool: 是否匹配
        """
        if goods_name_lower is None:
            goods_name_lower = goods_name.lower()

        keyword_upper = keyword.upper()
        keyword_lower = keyword.lower()

        # 1. 精确匹配（优先级最高）
        if keyword in goods_name or keyword_upper in goods_name_upper or keyword_lower in goods_name_lower:
            return True

        # 2. 词边界匹配（确保完整匹配）
        try:
            import re
            pattern = r'\b' + re.escape(keyword) + r'\b'
            if re.search(pattern, goods_name, re.IGNORECASE):
                return True
        except:
            pass

        # 3. 开头匹配（品牌通常在开头）
        if (goods_name.startswith(keyword) or
            goods_name_upper.startswith(keyword_upper) or
            goods_name_lower.startswith(keyword_lower)):
            return True

        return False

    def _fuzzy_brand_match(self, goods_name: str) -> str:
        """
        模糊匹配品牌名称（处理错别字等）
        """
        # 简单的编辑距离匹配（针对常见错别字）
        for brand in self.brand_keywords.values():
            if len(brand) >= 2:
                # 检查是否存在1个字符的差异
                if self._is_similar_brand(brand, goods_name):
                    return brand
        
        return ""
    
    def _is_similar_brand(self, brand: str, goods_name: str) -> bool:
        """
        检查品牌名称是否与商品名称相似（允许1个字符差异）
        """
        if len(brand) < 2:
            return False
        
        # 在商品名称中查找相似的子串
        for i in range(len(goods_name) - len(brand) + 1):
            substring = goods_name[i:i + len(brand)]
            if self._calculate_similarity(brand, substring) >= 0.8:
                return True
        
        return False
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度
        """
        if len(str1) != len(str2):
            return 0.0
        
        if str1 == str2:
            return 1.0
        
        # 计算字符匹配度
        matches = sum(1 for a, b in zip(str1, str2) if a == b)
        return matches / len(str1)

    
    def _init_marketing_system(self):
        """初始化营销信息识别系统 - 增强版"""
        # 营销标签优先级和分类
        self.marketing_categories = {
            "补贴活动": {
                "priority": 1,
                "keywords": ["百亿补贴", "万人团", "品牌补贴", "官方补贴", "平台补贴"],
                "patterns": [r"百亿补贴", r"万人团", r"品牌补贴"]
            },
            "限时活动": {
                "priority": 2, 
                "keywords": ["限时特价", "限时抢购", "限时折扣", "秒杀", "抢购", "闪购", "限时", "特价"],
                "patterns": [r"限时[\u7279\u4ef7\u62a2\u8d2d\u6298\u6263]*", r"秒杀", r"[0-9]+小时[\u7279\u4ef7\u62a2\u8d2d]*"]
            },
            "会员专享": {
                "priority": 3,
                "keywords": ["会员专享", "会员价", "VIP专享", "PLUS会员", "超级会员", "会员"],
                "patterns": [r"会员[\u4e13\u4eab\u4ef7]*", r"VIP[\u4e13\u4eab]*", r"PLUS[\u4f1a\u5458]*"]
            },
            "新人优惠": {
                "priority": 4,
                "keywords": ["新人专享", "新人价", "新用户", "首单", "新客", "新人"],
                "patterns": [r"新人[\u4e13\u4eab\u4ef7]*", r"新用户[\u4e13\u4eab]*", r"首单[\u4f18\u60e0]*"]
            },
            "满减优惠": {
                "priority": 5,
                "keywords": ["满减", "满送", "买赠", "第二件", "多买优惠"],
                "patterns": [r"满[0-9]+减[0-9]+", r"买[0-9]+送[0-9]+", r"第二件[\u534a\u4ef7\u514d\u8d39]*"]
            },
            "品牌活动": {
                "priority": 6,
                "keywords": ["品牌日", "官方旗舰", "品牌直销", "厂家直销", "品牌特卖", "官方", "旗舰", "正品", "保证"],
                "patterns": [r"品牌[\u65e5\u7279\u5356\u76f4\u9500]*", r"官方[\u65d7\u8230\u76f4\u9500]*", r"厂家直销"]
            },
            "节日促销": {
                "priority": 7,
                "keywords": ["双11", "双12", "618", "年货节", "春节", "国庆", "中秋"],
                "patterns": [r"双[0-9]+", r"[0-9]+\.[0-9]+", r"[\u6625\u8282\u56fd\u5e86\u4e2d\u79cb\u5e74\u8d27]+节"]
            },
            "包邮服务": {
                "priority": 8,
                "keywords": ["包邮", "免邮", "邮费", "运费"],
                "patterns": [r"[\u5305\u514d][\u90ae\u8d39]*", r"[\u8fd0\u90ae]费[\u5305\u514d]*"]
            },
            "热销推荐": {
                "priority": 9,
                "keywords": ["热销", "爆款", "推荐", "精选", "好评", "口碑", "畅销"],
                "patterns": [r"[\u70ed\u7206][\u9500\u6b3e]*", r"[\u63a8\u8350\u7cbe\u9009]*"]
            },
            "价格优惠": {
                "priority": 10,
                "keywords": ["直降", "立减", "折扣", "优惠", "特惠", "降价", "减价"],
                "patterns": [r"[\u76f4\u7acb][\u964d\u51cf]*", r"[\u6298\u6263\u4f18\u60e0]*"]
            }
        }

    def _init_product_filter(self):
        """初始化商品筛选器（支持统计型和基础型）"""
        try:
            # 获取筛选器配置
            filter_config = self.config.get('product_filter', {})
            
            # 检查是否启用统计型筛选器
            statistical_config = filter_config.get('statistical', {})
            if statistical_config.get('enabled', True):
                # 使用统计型筛选器
                from ..filters.statistical_product_filter import create_statistical_product_filter
                self.product_filter = create_statistical_product_filter(filter_config, brand_processor=self)
                logger.info("统计型商品筛选器已启用 (支持智能产品类型识别和过滤)")
            else:
                # 使用基础筛选器
                from ..filters import create_product_filter
                self.product_filter = create_product_filter(filter_config, brand_processor=self)
                logger.info("基础型商品筛选器已启用")
            
            if not filter_config.get('enabled', False):
                logger.info("商品筛选器已禁用")
                
        except ImportError as e:
            logger.warning(f"无法导入商品筛选器模块: {e}")
            self.product_filter = None
        except Exception as e:
            logger.error(f"初始化商品筛选器失败: {e}")
            self.product_filter = None
    
    def extract_marketing_tags(self, goods_data: Dict[str, Any]) -> List[str]:
        """
        提取商品的营销标签 - 增强版
        
        Args:
            goods_data: 商品数据
            
        Returns:
            List[str]: 营销标签列表，按优先级排序
        """
        if not hasattr(self, 'marketing_categories'):
            self._init_marketing_system()
        
        marketing_tags = []
        
        # 从更多字段提取营销信息，包括价格类型等
        text_fields = [
            goods_data.get("goods_name", ""),
            goods_data.get("special_text", ""),
            goods_data.get("sales_tip", ""),
            goods_data.get("tags", ""),
            goods_data.get("activity_type_name", ""),
            goods_data.get("price_type_name", ""),
            goods_data.get("tag_list", ""),
            goods_data.get("prop_tag_list", ""),
            goods_data.get("merchant_type_name", ""),  # 新增：商家类型
            goods_data.get("event_type_name", ""),     # 新增：事件类型
        ]
        
        # 过滤和清理文本字段
        cleaned_texts = []
        for field in text_fields:
            if field and isinstance(field, str):
                # 放宽过滤条件，保留更多营销信息
                if not self._is_invalid_marketing_content(field):
                    cleaned_texts.append(field)
        
        combined_text = " ".join(cleaned_texts)
        
        # 按优先级检查营销标签
        found_tags = []
        for category, info in self.marketing_categories.items():
            category_tags = self._extract_category_tags(combined_text, info)
            if category_tags:
                found_tags.extend([(tag, info["priority"]) for tag in category_tags])
        
        # 额外检查：从价格类型中提取营销信息
        price_type_name = goods_data.get("price_type_name", "")
        if price_type_name and "价格" not in price_type_name:
            # 如果价格类型不是普通价格，可能包含营销信息
            if price_type_name in ["拼团价格", "秒杀价格", "会员价格", "新人价格"]:
                marketing_word = price_type_name.replace("价格", "")
                if marketing_word and self._is_valid_marketing_tag(marketing_word):
                    found_tags.append((marketing_word, 3))
        
        # 额外检查：从活动类型中提取营销信息
        activity_type_name = goods_data.get("activity_type_name", "")
        if activity_type_name and activity_type_name not in ["无活动", "普通商品"]:
            if self._is_valid_marketing_tag(activity_type_name):
                found_tags.append((activity_type_name, 2))
        
        # 额外检查：从商家类型中提取营销信息
        merchant_type_name = goods_data.get("merchant_type_name", "")
        if merchant_type_name and "旗舰" in merchant_type_name:
            found_tags.append(("官方旗舰", 6))
        
        # 按优先级排序并去重
        found_tags.sort(key=lambda x: x[1])
        unique_tags = []
        seen_tags = set()
        
        for tag, priority in found_tags:
            if tag not in seen_tags and self._is_valid_marketing_tag(tag):
                unique_tags.append(tag)
                seen_tags.add(tag)
        
        # 如果没有找到任何营销标签，尝试从商品名称中提取基础信息
        if not unique_tags:
            goods_name = goods_data.get("goods_name", "")
            if goods_name:
                # 检查是否包含基础营销词汇
                basic_marketing_words = ["正品", "包邮", "官方", "品牌", "热销", "推荐"]
                for word in basic_marketing_words:
                    if word in goods_name and word not in seen_tags:
                        unique_tags.append(word)
                        seen_tags.add(word)
                        if len(unique_tags) >= 3:  # 最多添加3个基础标签
                            break
        
        return unique_tags[:5]  # 最多返回5个标签  # 最多返回5个标签
    
    def _is_invalid_marketing_content(self, content: str) -> bool:
        """
        检查内容是否为无效的营销内容 - 放宽过滤条件
        
        Args:
            content: 待检查的内容
            
        Returns:
            bool: 是否为无效内容
        """
        if not content or not isinstance(content, str):
            return True
        
        content = content.strip()
        
        # 过滤空内容
        if not content:
            return True
        
        # 过滤纯数字或主要是数字的内容
        if re.match(r'^[\d\.,]+$', content):
            return True
        
        # 只过滤明显的价格格式（不包含营销关键词的纯价格）
        if re.match(r'^\d+\.\d+$', content) and not any(keyword in content for keyword in [
            '百亿', '补贴', '限时', '特价', '抢购', '秒杀', '会员', '新人', '满减', '包邮', '品牌日', '双11', '618', '折', '减', '送', '免', '正品', '官方'
        ]):
            return True
        
        # 过滤时间格式（如"4小时"、"24小时"）
        if re.match(r'^\d+小时$', content):
            return True
        
        # 大幅减少无意义词汇过滤，只过滤明显无关的词
        invalid_words = [
            '发货', '现货', '库存', '预售', '定制', '尺寸', '规格', '型号'
        ]

        if content in invalid_words:
            return True

        # 只过滤完全由无意义词汇组成的内容
        words = content.replace(',', ' ').split()
        if len(words) > 1 and all(word in invalid_words for word in words if word):
            return True
        
        return False
    
    def _is_valid_marketing_tag(self, tag: str) -> bool:
        """
        检查是否为有效的营销标签 - 放宽验证条件
        
        Args:
            tag: 营销标签
            
        Returns:
            bool: 是否有效
        """
        if not tag or not isinstance(tag, str):
            return False
        
        tag = tag.strip()
        
        # 标签长度检查
        if len(tag) < 2 or len(tag) > 20:
            return False
        
        # 不能是纯数字
        if tag.isdigit():
            return False
        
        # 不能是纯小数
        if re.match(r'^\d+\.\d+$', tag):
            return False
        
        # 过滤时间格式
        if re.match(r'^\d+小时$', tag):
            return False
        
        # 过滤明显无意义的单词
        invalid_single_words = [
            '品牌', '运费', '发货', '现货', '库存', '预售',
            '定制', '尺寸', '颜色', '规格', '型号'
        ]
        
        if tag in invalid_single_words:
            return False
        
        # 放宽营销关键词验证 - 增加更多有效标签
        valid_marketing_keywords = [
            # 中文营销词
            '百亿', '补贴', '限时', '特价', '抢购', '秒杀', '会员', '新人', 
            '满减', '包邮', '免邮', '品牌日', '节日', '促销', '折扣', '优惠',
            '专享', '专属', '特惠', '清仓', '甩卖', '直降', '立减',
            '正品', '保证', '保障', '官方', '旗舰', '授权', '直营',
            '热销', '爆款', '推荐', '精选', '好评', '口碑',
            
            # 节日和活动
            '双11', '双12', '618', '年货', '春节', '国庆', '中秋',
            '元旦', '情人节', '母亲节', '父亲节', '儿童节',
            
            # 英文营销词
            'sale', 'off', 'discount', 'special', 'limited', 'exclusive',
            'free', 'plus', 'vip', 'premium', 'pro'
        ]
        
        # 检查是否包含有效营销关键词
        tag_lower = tag.lower()
        has_valid_keyword = any(keyword in tag or keyword in tag_lower for keyword in valid_marketing_keywords)
        
        # 如果包含中文字符，更宽松一些
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', tag))
        
        if has_chinese and has_valid_keyword:
            return True
        elif not has_chinese and has_valid_keyword:
            return True
        elif has_chinese and len(tag) >= 2:
            # 对于中文标签，如果长度合适且不在无效词列表中，也认为有效
            return True
        else:
            return False  # 最多返回5个标签
    
    def _extract_category_tags(self, text: str, category_info: Dict) -> List[str]:
        """
        从文本中提取特定类别的营销标签
        """
        found_tags = []
        
        # 关键词匹配
        for keyword in category_info["keywords"]:
            if keyword in text:
                found_tags.append(keyword)
        
        # 正则表达式匹配
        for pattern in category_info["patterns"]:
            matches = re.findall(pattern, text)
            found_tags.extend(matches)
        
        return list(set(found_tags))  # 去重
    
    def get_marketing_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取营销活动统计信息
        
        Args:
            data: 商品数据列表
            
        Returns:
            Dict[str, Any]: 营销统计信息
        """
        if not data:
            return {"total_products": 0}
        
        total_products = len(data)
        marketing_distribution = {}
        category_distribution = {}
        
        for item in data:
            marketing_tags = item.get("marketing_tags", [])
            if isinstance(marketing_tags, str):
                marketing_tags = [tag.strip() for tag in marketing_tags.split(",") if tag.strip()]
            
            for tag in marketing_tags:
                # 统计具体标签分布
                if tag in marketing_distribution:
                    marketing_distribution[tag] += 1
                else:
                    marketing_distribution[tag] = 1
                
                # 统计类别分布
                category = self._get_tag_category(tag)
                if category in category_distribution:
                    category_distribution[category] += 1
                else:
                    category_distribution[category] = 1
        
        # 排序
        sorted_marketing = sorted(marketing_distribution.items(), key=lambda x: x[1], reverse=True)
        sorted_categories = sorted(category_distribution.items(), key=lambda x: x[1], reverse=True)
        
        products_with_marketing = sum(1 for item in data if item.get("marketing_tags"))
        marketing_coverage = (products_with_marketing / total_products * 100) if total_products > 0 else 0
        
        return {
            "total_products": total_products,
            "products_with_marketing": products_with_marketing,
            "marketing_coverage": round(marketing_coverage, 2),
            "marketing_distribution": dict(sorted_marketing[:15]),
            "category_distribution": dict(sorted_categories),
            "unique_marketing_tags": len(marketing_distribution),
            "unique_categories": len(category_distribution)
        }
    
    def _get_tag_category(self, tag: str) -> str:
        """获取营销标签所属类别"""
        if not hasattr(self, 'marketing_categories'):
            self._init_marketing_system()
        
        for category, info in self.marketing_categories.items():
            if tag in info["keywords"]:
                return category
            for pattern in info["patterns"]:
                if re.search(pattern, tag):
                    return category
        
        return "其他营销"
    
    def _format_brand_display(self, sub_brand: str, main_brand: str) -> str:
        """
        格式化品牌显示名称
        显示格式：子品牌(主品牌) 或 主品牌

        Args:
            sub_brand: 子品牌名称
            main_brand: 主品牌名称

        Returns:
            str: 格式化后的品牌显示名称
        """
        # 英文品牌名称到中文的映射（扩展版）
        en_to_cn_mapping = {
            # 海尔集团
            "Leader": "统帅",
            "Casarte": "卡萨帝",
            "WAHIN": "华凌",
            "Haier": "海尔",

            # 美的集团
            "LittleSwan": "小天鹅",
            "COLMO": "COLMO",
            "Midea": "美的",
            "TOSHIBA": "东芝",
            "KUKA": "库卡",

            # 格力集团
            "TOSOT": "大松",
            "GREE": "格力",
            "KINGHOME": "晶弘",

            # 海信集团
            "Hisense": "海信",
            "Kelon": "科龙",
            "Ronshen": "容声",

            # TCL集团
            "TCL": "TCL",
            "XESS": "XESS",
            "Thunder": "雷鸟",

            # 长虹集团
            "Changhong": "长虹",
            "MeiLing": "美菱",
            "HuaYi": "华意",

            # 创维集团
            "Skyworth": "创维",
            "Coocaa": "酷开",

            # 小米集团
            "Xiaomi": "小米",
            "Redmi": "红米",
            "MIJIA": "米家",
            "POCO": "POCO",
            "BlackShark": "黑鲨",

            # 华为集团
            "HUAWEI": "华为",
            "Honor": "荣耀",
            "HiLink": "智选",

            # 其他品牌
            "Samsung": "三星",
            "LG": "LG",
            "Sony": "索尼",
            "Panasonic": "松下",
            "Sharp": "夏普",
            "Hitachi": "日立",
            "Siemens": "西门子",
            "Bosch": "博世",
            "Whirlpool": "惠而浦",
            "Electrolux": "伊莱克斯"
        }

        # 转换英文品牌名称为中文
        display_sub_brand = en_to_cn_mapping.get(sub_brand, sub_brand)
        display_main_brand = en_to_cn_mapping.get(main_brand, main_brand)

        # 🎯 修复显示格式：子品牌显示为"子品牌(主品牌)"格式
        if display_sub_brand == display_main_brand:
            # 如果子品牌和主品牌相同，直接显示主品牌
            return display_main_brand
        elif display_sub_brand and display_main_brand and display_sub_brand != display_main_brand:
            # 如果识别出不同的子品牌，只显示子品牌名称
            return display_sub_brand
        elif display_sub_brand:
            # 只有子品牌，直接显示
            return display_sub_brand
        elif display_main_brand:
            # 只有主品牌，直接显示
            return display_main_brand
        else:
            # 都没有，返回空字符串
            return ""

    def _identify_sub_and_main_brand(self, brand_name: str) -> tuple:
        """
        识别子品牌和主品牌关系
        
        Args:
            brand_name: 品牌名称
            
        Returns:
            tuple: (子品牌, 主品牌) 或 (None, None)
        """
        # 子品牌到主品牌的映射关系
        sub_to_main_brand_mapping = {
            # 海尔集团
            "统帅": "海尔",
            "Leader": "海尔",
            "卡萨帝": "海尔",
            "Casarte": "海尔",
            "AQUA": "海尔",
            "华凌": "海尔",
            "WAHIN": "海尔",
            
            # 美的集团
            "小天鹅": "美的",
            "LittleSwan": "美的",
            "COLMO": "美的",
            "华凌": "美的",  # 注意：华凌现在属于美的
            "布谷": "美的",
            "BUGU": "美的",
            "东芝": "美的",  # 美的收购了东芝家电
            "TOSHIBA": "美的",
            
            # 格力集团
            "大松": "格力",
            "TOSOT": "格力",
            "晶弘": "格力",
            "KINGHOME": "格力",
            
            # 海信集团
            "科龙": "海信",
            "Kelon": "海信",
            "容声": "海信",
            "Ronshen": "海信",
            "VIDAA": "海信",
            
            # TCL集团
            "雷鸟": "TCL",
            "Thunder": "TCL",
            "XESS": "TCL",
            
            # 长虹集团
            "美菱": "长虹",
            "MeiLing": "长虹",
            "华意": "长虹",
            "HuaYi": "长虹",
            
            # 创维集团
            "酷开": "创维",
            "Coocaa": "创维",
            
            # 小米集团
            "红米": "小米",
            "Redmi": "小米",
            "米家": "小米",
            "MIJIA": "小米",
            "POCO": "小米",
            "黑鲨": "小米",
            "BlackShark": "小米",
            
            # 华为集团
            "荣耀": "华为",  # 虽然荣耀已独立，但产品线仍有关联
            "Honor": "华为",
            "智选": "华为",
            "HiLink": "华为",
        }
        
        # 检查是否是子品牌
        if brand_name in sub_to_main_brand_mapping:
            main_brand = sub_to_main_brand_mapping[brand_name]
            return brand_name, main_brand
        
        # 没有找到子品牌关系
        return None, None

    def _get_price_type_name(self, price_type: int) -> str:
        """获取价格类型名称（基于真实API数据）"""
        price_types = {
            0: "普通价格",        # ✅ 基于真实数据确认
            2: "券后价格",        # ✅ 基于真实数据确认
        }
        return price_types.get(int(price_type) if price_type else 0, f"价格类型{price_type}")

    def _get_activity_type_name(self, activity_type: int) -> str:
        """获取活动类型名称（基于真实API数据）"""
        activity_types = {
            0: "无活动",          # ✅ 基于真实数据确认
            1: "拼团活动",        # ✅ 基于真实数据确认
            34: "国补商品",       # ✅ 基于真实数据确认
            101: "新人专享",      # ✅ 基于真实数据确认
        }
        return activity_types.get(int(activity_type) if activity_type else 0, f"活动类型{activity_type}")

    def _get_event_type_name(self, event_type: int) -> str:
        """获取事件类型名称"""
        event_types = {
            0: "无事件",
            1: "促销活动",
            2: "新品上市",
            3: "清仓处理",
            4: "节日特惠",
            5: "品牌日活动"
        }
        return event_types.get(int(event_type) if event_type else 0, f"事件类型{event_type}")
    
    def _init_id_mappings(self):
        """初始化ID解码映射"""
        # 商家类型映射（基于Playwright MCP真实数据验证）
        self.merchant_type_mapping = {
            0: "官方旗舰店",      # ✅ 真实数据：海尔官方旗舰店
            1: "企业店铺",        # ✅ 真实数据：精选好物大家电
            2: "品牌授权店",      # 推测（无真实数据）
            3: "品牌旗舰店",      # 推测（无真实数据）
            4: "品牌直营店",      # 推测（无真实数据）
            5: "专营店",          # ✅ 真实数据：众沁电器专营店
            6: "普通店铺",        # ✅ 真实数据：乐享优品家电购
            7: "工厂店",          # 推测（无真实数据）
            10: "官方旗舰店",     # 官方认证旗舰店
            11: "品牌旗舰店"      # 品牌认证旗舰店
        }
        
        # 价格类型映射（基于真实API数据）
        self.price_type_mapping = {
            0: "普通价格",        # ✅ 基于真实数据确认
            2: "券后价格",        # ✅ 基于真实数据确认
        }
        
        # 活动类型映射（基于真实API数据）
        self.activity_type_mapping = {
            0: "无活动",          # ✅ 基于真实数据确认
            1: "拼团",            # ✅ 基于真实数据确认
            34: "国补商品",       # ✅ 基于真实数据确认
            101: "新人专享",      # ✅ 基于真实数据确认
        }
        
        # 初始化增强品牌识别系统
        self._init_enhanced_brand_system()
    
    def _init_enhanced_brand_system(self):
        """初始化增强品牌识别系统"""
        # 主品牌与子品牌映射关系（完善版）
        self.brand_hierarchy = {
            # 海尔集团
            "海尔": {
                "main_brand": "海尔",
                "english": "Haier",
                "sub_brands": {
                    "统帅": "Leader",
                    "卡萨帝": "Casarte",
                    "华凌": "WAHIN",
                    "Leader": "Leader",
                    "Casarte": "Casarte",
                    "WAHIN": "WAHIN",
                    "leader": "leader",
                    "casarte": "casarte",
                    "wahin": "wahin"
                }
            },

            # 美的集团
            "美的": {
                "main_brand": "美的",
                "english": "Midea",
                "sub_brands": {
                    "小天鹅": "LittleSwan",
                    "华凌": "WAHIN",
                    "COLMO": "COLMO",
                    "东芝": "TOSHIBA",
                    "库卡": "KUKA",
                    "LittleSwan": "LittleSwan",
                    "littleswan": "littleswan",
                    "Midea": "Midea",
                    "midea": "midea",
                    "TOSHIBA": "TOSHIBA",
                    "toshiba": "toshiba",
                    "KUKA": "KUKA",
                    "kuka": "kuka",
                    "colmo": "colmo"
                }
            },

            # 格力集团
            "格力": {
                "main_brand": "格力",
                "english": "GREE",
                "sub_brands": {
                    "大松": "TOSOT",
                    "晶弘": "KINGHOME",
                    "TOSOT": "TOSOT",
                    "tosot": "tosot",
                    "GREE": "GREE",
                    "gree": "gree",
                    "KINGHOME": "KINGHOME",
                    "kinghome": "kinghome"
                }
            },
            
            # 海信集团
            "海信": {
                "main_brand": "海信",
                "english": "Hisense",
                "sub_brands": {
                    "科龙": "Kelon",
                    "容声": "Ronshen",
                    "东芝": "TOSHIBA",
                    "Hisense": "Hisense",
                    "hisense": "hisense",
                    "Kelon": "Kelon",
                    "kelon": "kelon",
                    "Ronshen": "Ronshen",
                    "ronshen": "ronshen",
                    "TOSHIBA": "TOSHIBA",
                    "toshiba": "toshiba"
                }
            },
            
            # TCL集团
            "TCL": {
                "main_brand": "TCL",
                "english": "TCL",
                "sub_brands": {
                    "雷鸟": "Thunder Bird",
                    "XESS": "XESS",
                    "alcatel": "Alcatel",
                    "BlackBerry": "BlackBerry",
                    "Thunder": "Thunder",
                    "thunder": "thunder",
                    "xess": "xess",
                    "ALCATEL": "ALCATEL",
                    "blackberry": "blackberry",
                    "tcl": "tcl"
                }
            },
            
            # 长虹集团
            "长虹": {
                "main_brand": "长虹",
                "english": "Changhong",
                "sub_brands": {
                    "美菱": "MeiLing",
                    "华意": "HUAYI",
                    "虹欧": "HONGO"
                }
            },
            
            # 创维集团
            "创维": {
                "main_brand": "创维",
                "english": "Skyworth",
                "sub_brands": {
                    "酷开": "Coocaa",
                    "美兹": "Metz"
                }
            },
            
            # 小米集团
            "小米": {
                "main_brand": "小米",
                "english": "Xiaomi",
                "sub_brands": {
                    "红米": "Redmi",
                    "米家": "MIJIA",
                    "POCO": "POCO",
                    "黑鲨": "Black Shark"
                }
            },
            
            # 华为集团
            "华为": {
                "main_brand": "华为",
                "english": "HUAWEI",
                "sub_brands": {
                    "荣耀": "Honor",
                    "智选": "HiLink"
                }
            }
        }
        
        # 构建品牌关键词映射表（包含主品牌和子品牌）
        self.brand_keywords = {}
        self.sub_brand_mapping = {}
        
        # 添加主品牌
        for main_brand, brand_info in self.brand_hierarchy.items():
            # 中文主品牌名
            self.brand_keywords[main_brand] = main_brand
            # 英文主品牌名
            self.brand_keywords[brand_info["english"]] = main_brand
            self.brand_keywords[brand_info["english"].upper()] = main_brand
            self.brand_keywords[brand_info["english"].lower()] = main_brand
            
            # 添加子品牌
            for sub_brand_cn, sub_brand_en in brand_info["sub_brands"].items():
                # 子品牌映射到主品牌
                self.sub_brand_mapping[sub_brand_cn] = main_brand
                self.sub_brand_mapping[sub_brand_en] = main_brand
                self.sub_brand_mapping[sub_brand_en.upper()] = main_brand
                self.sub_brand_mapping[sub_brand_en.lower()] = main_brand
                
                # 子品牌关键词
                self.brand_keywords[sub_brand_cn] = sub_brand_cn
                self.brand_keywords[sub_brand_en] = sub_brand_cn
                self.brand_keywords[sub_brand_en.upper()] = sub_brand_cn
                self.brand_keywords[sub_brand_en.lower()] = sub_brand_cn
        
        # 添加独立品牌（传统品牌）
        traditional_brands = {
            '奥克斯': 'AUX', '苹果': 'Apple', '三星': 'Samsung',
            '西门子': 'SIEMENS', '博世': 'BOSCH', '松下': 'Panasonic',
            '夏普': 'SHARP', '日立': 'HITACHI', '康佳': 'KONKA',
            # 小众品牌扩展
            '七星': 'Seven Stars', 'Twinwash': 'Twinwash', '町渥': 'Twinwash',
            'Candara': 'Candara', '菱木': 'LingMu', '美凌': 'MeiLing',
            'Haer': 'Haer', 'TRONSSRA': 'TRONSSRA', 'Hairi': 'Hairi',
            '志高': 'CHIGO', '奥马': 'Homa', '新飞': 'Frestec',
            '星星': 'XINGX', '白雪': 'Baixue', '澳柯玛': 'AUCMA', 
            '伊莱克斯': 'Electrolux', '惠而浦': 'Whirlpool', 'LG': 'LG',
            '荣事达': 'Royalstar', '威力': 'WEILI', '金羚': 'Jinling', 
            '扬子': 'Yangzi', '樱花': 'SAKURA', '万和': 'Vanward',
            '万家乐': 'Macro', '史密斯': 'A.O.Smith', '阿里斯顿': 'ARISTON', 
            '能率': 'NORITZ', '林内': 'Rinnai', '华帝': 'Vatti', 
            '方太': 'FOTILE', '老板': 'Robam', '苏泊尔': 'SUPOR', 
            '九阳': 'Joyoung', '美苏': 'Meisu', '格兰仕': 'Galanz',
            '康宝': 'Canbo', '万宝': 'Wanbao', '新科': 'Shinco', 
            '先科': 'SAST', '长岭': 'Changling', '双鹿': 'SHUANGLU', 
            '申花': 'Shenhua', '小鸭': 'LITTLEDUCK', '圣吉奥': 'SHENGJIAO', 
            '威博': 'WEIBO', 'OPPO': 'OPPO', 'vivo': 'vivo', 
            '一加': 'OnePlus', '魅族': 'Meizu', '联想': 'Lenovo'
        }
        
        # 添加新兴智能品牌
        emerging_brands = {
            '云米': 'VIOMI', '石头': 'Roborock', '追觅': 'Dreame',
            '科沃斯': 'Ecovacs', '浦桑尼克': 'Proscenic', '由利': 'Uoni',
            '戴森': 'Dyson', 'iRobot': 'iRobot', '必胜': 'Bissell',
            '福维克': 'Vorwerk', '添可': 'Tineco', '睿米': 'ROIDMI',
            '顺造': 'SHUNZAO', '莱克': 'LEXY', '德尔玛': 'Deerma',
            '小狗': 'Puppy', '飞利浦': 'Philips', '松下': 'Panasonic',
            '美诺': 'Miele', '卡赫': 'Karcher', '伊莱克斯': 'Electrolux',
            '鲨客': 'Shark', '胡佛': 'Hoover', '百得': 'Black+Decker'
        }
        
        # 添加厨电品牌
        kitchen_brands = {
            '老板': 'Robam', '方太': 'FOTILE', '华帝': 'Vatti',
            '帅康': 'Sacon', '万和': 'Vanward', '万家乐': 'Macro',
            '樱花': 'SAKURA', '林内': 'Rinnai', '能率': 'NORITZ',
            '史密斯': 'A.O.Smith', '阿里斯顿': 'ARISTON', '海尔': 'Haier',
            '美的': 'Midea', '格兰仕': 'Galanz', '九阳': 'Joyoung',
            '苏泊尔': 'SUPOR', '小熊': 'Bear', '荣事达': 'Royalstar',
            '奔腾': 'POVOS', '东菱': 'Donlim', '德龙': 'DeLonghi',
            '摩飞': 'Morphyrichards', '凯伍德': 'Kenwood', '博朗': 'Braun'
        }
        
        # 添加个护品牌
        personal_care_brands = {
            '飞利浦': 'Philips', '松下': 'Panasonic', '博朗': 'Braun',
            '戴森': 'Dyson', '沙宣': 'VS', '康夫': 'Kangfu',
            '奔腾': 'POVOS', '飞科': 'FLYCO', '超人': 'SID',
            '雷瓦': 'RIWA', '华为': 'HUAWEI', '小米': 'Xiaomi',
            '荣耀': 'Honor', '欧乐B': 'Oral-B', '舒客': 'Saky'
        }
        
        # 合并所有品牌
        all_independent_brands = {**traditional_brands, **emerging_brands, **kitchen_brands, **personal_care_brands}
        
        for brand_cn, brand_en in all_independent_brands.items():
            self.brand_keywords[brand_cn] = brand_cn
            self.brand_keywords[brand_en] = brand_cn
            self.brand_keywords[brand_en.upper()] = brand_cn
            self.brand_keywords[brand_en.lower()] = brand_cn
        
        # 添加品牌别名和常见错误拼写
        self._init_brand_aliases()        # 品牌ID映射（已修复冲突）
        self.brand_id_mapping = {
            "8": "苏泊尔",  # 苏泊尔品牌ID
            "14": "奥克斯",  # 奥克斯品牌ID
            "15": "美的",  # 美的品牌ID
            "77": "容声",  # 容声品牌ID
            "79": "松下",  # 松下品牌ID
            "168": "南极人",  # 南极人品牌ID
            "228": "长虹",  # 长虹品牌ID
            "234": "海尔",  # 海尔品牌ID
            "335": "格兰仕",  # 格兰仕品牌ID
            "403": "TCL",  # TCL品牌ID
            "430": "创维",  # 创维品牌ID
            "439": "小米",  # 小米品牌ID
            "465": "志高",  # 志高品牌ID
            "469": "小天鹅",  # 小天鹅品牌ID
            "500": "海信",  # 海信品牌ID
            "1139": "LG",  # LG品牌ID
            "3003": "韩电",  # 韩电品牌ID
            "3835": "三星",  # 三星品牌ID
            "3837": "卡萨帝",  # 卡萨帝品牌ID
            "3838": "统帅",  # 统帅品牌ID
            "3840": "新飞",  # 新飞品牌ID
            "3854": "东芝",  # 东芝品牌ID
            "3855": "AO史密斯",  # AO史密斯品牌ID
            "4875": "扬子",  # 扬子品牌ID
            "7657": "飞利浦",  # 飞利浦品牌ID
            "8579": "荣事达",  # 荣事达品牌ID
            "15323": "康佳",  # 康佳品牌ID
            "17357": "亚都",  # 亚都品牌ID
            "17427": "石头",  # 石头品牌ID
            "435792": "VCJ",  # VCJ品牌ID
            "856230": "町渥",  # 町渥品牌ID
            "1049165": "Candara",  # Candara品牌ID
            "1160429": "美凌",  # 美凌品牌ID
            "1488684": "移动",  # 移动品牌ID
            "1759197": "Mendi",  # Mendi品牌ID
            "10737465": "格力",  # 格力品牌ID
            "10753633": "海日",  # 海日品牌ID
            "11381594": "Candara",  # Candara品牌ID
        }
    
    def _init_brand_aliases(self):
        """初始化品牌别名和常见错误拼写"""
        # 品牌别名映射
        brand_aliases = {
            # 海尔相关
            "海而": "海尔", "海儿": "海尔", "Haier": "海尔", "HAIER": "海尔",
            
            # 美的相关  
            "美地": "美的", "美得": "美的", "Midea": "美的", "MIDEA": "美的",
            
            # 格力相关
            "格利": "格力", "格力": "格力", "GREE": "格力", "Gree": "格力",
            
            # 小米相关
            "米家": "小米", "MIJIA": "小米", "Xiaomi": "小米", "XIAOMI": "小米",
            
            # 华为相关
            "华为": "华为", "HUAWEI": "华为", "Huawei": "华为",
            
            # 戴森相关
            "戴森": "戴森", "Dyson": "戴森", "DYSON": "戴森", "dyson": "戴森",
            
            # 云米相关
            "云米": "云米", "VIOMI": "云米", "viomi": "云米", "Viomi": "云米",
            
            # 石头相关
            "石头": "石头", "Roborock": "石头", "roborock": "石头", "ROBOROCK": "石头",
            
            # 追觅相关
            "追觅": "追觅", "Dreame": "追觅", "dreame": "追觅", "DREAME": "追觅",
            
            # 科沃斯相关
            "科沃斯": "科沃斯", "Ecovacs": "科沃斯", "ecovacs": "科沃斯", "ECOVACS": "科沃斯",
            
            # iRobot相关
            "iRobot": "iRobot", "irobot": "iRobot", "IROBOT": "iRobot", "艾罗伯特": "iRobot",
            
            # 飞利浦相关
            "飞利浦": "飞利浦", "Philips": "飞利浦", "philips": "飞利浦", "PHILIPS": "飞利浦",
            
            # 松下相关
            "松下": "松下", "Panasonic": "松下", "panasonic": "松下", "PANASONIC": "松下",
            
            # 常见错误拼写
            "海尓": "海尔", "海儿": "海尔", "海而": "海尔",
            "美地": "美的", "美得": "美的", "美滴": "美的",
            "格利": "格力", "格力": "格力", "格立": "格力",
            "小咪": "小米", "小迷": "小米", "小密": "小米",
        }
        
        # 将别名添加到品牌关键词映射中
        for alias, brand in brand_aliases.items():
            if brand in self.brand_keywords.values():
                self.brand_keywords[alias] = brand

    def _build_brand_candidates(self):
        """
        预构建品牌候选列表，提高RapidFuzz匹配性能
        """
        if not RAPIDFUZZ_AVAILABLE:
            return

        # 构建子品牌候选列表（优先级最高）
        self.sub_brand_candidates = []
        self.main_brand_candidates = []

        for keyword, brand in self.brand_keywords.items():
            if len(keyword) >= 2:  # 过滤太短的关键词
                if brand in self.sub_brand_mapping:
                    self.sub_brand_candidates.append((keyword, brand))
                else:
                    self.main_brand_candidates.append((keyword, brand))

        # 按关键词长度排序（长关键词优先，更精确）
        self.sub_brand_candidates.sort(key=lambda x: len(x[0]), reverse=True)
        self.main_brand_candidates.sort(key=lambda x: len(x[0]), reverse=True)

        # 提取关键词列表用于RapidFuzz
        self.sub_brand_keywords = [item[0] for item in self.sub_brand_candidates]
        self.main_brand_keywords = [item[0] for item in self.main_brand_candidates]
        self.all_brand_keywords = self.sub_brand_keywords + self.main_brand_keywords

        # 构建关键词到品牌的快速映射
        self.keyword_to_brand = {}
        for keyword, brand in self.sub_brand_candidates + self.main_brand_candidates:
            self.keyword_to_brand[keyword] = brand

        logger.info(f"品牌候选列表构建完成: 子品牌{len(self.sub_brand_candidates)}个, 主品牌{len(self.main_brand_candidates)}个")

    def _decode_merchant_type(self, merchant_type: Any) -> str:
        """解码商家类型"""
        try:
            type_id = int(merchant_type) if merchant_type else 0
            return self.merchant_type_mapping.get(type_id, f"商家类型{type_id}")
        except:
            return str(merchant_type) if merchant_type else "普通商家"
    
    def _decode_price_type(self, price_type: Any) -> str:
        """解码价格类型"""
        try:
            type_id = int(price_type) if price_type else 0
            return self.price_type_mapping.get(type_id, f"价格类型{type_id}")
        except:
            return str(price_type) if price_type else "普通价格"
    
    def _decode_activity_type(self, activity_type: Any) -> str:
        """解码活动类型"""
        try:
            type_id = int(activity_type) if activity_type else 0
            return self.activity_type_mapping.get(type_id, f"活动类型{type_id}")
        except:
            return str(activity_type) if activity_type else "无活动"
    
    def _decode_brand_id(self, brand_id: Any, brand_name: str = "") -> str:
        """
        解码品牌ID - 增强版
        优先使用有效的品牌名称，进行中英文转换和格式化
        支持子品牌识别和显示
        """
        # 如果已有有效的品牌名称，进行中英文转换
        if brand_name and self._is_valid_brand_name(brand_name):
            # 尝试转换为中文品牌名称
            converted_brand = self._convert_to_chinese_brand(brand_name)
            if converted_brand:
                # 检查是否是子品牌
                sub_brand, main_brand = self._identify_sub_and_main_brand(converted_brand)
                if sub_brand and main_brand:
                    return self._format_brand_display(sub_brand, main_brand)
                return converted_brand
            
            # 直接检查原品牌名是否是子品牌
            sub_brand, main_brand = self._identify_sub_and_main_brand(brand_name)
            if sub_brand and main_brand:
                return self._format_brand_display(sub_brand, main_brand)
            return brand_name
        
        # 尝试从品牌ID映射表解码
        if brand_id and str(brand_id) in self.brand_id_mapping:
            decoded_brand = self.brand_id_mapping[str(brand_id)]
            if self._is_valid_brand_name(decoded_brand):
                # 检查是否是子品牌
                sub_brand, main_brand = self._identify_sub_and_main_brand(decoded_brand)
                if sub_brand and main_brand:
                    return self._format_brand_display(sub_brand, main_brand)
                return decoded_brand
        
        # 如果brand_name存在但不是有效品牌名，尝试从中提取
        if brand_name:
            extracted_brand = self._extract_brand_from_text(brand_name)
            if extracted_brand:
                # 检查是否是子品牌
                sub_brand, main_brand = self._identify_sub_and_main_brand(extracted_brand)
                if sub_brand and main_brand:
                    return self._format_brand_display(sub_brand, main_brand)
                return extracted_brand
        
        # 最后尝试使用brand_id，但避免返回无意义占位符
        if brand_id and str(brand_id).isdigit() and str(brand_id) != "0":
            # 不返回"品牌_ID"格式，而是返回空字符串
            # 这样在导出时会被过滤掉
            return ""
        
        return ""
    
    def _convert_to_chinese_brand(self, brand_name: str) -> str:
        """
        将品牌名称转换为中文 - 与基础解析保持一致
        """
        if not brand_name:
            return brand_name

        # 使用与APIResponseMonitor相同的扩充映射表
        brand_mapping = {
            # 主流家电品牌
            "haier": "海尔", "midea": "美的", "gree": "格力", "tcl": "TCL",
            "siemens": "西门子", "bosch": "博世", "panasonic": "松下",
            "samsung": "三星", "lg": "LG", "whirlpool": "惠而浦",
            "xiaomi": "小米", "mi": "小米", "hisense": "海信",

            # 子品牌
            "casarte": "卡萨帝", "leader": "统帅", "littleswan": "小天鹅",
            "ronshen": "容声", "galanz": "格兰仕", "aux": "奥克斯",
            "colmo": "COLMO", "华凌": "华凌", "布谷": "布谷", "toshiba": "东芝",
            "tosot": "大松", "晶弘": "晶弘", "kelon": "科龙", "vidaa": "VIDAA",
            "changhong": "长虹", "chiq": "长虹", "konka": "康佳", "skyworth": "创维",
            "coocaa": "酷开", "chigo": "志高", "扬子": "扬子", "新飞": "新飞",

            # 小家电品牌
            "joyoung": "九阳", "supor": "苏泊尔", "bear": "小熊", "小熊电器": "小熊",
            "royalstar": "荣事达", "liven": "利仁", "deerma": "德尔玛", "buydeem": "北鼎",
            "素士": "素士", "云米": "云米", "追觅": "追觅",
            "philips": "飞利浦", "dyson": "戴森", "braun": "博朗", "tefal": "特福",
            "delonghi": "德龙", "zojirushi": "象印", "tiger": "虎牌",

            # 数码品牌
            "huawei": "华为", "honor": "荣耀", "oppo": "OPPO", "vivo": "vivo",
            "oneplus": "一加", "realme": "真我", "meizu": "魅族", "zte": "中兴",
            "nubia": "努比亚", "apple": "苹果", "iphone": "苹果",
            "lenovo": "联想", "thinkpad": "联想", "hasee": "神舟", "tongfang": "同方",
            "dell": "戴尔", "hp": "惠普", "asus": "华硕", "acer": "宏碁", "msi": "微星",

            # 骑行服装品牌
            "santic": "森地客", "spakct": "思帕客", "mysenlan": "迈森兰", "msl": "迈森兰",
            "grc": "GRC", "jakroo": "捷酷", "ccn": "CCN", "sobike": "速盟",
            "lightning": "闪电", "qiji": "骑记", "rockbros": "洛克兄弟",
            "forever": "永久", "phoenix": "凤凰", "pigeon": "飞鸽",
            "decathlon": "迪卡侬", "btwin": "迪卡侬", "rapha": "Rapha", "castelli": "Castelli",
            "assos": "Assos", "pearlizumi": "Pearl Izumi", "pearl": "Pearl Izumi",
            "craft": "Craft", "endura": "Endura", "gore": "Gore", "goretex": "Gore",
            "specialized": "闪电", "trek": "Trek", "giant": "捷安特", "merida": "美利达",

            # 小众品牌
            "sevenstars": "七星", "seven stars": "七星",
            "twinwash": "町渥", "candara": "Candara", "lingmu": "菱木",
            "meiling": "美凌", "haer": "海尔", "tronssra": "TRONSSRA",
            "hairi": "海日",

            # 英文变体（大小写统一）
            "HAIER": "海尔", "Haier": "海尔", "MIDEA": "美的", "Midea": "美的",
            "GREE": "格力", "Gree": "格力", "SIEMENS": "西门子", "Siemens": "西门子",
            "BOSCH": "博世", "Bosch": "博世", "SAMSUNG": "三星", "Samsung": "三星",
            "XIAOMI": "小米", "Xiaomi": "小米", "HISENSE": "海信", "Hisense": "海信",
            "CASARTE": "卡萨帝", "Casarte": "卡萨帝", "LEADER": "统帅", "Leader": "统帅",
            "LITTLESWAN": "小天鹅", "LittleSwan": "小天鹅", "RONSHEN": "容声", "Ronshen": "容声",
            "GALANZ": "格兰仕", "Galanz": "格兰仕", "SEVENSTARS": "七星", "Sevenstars": "七星",
            "TWINWASH": "町渥", "Twinwash": "町渥", "MEILING": "美凌", "MeiLing": "美凌",
            "HAER": "海尔", "Haer": "海尔", "TRONSSRA": "TRONSSRA", "Tronssra": "TRONSSRA",
            "HAIRI": "海日", "Hairi": "海日", "HUAWEI": "华为", "Huawei": "华为",
            "APPLE": "苹果", "Apple": "苹果", "ONEPLUS": "一加", "OnePlus": "一加",
            "MEIZU": "魅族", "Meizu": "魅族", "HONOR": "荣耀", "Honor": "荣耀",
            "SANTIC": "森地客", "Santic": "森地客", "SPAKCT": "思帕客", "Spakct": "思帕客",
            "DECATHLON": "迪卡侬", "Decathlon": "迪卡侬", "GIANT": "捷安特", "Giant": "捷安特",
            "MERIDA": "美利达", "Merida": "美利达",
            # 小众品牌英文映射
            "sevenstars": "七星", "Seven Stars": "七星",
            "Twinwash": "町渥",
            "Candara": "Candara",
            "LingMu": "菱木",
            "MeiLing": "美凌",
            "Haer": "Haer",
            "TRONSSRA": "TRONSSRA", "tronssra": "TRONSSRA",
            "Hairi": "Hairi",
            "Redmi": "红米",
            "MIJIA": "米家",
            "HUAWEI": "华为",
            "Honor": "荣耀",
            "AUX": "奥克斯",
            "Apple": "苹果",
            "Samsung": "三星",
            "SIEMENS": "西门子",
            "BOSCH": "博世",
            "Panasonic": "松下",
            "SHARP": "夏普",
            "HITACHI": "日立",
            "KONKA": "康佳",
            "CHIGO": "志高",
            "Homa": "奥马",
            "Frestec": "新飞",
            "XINGX": "星星",
            "Baixue": "白雪",
            "AUCMA": "澳柯玛",
            "Electrolux": "伊莱克斯",
            "Whirlpool": "惠而浦",
            "LG": "LG",
            "Royalstar": "荣事达",
            "WEILI": "威力",
            "Jinling": "金羚",
            "Yangzi": "扬子",
            "SAKURA": "樱花",
            "Vanward": "万和",
            "Macro": "万家乐",
            "A.O.Smith": "史密斯",
            "ARISTON": "阿里斯顿",
            "NORITZ": "能率",
            "Rinnai": "林内",
            "Vatti": "华帝",
            "FOTILE": "方太",
            "Robam": "老板",
            "SUPOR": "苏泊尔",
            "Joyoung": "九阳",
            "Galanz": "格兰仕",
            "VIOMI": "云米",
            "Roborock": "石头",
            "Dreame": "追觅",
            "Ecovacs": "科沃斯",
            "iRobot": "iRobot",
            "Dyson": "戴森",
            "Philips": "飞利浦",

            # 大小写变体
            "haier": "海尔",
            "leader": "统帅",
            "casarte": "卡萨帝",
            "wahin": "华凌",
            "midea": "美的",
            "littleswan": "小天鹅",
            "colmo": "COLMO",
            "gree": "格力",
            "tosot": "大松",
            "hisense": "海信",
            "kelon": "科龙",
            "ronshen": "容声",
            "tcl": "TCL",
            "skyworth": "创维",
            "coocaa": "酷开",
            "xiaomi": "小米",
            "redmi": "红米",
            "mijia": "米家",
            "huawei": "华为",
            "honor": "荣耀",
            "aux": "奥克斯",
            "apple": "苹果",
            "samsung": "三星",
            "siemens": "西门子",
            "bosch": "博世",
            "panasonic": "松下",
            "sharp": "夏普",
            "hitachi": "日立",
            "konka": "康佳",
            "lg": "LG",
            "sony": "索尼",
            "philips": "飞利浦",
            "dyson": "戴森",
            "electrolux": "伊莱克斯",
            "whirlpool": "惠而浦"
        }

        # 多种匹配方式
        for key in [brand_name, brand_name.lower(), brand_name.upper(), brand_name.capitalize()]:
            if key in brand_mapping:
                chinese_brand = brand_mapping[key]
                break
        else:
            chinese_brand = brand_name
        
        # 检查是否为子品牌，应用格式化
        if chinese_brand in self.sub_brand_mapping:
            main_brand = self.sub_brand_mapping[chinese_brand]
            return self._format_brand_display(chinese_brand, main_brand)
        
        return chinese_brand
    
    def _is_valid_brand_name(self, brand_name: str) -> bool:
        """
        检查品牌名称是否有效
        
        Args:
            brand_name: 品牌名称
            
        Returns:
            bool: 是否为有效品牌名称
        """
        if not brand_name or not isinstance(brand_name, str):
            return False
        
        brand_name = brand_name.strip()
        
        # 排除无意义的占位符
        invalid_patterns = [
            "品牌_", "品牌ID", "未知品牌", "暂无品牌", "无品牌",
            "品牌", "brand_", "Brand_", "BRAND_", "未知", "暂无", "无"
        ]
        
        for pattern in invalid_patterns:
            if brand_name.startswith(pattern) or brand_name == pattern:
                return False
        
        # 检查是否为纯数字
        if brand_name.isdigit():
            return False
        
        # 检查长度（有效品牌名称通常至少2个字符）
        if len(brand_name) < 2:
            return False
        
        return True
    
    def _extract_brand_from_text(self, text: str) -> str:
        """
        从文本中提取品牌名称
        
        Args:
            text: 输入文本
            
        Returns:
            str: 提取的品牌名称
        """
        if not text:
            return ""
        
        # 使用品牌关键词匹配
        text_upper = text.upper()
        for keyword, brand in self.brand_keywords.items():
            if len(keyword) >= 2 and (keyword in text or keyword.upper() in text_upper):
                # 检查是否为子品牌
                if brand in self.sub_brand_mapping:
                    main_brand = self.sub_brand_mapping[brand]
                    return self._format_brand_display(brand, main_brand)
                else:
                    return brand
        
        return ""

    
    def get_brand_recognition_stats(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取品牌识别统计信息
        
        Args:
            data: 商品数据列表
            
        Returns:
            Dict[str, Any]: 品牌识别统计
        """
        if not data:
            return {"total_products": 0, "recognized_brands": 0, "coverage_rate": 0}
        
        total_products = len(data)
        recognized_count = 0
        brand_distribution = {}
        main_brand_distribution = {}
        sub_brand_count = 0
        
        for item in data:
            brand_name = item.get("brand_name", "")
            if brand_name and self._is_valid_brand_name(brand_name):
                recognized_count += 1
                
                # 统计品牌分布
                if brand_name in brand_distribution:
                    brand_distribution[brand_name] += 1
                else:
                    brand_distribution[brand_name] = 1
                
                # 分析主品牌和子品牌
                if "(" in brand_name and ")" in brand_name:
                    # 子品牌格式：子品牌(主品牌)
                    sub_brand_count += 1
                    main_brand = brand_name.split("(")[1].split(")")[0]
                    if main_brand in main_brand_distribution:
                        main_brand_distribution[main_brand] += 1
                    else:
                        main_brand_distribution[main_brand] = 1
                else:
                    # 主品牌
                    if brand_name in main_brand_distribution:
                        main_brand_distribution[brand_name] += 1
                    else:
                        main_brand_distribution[brand_name] = 1
        
        coverage_rate = (recognized_count / total_products * 100) if total_products > 0 else 0
        
        # 排序品牌分布
        sorted_brands = sorted(brand_distribution.items(), key=lambda x: x[1], reverse=True)
        sorted_main_brands = sorted(main_brand_distribution.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "total_products": total_products,
            "recognized_brands": recognized_count,
            "unrecognized_brands": total_products - recognized_count,
            "coverage_rate": round(coverage_rate, 2),
            "sub_brand_count": sub_brand_count,
            "main_brand_count": recognized_count - sub_brand_count,
            "brand_distribution": dict(sorted_brands[:20]),  # 前20个品牌
            "main_brand_distribution": dict(sorted_main_brands[:10]),  # 前10个主品牌
            "unique_brands": len(brand_distribution),
            "unique_main_brands": len(main_brand_distribution)
        }

    async def _deduplicate_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去重数据 - 改进版，基于商品ID和关键词组合去重

        Args:
            data: 数据列表

        Returns:
            List[Dict[str, Any]]: 去重后的数据
        """
        seen_combinations: Set[str] = set()
        deduplicated_data = []

        for item in data:
            goods_id = item.get("goods_id", "")
            keyword = item.get("keyword", item.get("search_keyword", ""))

            # 使用商品ID和关键词的组合作为去重标识
            # 这样同一个商品在不同关键词下可以保留
            combination_key = f"{goods_id}_{keyword}"

            if combination_key not in seen_combinations:
                seen_combinations.add(combination_key)
                deduplicated_data.append(item)
            else:
                logger.debug(f"发现重复商品组合，跳过: {goods_id} (关键词: {keyword})")

        removed_count = len(data) - len(deduplicated_data)
        if removed_count > 0:
            logger.info(f"去重完成，移除 {removed_count} 条重复数据")

        return deduplicated_data
    
    async def group_data_by_keyword(self, data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按关键词分组数据
        
        Args:
            data: 商品数据列表
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 按关键词分组的数据
        """
        grouped_data = {}
        
        for item in data:
            keyword = item.get("keyword", "未知关键词")
            if keyword not in grouped_data:
                grouped_data[keyword] = []
            grouped_data[keyword].append(item)
        
        # 按关键词排序
        for keyword in grouped_data:
            grouped_data[keyword].sort(key=lambda x: x.get("goods_id", ""))
        
        logger.info(f"数据分组完成: {len(grouped_data)} 个关键词")
        return grouped_data

    async def group_data_by_keyword_and_sort(self, data: List[Dict[str, Any]]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        按关键词和排序方式分组数据
        
        Args:
            data: 商品数据列表
            
        Returns:
            Dict[str, Dict[str, List]]: 按关键词和排序分组的数据
            格式: {keyword: {sort_type: [goods_list]}}
        """
        grouped_data = {}
        
        for item in data:
            # 解析关键词和排序信息
            keyword_sort = item.get("keyword", "未知关键词")
            
            # 分离关键词和排序类型
            if "_" in keyword_sort:
                parts = keyword_sort.split("_", 1)
                keyword = parts[0]
                sort_type = parts[1]
            else:
                keyword = keyword_sort
                sort_type = "default"
            
            # 创建嵌套字典结构
            if keyword not in grouped_data:
                grouped_data[keyword] = {}
            
            if sort_type not in grouped_data[keyword]:
                grouped_data[keyword][sort_type] = []
            
            # 更新item中的keyword字段为纯关键词
            item["keyword"] = keyword
            item["sort_type"] = sort_type
            
            grouped_data[keyword][sort_type].append(item)
        
        # 排序数据
        for keyword in grouped_data:
            for sort_type in grouped_data[keyword]:
                grouped_data[keyword][sort_type].sort(key=lambda x: x.get("goods_id", ""))
        
        total_groups = sum(len(sorts) for sorts in grouped_data.values())
        logger.info(f"数据按关键词和排序分组完成: {len(grouped_data)} 个关键词, {total_groups} 个排序组")
        return grouped_data
    
    def get_data_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            data: 商品数据列表
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not data:
            return {"total_count": 0}
        
        # 基本统计
        stats = {
            "total_count": len(data),
            "keywords": set(),
            "shops": set(),
            "categories": set()
        }
        
        # 价格统计
        prices = []
        sales_list = []
        
        for item in data:
            # 关键词
            keyword = item.get("keyword", "")
            if keyword:
                stats["keywords"].add(keyword)
            
            # 店铺
            shop = item.get("shop_name", "")
            if shop and shop != "未知店铺":
                stats["shops"].add(shop)
            
            # 分类
            category = item.get("category", "")
            if category:
                stats["categories"].add(category)
            
            # 价格
            try:
                price = float(item.get("price", 0))
                if price > 0:
                    prices.append(price)
            except:
                pass
            
            # 销量
            try:
                sales = int(item.get("sales", 0))
                if sales > 0:
                    sales_list.append(sales)
            except:
                pass
        
        # 转换为数量
        stats["keyword_count"] = len(stats["keywords"])
        stats["shop_count"] = len(stats["shops"])
        stats["category_count"] = len(stats["categories"])
        
        # 价格统计
        if prices:
            stats["price_stats"] = {
                "min": min(prices),
                "max": max(prices),
                "avg": sum(prices) / len(prices),
                "count": len(prices)
            }
        
        # 销量统计
        if sales_list:
            stats["sales_stats"] = {
                "min": min(sales_list),
                "max": max(sales_list),
                "avg": sum(sales_list) / len(sales_list),
                "count": len(sales_list)
            }
        
        return stats
