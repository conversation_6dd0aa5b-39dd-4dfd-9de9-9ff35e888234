#!/usr/bin/env python3
"""
更新后的拼多多营销标签识别模块
基于用户确认的ID含义进行更新
"""

def identify_marketing_labels(icon_list):
    """
    识别营销标签
    
    Args:
        icon_list: 商品的icon_list数组
        
    Returns:
        list: 识别到的营销标签列表
    """
    labels = []
    
    # 更新后的标签映射（基于用户确认）
    label_mapping = {
        20001: "百亿补贴",
        0: "秒杀",
        20013: "夏清仓",      # 用户确认
        10: "旗舰店",         # 用户确认
        10014: "品牌直营"
    }
    
    for icon in icon_list:
        icon_id = icon.get('id')
        if icon_id in label_mapping:
            labels.append({
                'id': icon_id,
                'label': label_mapping[icon_id],
                'url': icon.get('url', ''),
                'size': f"{icon.get('width', 0)}×{icon.get('height', 0)}",
                'tag_track_info': icon.get('tag_track_info', '')
            })
    
    return labels

def extract_enhanced_product_data(goods_model):
    """
    提取增强的商品数据（包含营销标签信息）
    
    Args:
        goods_model: 商品模型数据
        
    Returns:
        dict: 增强的商品数据
    """
    data = {
        'goods_id': goods_model.get('goods_id'),
        'goods_name': goods_model.get('goods_name'),
        'marketing_labels': [],
        'has_billion_subsidy': False,    # 百亿补贴
        'has_flash_sale': False,         # 秒杀
        'has_summer_clearance': False,   # 夏清仓
        'has_flagship_store': False,     # 旗舰店
        'has_brand_official': False      # 品牌直营
    }
    
    icon_list = goods_model.get('icon_list', [])
    for icon in icon_list:
        icon_id = icon.get('id')
        
        if icon_id == 20001:
            data['has_billion_subsidy'] = True
            data['marketing_labels'].append('百亿补贴')
        elif icon_id == 0:
            data['has_flash_sale'] = True
            data['marketing_labels'].append('秒杀')
        elif icon_id == 20013:
            data['has_summer_clearance'] = True
            data['marketing_labels'].append('夏清仓')
        elif icon_id == 10:
            data['has_flagship_store'] = True
            data['marketing_labels'].append('旗舰店')
        elif icon_id == 10014:
            data['has_brand_official'] = True
            data['marketing_labels'].append('品牌直营')
    
    return data

def get_marketing_label_priority():
    """
    获取营销标签的优先级排序
    用于在多个标签同时存在时确定显示优先级
    
    Returns:
        dict: 标签ID到优先级的映射（数字越小优先级越高）
    """
    return {
        20001: 1,  # 百亿补贴 - 最高优先级
        0: 2,      # 秒杀 - 高优先级
        20013: 3,  # 夏清仓 - 中优先级
        10: 4,     # 旗舰店 - 中低优先级
        10014: 5   # 品牌直营 - 低优先级
    }

def format_marketing_labels_for_display(labels):
    """
    格式化营销标签用于显示
    
    Args:
        labels: 营销标签列表
        
    Returns:
        str: 格式化后的标签字符串
    """
    if not labels:
        return ""
    
    # 按优先级排序
    priority_map = get_marketing_label_priority()
    sorted_labels = sorted(labels, key=lambda x: priority_map.get(x['id'], 999))
    
    # 提取标签名称
    label_names = [label['label'] for label in sorted_labels]
    
    return " | ".join(label_names)

def validate_marketing_labels(icon_list):
    """
    验证营销标签数据的完整性
    
    Args:
        icon_list: 商品的icon_list数组
        
    Returns:
        dict: 验证结果
    """
    result = {
        'valid': True,
        'warnings': [],
        'errors': []
    }
    
    known_ids = {20001, 0, 20013, 10, 10014}
    
    for icon in icon_list:
        icon_id = icon.get('id')
        url = icon.get('url', '')
        
        # 检查未知ID
        if icon_id not in known_ids:
            result['warnings'].append(f"发现未知营销标签ID: {icon_id}")
        
        # 检查URL完整性
        if not url:
            result['errors'].append(f"营销标签ID {icon_id} 缺少URL")
            result['valid'] = False
        
        # 检查尺寸信息
        width = icon.get('width', 0)
        height = icon.get('height', 0)
        if not width or not height:
            result['warnings'].append(f"营销标签ID {icon_id} 缺少尺寸信息")
    
    return result

# 使用示例
if __name__ == "__main__":
    # 示例数据
    sample_icon_list = [
        {
            "id": 20001,
            "url": "http://img.pddpic.com/social/pincard/1/share.png",
            "width": 156,
            "height": 42,
            "tag_track_info": "2&20001"
        },
        {
            "id": 0,
            "url": "https://funimg.pddpic.com/hot_friends/8c0d811f-8173-4059-90b8-bd7704f00f05.png",
            "width": 81,
            "height": 42,
            "tag_track_info": "2&0"
        },
        {
            "id": 20013,
            "url": "https://commimg.pddpic.com/oms_img_ng/2025-07-24/dba0d079-6501-4a05-9232-706847dbfeb5.png",
            "width": 126,
            "height": 45,
            "tag_track_info": "2&20013"
        }
    ]
    
    # 识别标签
    labels = identify_marketing_labels(sample_icon_list)
    print("识别到的营销标签:")
    for label in labels:
        print(f"  {label['label']} (ID: {label['id']}, 尺寸: {label['size']})")
    
    # 格式化显示
    display_text = format_marketing_labels_for_display(labels)
    print(f"\n显示文本: {display_text}")
    
    # 验证数据
    validation = validate_marketing_labels(sample_icon_list)
    print(f"\n验证结果: {'通过' if validation['valid'] else '失败'}")
    if validation['warnings']:
        print("警告:", validation['warnings'])
    if validation['errors']:
        print("错误:", validation['errors'])
