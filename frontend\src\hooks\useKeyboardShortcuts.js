import { useEffect, useCallback } from 'react'

// 键盘快捷键 Hook
export const useKeyboardShortcuts = (shortcuts) => {
  const handleKeyDown = useCallback((event) => {
    // 获取按键组合
    const key = event.key.toLowerCase()
    const ctrl = event.ctrlKey || event.metaKey
    const shift = event.shiftKey
    const alt = event.altKey
    
    // 忽略输入框中的快捷键
    const tagName = event.target.tagName.toLowerCase()
    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
      return
    }
    
    // 遍历快捷键配置
    shortcuts.forEach(shortcut => {
      const { keys, handler, preventDefault = true } = shortcut
      
      // 检查按键是否匹配
      const matchKey = keys.key?.toLowerCase() === key
      const matchCtrl = !!keys.ctrl === ctrl
      const matchShift = !!keys.shift === shift
      const matchAlt = !!keys.alt === alt
      
      if (matchKey && matchCtrl && matchShift && matchAlt) {
        if (preventDefault) {
          event.preventDefault()
        }
        handler(event)
      }
    })
  }, [shortcuts])
  
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])
}

// 常用快捷键配置
export const commonShortcuts = {
  // 搜索
  search: {
    keys: { ctrl: true, key: 'k' },
    description: '打开搜索'
  },
  
  // 开始/停止爬虫
  startCrawl: {
    keys: { ctrl: true, key: 's' },
    description: '开始爬取'
  },
  
  stopCrawl: {
    keys: { ctrl: true, shift: true, key: 's' },
    description: '停止爬取'
  },
  
  // 导出数据
  exportData: {
    keys: { ctrl: true, key: 'e' },
    description: '导出数据'
  },
  
  // Cookie 管理
  cookieManager: {
    keys: { ctrl: true, key: 'm' },
    description: '打开Cookie管理'
  },
  
  // 主题切换
  toggleTheme: {
    keys: { ctrl: true, shift: true, key: 't' },
    description: '切换主题'
  },
  
  // 帮助
  help: {
    keys: { key: '?' },
    description: '显示帮助'
  },
  
  // 关闭弹窗
  escape: {
    keys: { key: 'Escape' },
    description: '关闭弹窗'
  }
}

// 格式化快捷键显示
export const formatShortcut = (keys) => {
  const parts = []
  if (keys.ctrl) parts.push('Ctrl')
  if (keys.alt) parts.push('Alt')
  if (keys.shift) parts.push('Shift')
  if (keys.key) {
    const key = keys.key.length === 1 ? keys.key.toUpperCase() : keys.key
    parts.push(key)
  }
  return parts.join('+')
}