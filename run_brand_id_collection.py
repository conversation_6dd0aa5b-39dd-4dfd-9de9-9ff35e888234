#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
品牌ID收集一键启动脚本
自动运行数据收集和分析流程
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

def print_header():
    """打印程序头部信息"""
    print("🎯" + "="*58 + "🎯")
    print("🚀 拼多多品牌ID数据收集工具 - 一键启动")
    print("🎯" + "="*58 + "🎯")
    print("📋 功能: 自动收集品牌ID数据并生成映射建议")
    print("🎯 目标: 冰箱、空调、热水器、洗衣机 (共240个样本)")
    print("⏱️  预计耗时: 10-15分钟")
    print("="*60)

def check_prerequisites():
    """检查运行前提条件"""
    print("\n🔍 检查运行环境...")
    
    # 检查Cookie配置
    cookie_files = [
        "config/cookies.json",
        "browser_data/cookies/pdd.json"
    ]
    
    cookie_found = False
    for cookie_file in cookie_files:
        if Path(cookie_file).exists():
            try:
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if data:  # 有内容
                        cookie_found = True
                        print(f"✅ 找到Cookie配置: {cookie_file}")
                        break
            except:
                continue
    
    if not cookie_found:
        print("❌ 未找到有效的Cookie配置!")
        print("\n📝 请先配置Cookie:")
        print("1. 运行: python -m backend.api_server")
        print("2. 访问: http://localhost:8000")
        print("3. 在Cookie管理页面配置拼多多Cookie")
        print("4. 或者手动编辑 config/cookies.json 文件")
        return False
    
    # 检查必要的目录
    data_dir = Path("data")
    if not data_dir.exists():
        data_dir.mkdir(parents=True, exist_ok=True)
        print("✅ 创建数据目录")
    
    print("✅ 环境检查通过")
    return True

async def run_data_collection():
    """运行数据收集"""
    print("\n📊 开始数据收集阶段...")
    print("⏱️  这可能需要5-10分钟，请耐心等待...")
    
    try:
        # 动态导入避免初始化问题
        from brand_id_collector import BrandIDCollector
        
        collector = BrandIDCollector()
        result = await collector.collect_brand_data()
        
        if result["success"]:
            print(f"\n✅ 数据收集完成!")
            print(f"📊 处理关键词: {result['keywords_processed']} 个")
            print(f"📦 收集商品: {result['total_goods_collected']} 个")
            print(f"🏷️  发现品牌ID: {result['unique_brand_ids']} 个")
            print(f"⏱️  耗时: {result['duration']:.1f} 秒")
            return True
        else:
            print(f"\n❌ 数据收集失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"\n❌ 数据收集出错: {e}")
        logger.error(f"数据收集出错: {e}")
        return False

def run_data_analysis():
    """运行数据分析"""
    print("\n🔍 开始数据分析阶段...")
    
    try:
        from brand_id_analyzer import BrandIDAnalyzer
        
        analyzer = BrandIDAnalyzer()
        success = analyzer.run_analysis()
        
        if success:
            print("\n✅ 数据分析完成!")
            return True
        else:
            print("\n❌ 数据分析失败!")
            return False
            
    except Exception as e:
        print(f"\n❌ 数据分析出错: {e}")
        logger.error(f"数据分析出错: {e}")
        return False

def show_results():
    """显示结果摘要"""
    print("\n📋 查看结果文件...")
    
    # 检查生成的文件
    result_files = [
        ("原始数据", "data/api_responses/"),
        ("详细分析", "data/analysis/brand_id_analysis_detailed.json"),
        ("新映射建议", "data/analysis/new_brand_mappings.json"),
        ("高质量映射", "data/analysis/high_quality_mappings.json")
    ]
    
    print("\n📁 生成的文件:")
    for name, path in result_files:
        if Path(path).exists():
            if Path(path).is_dir():
                file_count = len(list(Path(path).glob("*.json")))
                print(f"  ✅ {name}: {path} ({file_count} 个文件)")
            else:
                file_size = Path(path).stat().st_size
                print(f"  ✅ {name}: {path} ({file_size} bytes)")
        else:
            print(f"  ❌ {name}: {path} (未生成)")

def show_implementation_guide():
    """显示实施指南"""
    print("\n🔧 实施新映射的步骤:")
    print("1. 查看分析结果:")
    print("   cat data/analysis/high_quality_mappings.json")
    print()
    print("2. 编辑处理器文件:")
    print("   编辑 src/data/processor.py")
    print("   在 brand_id_mapping 字典中添加新映射")
    print()
    print("3. 测试验证:")
    print("   python -c \"from src.data.processor import DataProcessor; p=DataProcessor(); print(len(p.brand_id_mapping))\"")
    print()
    print("4. 查看详细实现代码:")
    print("   查看 data/analysis/brand_id_analysis_detailed.json 中的 implementation 部分")

async def main():
    """主函数"""
    print_header()
    
    # 检查前提条件
    if not check_prerequisites():
        input("\n按回车键退出...")
        return 1
    
    # 询问用户是否继续
    print(f"\n⚠️  注意事项:")
    print("- 确保网络连接稳定")
    print("- 确保已配置有效的拼多多Cookie")
    print("- 整个过程可能需要10-15分钟")
    print("- 过程中请勿关闭浏览器窗口")
    
    response = input("\n是否开始收集? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("已取消操作")
        return 0
    
    start_time = datetime.now()
    
    try:
        # 第一阶段：数据收集
        collection_success = await run_data_collection()
        
        if not collection_success:
            print("\n❌ 数据收集失败，无法继续分析")
            return 1
        
        # 第二阶段：数据分析
        analysis_success = run_data_analysis()
        
        if not analysis_success:
            print("\n❌ 数据分析失败")
            return 1
        
        # 显示结果
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        print("\n🎉 品牌ID收集流程完成!")
        print(f"⏱️  总耗时: {total_duration:.1f} 秒")
        
        show_results()
        show_implementation_guide()
        
        print("\n✨ 收集完成! 请查看生成的分析报告。")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        logger.error(f"运行出错: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n程序已停止")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
