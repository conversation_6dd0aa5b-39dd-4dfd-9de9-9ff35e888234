"""
CDP浏览器管理器 - 基于MediaCrawler实现
负责启动和管理通过CDP连接的浏览器
"""
import os
import socket
import asyncio
import httpx
from typing import Optional, Dict, List
from playwright.async_api import Playwright, <PERSON><PERSON>er, BrowserContext, Page
from loguru import logger

from .browser_launcher import BrowserLauncher
from ..utils.helpers import load_config


class CDPBrowserManager:
    """
    CDP浏览器管理器，负责启动和管理通过CDP连接的浏览器
    """

    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化CDP浏览器管理器"""
        self.config = load_config(config_path)
        self.cdp_config = self.config.get("cdp", {})
        
        self.launcher = BrowserLauncher()
        self.browser: Optional[Browser] = None
        self.browser_context: Optional[BrowserContext] = None
        self.debug_port: Optional[int] = None
        
        # 初始化Cookie管理器
        from .cookie_manager import CookieManager
        self.cookie_manager = CookieManager()
        
        logger.info("CDP浏览器管理器初始化完成")

    async def launch_and_connect(
        self,
        playwright: Playwright,
        playwright_proxy: Optional[Dict] = None,
        user_agent: Optional[str] = None,
        headless: bool = False,
    ) -> BrowserContext:
        """
        启动浏览器并通过CDP连接
        
        Args:
            playwright: Playwright实例
            playwright_proxy: 代理配置（CDP模式下需要在浏览器启动前配置）
            user_agent: 用户代理（可选）
            headless: 是否无头模式
            
        Returns:
            BrowserContext: 浏览器上下文
        """
        try:
            # 1. 检测浏览器路径
            browser_path = await self._get_browser_path()

            # 2. 获取可用端口
            self.debug_port = self.launcher.find_available_port(
                self.cdp_config.get("debug_port", 9222)
            )

            # 3. 启动浏览器
            await self._launch_browser(browser_path, headless)

            # 4. 通过CDP连接
            await self._connect_via_cdp(playwright)

            # 5. 创建浏览器上下文
            browser_context = await self._create_browser_context(
                playwright_proxy, user_agent
            )

            self.browser_context = browser_context
            
            # 6. 加载保存的Cookie
            if self.cdp_config.get("save_login_state", True):
                await self.cookie_manager.load_cookies(browser_context, "pdd")
                logger.info("已尝试加载保存的Cookie")
            
            return browser_context

        except Exception as e:
            logger.error(f"CDP浏览器启动失败: {e}")
            await self.cleanup()
            raise

    async def _get_browser_path(self) -> str:
        """获取浏览器路径"""
        # 优先使用用户自定义路径
        custom_path = self.cdp_config.get("custom_browser_path")
        if custom_path and os.path.isfile(custom_path):
            logger.info(f"使用自定义浏览器路径: {custom_path}")
            return custom_path

        # 自动检测浏览器路径
        browser_paths = self.launcher.detect_browser_paths()

        if not browser_paths:
            # 生成详细的错误信息和解决方案
            error_msg = self._generate_browser_not_found_error()
            raise RuntimeError(error_msg)

        browser_path = browser_paths[0]  # 使用第一个找到的浏览器
        browser_name, browser_version = self.launcher.get_browser_info(browser_path)

        logger.info(f"检测到浏览器: {browser_name} ({browser_version})")
        logger.info(f"浏览器路径: {browser_path}")

        return browser_path

    async def _test_cdp_connection(self, debug_port: int) -> bool:
        """测试CDP连接是否可用"""
        try:
            # 简单的socket连接测试
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(5)
                result = s.connect_ex(("localhost", debug_port))
                if result == 0:
                    logger.info(f"CDP端口 {debug_port} 可访问")
                    return True
                else:
                    logger.warning(f"CDP端口 {debug_port} 不可访问")
                    return False
        except Exception as e:
            logger.warning(f"CDP连接测试失败: {e}")
            return False

    async def _launch_browser(self, browser_path: str, headless: bool):
        """启动浏览器进程"""
        # 设置用户数据目录（如果启用了保存登录状态）
        user_data_dir = None
        if self.cdp_config.get("save_login_state", True):
            user_data_dir = os.path.join(
                os.getcwd(),
                "browser_data",
                f"cdp_{self.cdp_config.get('user_data_dir', 'pdd_user_data_dir')}",
            )
            os.makedirs(user_data_dir, exist_ok=True)
            logger.info(f"用户数据目录: {user_data_dir}")

        # 启动浏览器
        self.launcher.browser_process = await self.launcher.launch_browser(
            browser_path=browser_path,
            debug_port=self.debug_port,
            headless=headless,
            user_data_dir=user_data_dir,
        )

        # 等待浏览器准备就绪
        launch_timeout = self.cdp_config.get("launch_timeout", 30)
        if not await self.launcher.wait_for_browser_ready(
            self.debug_port, launch_timeout
        ):
            raise RuntimeError(f"浏览器在 {launch_timeout} 秒内未能启动")

        # 额外等待一秒让CDP服务完全启动
        await asyncio.sleep(1)

        # 测试CDP连接
        if not await self._test_cdp_connection(self.debug_port):
            logger.warning("CDP连接测试失败，但将继续尝试连接")

    async def _get_browser_websocket_url(self, debug_port: int) -> str:
        """获取浏览器的WebSocket连接URL"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"http://localhost:{debug_port}/json/version", timeout=10
                )
                if response.status_code == 200:
                    data = response.json()
                    ws_url = data.get("webSocketDebuggerUrl")
                    if ws_url:
                        logger.info(f"获取到浏览器WebSocket URL: {ws_url}")
                        return ws_url
                    else:
                        raise RuntimeError("未找到webSocketDebuggerUrl")
                else:
                    raise RuntimeError(f"HTTP {response.status_code}: {response.text}")
        except Exception as e:
            logger.error(f"获取WebSocket URL失败: {e}")
            raise

    async def _connect_via_cdp(self, playwright: Playwright):
        """通过CDP连接到浏览器"""
        try:
            # 获取正确的WebSocket URL
            ws_url = await self._get_browser_websocket_url(self.debug_port)
            logger.info(f"正在通过CDP连接到浏览器: {ws_url}")

            # 使用Playwright的connectOverCDP方法连接
            self.browser = await playwright.chromium.connect_over_cdp(ws_url)

            if self.browser.is_connected():
                logger.info("成功连接到浏览器")
                logger.info(f"浏览器上下文数量: {len(self.browser.contexts)}")
            else:
                raise RuntimeError("CDP连接失败")

        except Exception as e:
            logger.error(f"CDP连接失败: {e}")
            raise

    async def _create_browser_context(
        self, playwright_proxy: Optional[Dict] = None, user_agent: Optional[str] = None
    ) -> BrowserContext:
        """创建或获取浏览器上下文"""
        if not self.browser:
            raise RuntimeError("浏览器未连接")

        # 获取现有上下文或创建新的上下文
        contexts = self.browser.contexts

        if contexts:
            # 使用现有的第一个上下文（保持登录状态）
            browser_context = contexts[0]
            logger.info("使用现有的浏览器上下文")
        else:
            # 创建新的上下文
            context_options = {
                "viewport": {"width": 1920, "height": 1080},
                "accept_downloads": True,
            }

            # 设置用户代理
            if user_agent:
                context_options["user_agent"] = user_agent
                logger.info(f"设置用户代理: {user_agent}")

            # 注意：CDP模式下代理设置可能不生效，因为浏览器已经启动
            if playwright_proxy:
                logger.warning(
                    "警告: CDP模式下代理设置可能不生效，"
                    "建议在浏览器启动前配置系统代理或浏览器代理扩展"
                )

            browser_context = await self.browser.new_context(**context_options)
            logger.info("创建新的浏览器上下文")

        return browser_context

    async def add_stealth_script(self, script_path: str = "libs/stealth.min.js"):
        """添加反检测脚本"""
        if self.browser_context and os.path.exists(script_path):
            try:
                await self.browser_context.add_init_script(path=script_path)
                logger.info(f"已添加反检测脚本: {script_path}")
            except Exception as e:
                logger.warning(f"添加反检测脚本失败: {e}")

    async def add_cookies(self, cookies: list):
        """添加Cookie"""
        if self.browser_context:
            try:
                await self.browser_context.add_cookies(cookies)
                logger.info(f"已添加 {len(cookies)} 个Cookie")
            except Exception as e:
                logger.warning(f"添加Cookie失败: {e}")

    async def get_cookies(self) -> list:
        """获取当前Cookie"""
        if self.browser_context:
            try:
                cookies = await self.browser_context.cookies()
                return cookies
            except Exception as e:
                logger.warning(f"获取Cookie失败: {e}")
                return []
        return []

    async def cleanup(self):
        """清理资源"""
        try:
            # 保存Cookie（如果配置了保存登录状态）
            if self.browser_context and self.cdp_config.get("save_login_state", True):
                try:
                    await self.cookie_manager.save_cookies(self.browser_context, "pdd")
                    logger.info("已保存Cookie")
                except Exception as e:
                    logger.warning(f"保存Cookie失败: {e}")
            
            # 关闭浏览器上下文
            if self.browser_context:
                await self.browser_context.close()
                self.browser_context = None
                logger.info("浏览器上下文已关闭")

            # 断开浏览器连接
            if self.browser:
                await self.browser.close()
                self.browser = None
                logger.info("浏览器连接已断开")

            # 关闭浏览器进程（如果配置为自动关闭）
            if self.cdp_config.get("auto_close_browser", True):
                await self.launcher.cleanup()
            else:
                logger.info("浏览器进程保持运行（auto_close_browser=False）")

        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def is_connected(self) -> bool:
        """检查是否已连接到浏览器"""
        return self.browser is not None and self.browser.is_connected()

    async def get_browser_info(self) -> Dict[str, any]:
        """获取浏览器信息"""
        if not self.browser:
            return {}

        try:
            version = self.browser.version
            contexts_count = len(self.browser.contexts)

            return {
                "version": version,
                "contexts_count": contexts_count,
                "debug_port": self.debug_port,
                "is_connected": self.is_connected(),
            }
        except Exception as e:
            logger.warning(f"获取浏览器信息失败: {e}")
            return {}

    def _generate_browser_not_found_error(self) -> str:
        """
        生成详细的浏览器未找到错误信息
        """
        import platform
        system = platform.system()

        error_msg = "❌ 未找到可用的浏览器！\n\n"

        if system == "Windows":
            error_msg += "🔍 已搜索以下位置：\n"
            error_msg += "  • C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\n"
            error_msg += "  • C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\n"
            error_msg += "  • %USERPROFILE%\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe\n"
            error_msg += "  • C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe\n"
            error_msg += "  • 便携版和自定义安装路径\n\n"

            error_msg += "💡 解决方案：\n"
            error_msg += "  1. 安装Chrome浏览器：https://www.google.com/chrome/\n"
            error_msg += "  2. 安装Edge浏览器：https://www.microsoft.com/edge\n"
            error_msg += "  3. 如果已安装但在非标准位置，请在config/settings.yaml中设置：\n"
            error_msg += "     cdp:\n"
            error_msg += "       custom_browser_path: \"D:\\Your\\Chrome\\Path\\chrome.exe\"\n\n"

        elif system == "Darwin":  # macOS
            error_msg += "🔍 已搜索以下位置：\n"
            error_msg += "  • /Applications/Google Chrome.app\n"
            error_msg += "  • /Applications/Microsoft Edge.app\n"
            error_msg += "  • ~/Applications/Google Chrome.app\n\n"

            error_msg += "💡 解决方案：\n"
            error_msg += "  1. 安装Chrome：brew install --cask google-chrome\n"
            error_msg += "  2. 安装Edge：brew install --cask microsoft-edge\n"
            error_msg += "  3. 或从官网下载安装\n\n"

        else:  # Linux
            error_msg += "🔍 已搜索以下位置：\n"
            error_msg += "  • /usr/bin/google-chrome\n"
            error_msg += "  • /usr/bin/chromium\n"
            error_msg += "  • /snap/bin/chromium\n"
            error_msg += "  • /usr/bin/microsoft-edge\n\n"

            error_msg += "💡 解决方案：\n"
            error_msg += "  1. Ubuntu/Debian: sudo apt install google-chrome-stable\n"
            error_msg += "  2. CentOS/RHEL: sudo yum install google-chrome-stable\n"
            error_msg += "  3. 或安装Chromium: sudo apt install chromium-browser\n\n"

        error_msg += "🔧 高级配置：\n"
        error_msg += "  如果浏览器安装在自定义位置，请在配置文件中指定路径：\n"
        error_msg += "  config/settings.yaml -> cdp -> custom_browser_path\n\n"

        error_msg += "📞 需要帮助？请检查：\n"
        error_msg += "  • 浏览器是否正确安装\n"
        error_msg += "  • 浏览器可执行文件是否有执行权限\n"
        error_msg += "  • 防火墙或安全软件是否阻止了浏览器启动"

        return error_msg