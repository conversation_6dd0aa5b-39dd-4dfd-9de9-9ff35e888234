"""
工具函数模块
提供配置加载、文件操作等通用功能
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载YAML配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置数据
    """
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            logger.error(f"配置文件不存在: {config_path}")
            return {}
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        logger.debug(f"成功加载配置文件: {config_path}")
        return config or {}
        
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}


def load_json(json_path: str) -> Dict[str, Any]:
    """
    加载JSON文件
    
    Args:
        json_path: JSON文件路径
        
    Returns:
        Dict[str, Any]: JSON数据
    """
    try:
        json_file = Path(json_path)
        if not json_file.exists():
            logger.error(f"JSON文件不存在: {json_path}")
            return {}
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.debug(f"成功加载JSON文件: {json_path}")
        return data or {}
        
    except Exception as e:
        logger.error(f"加载JSON文件失败: {e}")
        return {}


def save_json(data: Dict[str, Any], json_path: str) -> bool:
    """
    保存数据到JSON文件
    
    Args:
        data: 要保存的数据
        json_path: JSON文件路径
        
    Returns:
        bool: 是否保存成功
    """
    try:
        json_file = Path(json_path)
        json_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.debug(f"成功保存JSON文件: {json_path}")
        return True
        
    except Exception as e:
        logger.error(f"保存JSON文件失败: {e}")
        return False


def ensure_dir(dir_path: str) -> bool:
    """
    确保目录存在
    
    Args:
        dir_path: 目录路径
        
    Returns:
        bool: 是否成功
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化后的大小
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除非法字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    import re
    
    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    filename = re.sub(illegal_chars, '_', filename)
    
    # 移除前后空格
    filename = filename.strip()
    
    # 限制长度
    if len(filename) > 200:
        filename = filename[:200]
    
    return filename


def get_timestamp_string() -> str:
    """
    获取时间戳字符串
    
    Returns:
        str: 时间戳字符串
    """
    import datetime
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")


def parse_number_from_text(text: str) -> Optional[float]:
    """
    从文本中解析数字
    
    Args:
        text: 包含数字的文本
        
    Returns:
        Optional[float]: 解析出的数字
    """
    import re
    
    if not text:
        return None
    
    # 移除逗号和空格
    text = str(text).replace(',', '').replace(' ', '')
    
    # 提取数字（包括小数）
    numbers = re.findall(r'\d+\.?\d*', text)
    
    if numbers:
        try:
            return float(numbers[0])
        except ValueError:
            pass
    
    return None


def format_price(price: Any) -> str:
    """
    格式化价格
    
    Args:
        price: 价格值
        
    Returns:
        str: 格式化后的价格
    """
    if price is None:
        return "0.00"
    
    try:
        if isinstance(price, str):
            # 从字符串中提取数字
            price_num = parse_number_from_text(price)
            if price_num is not None:
                return f"{price_num:.2f}"
        elif isinstance(price, (int, float)):
            return f"{float(price):.2f}"
    except Exception:
        pass
    
    return "0.00"


def format_sales(sales: Any) -> str:
    """
    格式化销量
    
    Args:
        sales: 销量值
        
    Returns:
        str: 格式化后的销量
    """
    if sales is None:
        return "0"
    
    try:
        if isinstance(sales, str):
            # 提取数字
            sales_num = parse_number_from_text(sales)
            if sales_num is not None:
                return str(int(sales_num))
            # 如果包含中文单位，保留原文
            if any(char in sales for char in ['万', '千', '件', '笔']):
                return sales
        elif isinstance(sales, (int, float)):
            return str(int(sales))
    except Exception:
        pass
    
    return "0"


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    截断文本
    
    Args:
        text: 原始文本
        max_length: 最大长度
        
    Returns:
        str: 截断后的文本
    """
    if not text:
        return ""
    
    text = str(text).strip()
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."


def validate_url(url: str) -> bool:
    """
    验证URL格式
    
    Args:
        url: URL字符串
        
    Returns:
        bool: 是否为有效URL
    """
    import re
    
    if not url:
        return False
    
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def clean_text(text: str) -> str:
    """
    清理文本，移除多余空格和特殊字符
    
    Args:
        text: 原始文本
        
    Returns:
        str: 清理后的文本
    """
    if not text:
        return ""
    
    import re
    
    # 转换为字符串
    text = str(text)
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    
    # 移除多余空格
    text = re.sub(r'\s+', ' ', text)
    
    # 移除前后空格
    text = text.strip()
    
    return text
