{"browser_paths": {"windows": {"chrome": ["%PROGRAMFILES%\\Google\\Chrome\\Application\\chrome.exe", "%PROGRAMFILES(X86)%\\Google\\Chrome\\Application\\chrome.exe", "%LOCALAPPDATA%\\Google\\Chrome\\Application\\chrome.exe", "%LOCALAPPDATA%\\Google\\Chrome Beta\\Application\\chrome.exe", "%LOCALAPPDATA%\\Google\\Chrome Dev\\Application\\chrome.exe", "%LOCALAPPDATA%\\Google\\Chrome SxS\\Application\\chrome.exe"], "edge": ["%PROGRAMFILES%\\Microsoft\\Edge\\Application\\msedge.exe", "%PROGRAMFILES(X86)%\\Microsoft\\Edge\\Application\\msedge.exe"]}, "macos": {"chrome": ["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta", "/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev", "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary"], "edge": ["/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge", "/Applications/Microsoft Edge Beta.app/Contents/MacOS/Microsoft Edge Beta", "/Applications/Microsoft Edge Dev.app/Contents/MacOS/Microsoft Edge Dev", "/Applications/Microsoft Edge Canary.app/Contents/MacOS/Microsoft Edge Canary"]}, "linux": {"chrome": ["/usr/bin/google-chrome", "/usr/bin/google-chrome-stable", "/usr/bin/google-chrome-beta", "/usr/bin/google-chrome-unstable", "/usr/bin/chromium-browser", "/usr/bin/chromium", "/snap/bin/chromium"], "edge": ["/usr/bin/microsoft-edge", "/usr/bin/microsoft-edge-stable", "/usr/bin/microsoft-edge-beta", "/usr/bin/microsoft-edge-dev"]}}, "preferred_browsers": ["chrome", "edge"], "debug_port_range": {"start": 9222, "end": 9322}}