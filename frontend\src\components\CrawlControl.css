.crawl-control {
  animation: fadeIn 0.5s ease;
}

.control-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-title svg {
  color: var(--primary);
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.control-buttons .btn {
  flex: 1;
}

.btn-large {
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
  background: #e0e0e0 !important;
  color: #999 !important;
  border-color: #d0d0d0 !important;
}

.btn-disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

.btn-disabled svg {
  color: #999 !important;
}

.export-section {
  margin-top: 20px;
}

.divider {
  height: 1px;
  background: var(--border);
  margin: 20px 0;
}

.control-tips {
  margin-top: 24px;
  padding: 16px;
  background: var(--secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 12px 0;
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tips-list li {
  font-size: 12px;
  color: var(--text-secondary);
  padding-left: 20px;
  position: relative;
}

.tips-list li::before {
  content: '•';
  position: absolute;
  left: 8px;
  color: var(--primary);
  font-weight: bold;
}

/* 按钮状态动画 */
.btn svg {
  transition: transform 0.2s ease;
}

.btn:hover svg {
  transform: scale(1.1);
}

.btn:active svg {
  transform: scale(0.9);
}

/* 脉动动画用于运行状态 */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.running-indicator {
  position: relative;
}

.running-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid var(--primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-ring 2s ease-out infinite;
}