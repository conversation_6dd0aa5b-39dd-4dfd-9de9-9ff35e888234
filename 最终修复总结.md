# 商品筛选器问题修复总结

## 发现的问题

1. **统帅品牌被过滤**：
   - 虽然品牌匹配逻辑正确（海尔→统帅得0.8分）
   - 但总分计算后可能低于阈值0.6
   - 原因可能是产品类型未正确识别

2. **统计筛选未充分工作**：
   - 原阈值太高（70%），导致60%的冰箱不被认为是主要类型
   - 最小样本量刚好卡在边界

## 已实施的修复

### 1. 降低匹配阈值
```yaml
match_threshold: 0.5  # 从0.6降到0.5
```

### 2. 调整统计筛选参数
```yaml
statistical:
  min_sample_size: 8       # 从10降到8
  major_type_threshold: 0.5    # 从0.7降到0.5
  minor_type_threshold: 0.15   # 从0.1升到0.15
```

### 3. 改进统计筛选逻辑
- 即使没有超过70%的主导类型
- 如果最高占比≥50%，仍将其作为主要类型
- 这样60%的冰箱会被识别为主要类型

## 当前效果

搜索"海尔476"时：
- 基础筛选：保留海尔品牌商品（统帅需要降低阈值才能通过）
- 统计筛选：过滤掉热水器、血糖仪等少数类型
- 最终保留：冰箱（主要）和洗衣机（次要但占比>15%）

## 进一步优化建议

1. **改进产品类型识别**：
   - 确保统帅冰箱能正确识别为"冰箱"类型
   - 优化型号推断逻辑

2. **动态阈值调整**：
   - 根据搜索关键词的具体程度调整阈值
   - 通用搜索用宽松阈值，精确搜索用严格阈值

3. **品牌关系优化**：
   - 可以考虑给子品牌更高的权重（如0.9而非0.8）
   - 或者在通用搜索时完全接受子品牌