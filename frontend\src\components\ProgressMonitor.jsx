import React, { useState, useEffect } from 'react'
import './ProgressMonitor.css'

function ProgressMonitor({ status, progress, onError }) {
  const [animatedProgress, setAnimatedProgress] = useState(0)
  const [elapsedTime, setElapsedTime] = useState(0)
  
  // 计算进度百分比
  const progressPercentage = progress.targetItems > 0
    ? Math.round((progress.itemsCollected / progress.targetItems) * 100)
    : 0

  // 平滑动画更新进度
  useEffect(() => {
    const animationDuration = 500 // ms
    const steps = 30
    const stepDuration = animationDuration / steps
    const increment = (progressPercentage - animatedProgress) / steps

    let currentStep = 0
    const timer = setInterval(() => {
      if (currentStep < steps) {
        setAnimatedProgress(prev => prev + increment)
        currentStep++
      } else {
        setAnimatedProgress(progressPercentage)
        clearInterval(timer)
      }
    }, stepDuration)

    return () => clearInterval(timer)
  }, [progressPercentage])

  // 更新已用时间
  useEffect(() => {
    if (status === 'running' && progress.startTime) {
      const timer = setInterval(() => {
        setElapsedTime(Date.now() - progress.startTime)
      }, 1000)
      return () => clearInterval(timer)
    } else if (status === 'completed' && progress.startTime) {
      // 完成时计算最终用时
      setElapsedTime(Date.now() - progress.startTime)
    }
  }, [status, progress.startTime])

  // 格式化时间
  const formatDuration = (milliseconds) => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟 ${seconds % 60}秒`
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`
    } else {
      return `${seconds}秒`
    }
  }

  // 计算采集速度（商品/分钟）
  const calculateSpeed = () => {
    if (!progress.startTime || progress.itemsCollected === 0 || elapsedTime === 0) return 0
    const itemsPerSecond = progress.itemsCollected / (elapsedTime / 1000)
    return Math.round(itemsPerSecond * 60) // 每分钟的数量
  }

  // 计算预计剩余时间
  const calculateRemainingTime = () => {
    if (!progress.startTime || progress.itemsCollected === 0 || status !== 'running') return null
    
    const remainingItems = progress.targetItems - progress.itemsCollected
    const itemsPerMs = progress.itemsCollected / elapsedTime
    const estimatedRemainingTime = remainingItems / itemsPerMs
    
    return formatDuration(estimatedRemainingTime)
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return (
          <div className="status-icon running">
            <svg className="spin" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2a10 10 0 00-9.95 9h2.02a8 8 0 117.93 9 8 8 0 01-7.93-7H2.05A10 10 0 1012 2z" fill="currentColor"/>
            </svg>
          </div>
        )
      case 'paused':
        return (
          <div className="status-icon paused">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
            </svg>
          </div>
        )
      case 'completed':
        return (
          <div className="status-icon completed">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
            </svg>
          </div>
        )
      default:
        return (
          <div className="status-icon idle">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <circle cx="12" cy="12" r="10"/>
            </svg>
          </div>
        )
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'running': return '正在爬取'
      case 'paused': return '已暂停'
      case 'completed': return '爬取完成'
      default: return '等待开始'
    }
  }

  return (
    <div className="progress-monitor card">
      <div className="progress-header">
        <h2 className="progress-title">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
          </svg>
          实时进度
        </h2>
        <div className="progress-status">
          {getStatusIcon()}
          <span className="status-text">{getStatusText()}</span>
        </div>
      </div>
      
      <div className="progress-content">
        {/* 进度条 */}
        <div className="progress-bar-container">
          <div className="progress-bar">
            <div 
              className={`progress-fill ${status}`}
              style={{ width: `${animatedProgress}%` }}
            >
              {animatedProgress > 10 && (
                <span className="progress-percentage">{animatedProgress}%</span>
              )}
            </div>
          </div>
          {animatedProgress <= 10 && animatedProgress > 0 && (
            <span className="progress-percentage-outside">{animatedProgress}%</span>
          )}
        </div>
        
        {/* 统计信息 */}
        <div className="progress-stats">
          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                <path fillRule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H4v10h12V5h-2a1 1 0 100-2 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">已采集商品</span>
              <span className="stat-value">{progress.itemsCollected} / {progress.targetItems}</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">当前页数</span>
              <span className="stat-value">{progress.currentPage} / {progress.totalPages}</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">用时</span>
              <span className="stat-value">
                {progress.startTime ? formatDuration(elapsedTime) : '--'}
              </span>
            </div>
          </div>

          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
                <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">采集速度</span>
              <span className="stat-value">
                {calculateSpeed()} 个/分钟
              </span>
            </div>
          </div>
          
          {status === 'running' && (
            <div className="stat-item">
              <div className="stat-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
              </div>
              <div className="stat-info">
                <span className="stat-label">预计剩余</span>
                <span className="stat-value">
                  {calculateRemainingTime() || '计算中...'}
                </span>
              </div>
            </div>
          )}
        </div>
        
        {/* 实时动态效果 */}
        {status === 'running' && (
          <div className="live-indicator">
            <span className="live-dot"></span>
            <span className="live-text">实时爬取中...</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProgressMonitor