// API服务配置
const API_BASE_URL = 'http://localhost:8000/api'
const WS_URL = 'ws://localhost:8000/ws'

// API请求封装
class ApiService {
  // 开始爬取
  async startCrawl(config) {
    const response = await fetch(`${API_BASE_URL}/crawl/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keyword: config.keyword,
        max_pages: config.maxPages,
        target_count: config.targetCount,
        sort_type: config.sortType
      })
    })
    
    if (!response.ok) {
      throw new Error('开始爬取失败')
    }
    
    return await response.json()
  }
  
  // 暂停爬取
  async pauseCrawl(taskId) {
    const response = await fetch(`${API_BASE_URL}/crawl/pause`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ task_id: taskId })
    })
    
    if (!response.ok) {
      throw new Error('暂停爬取失败')
    }
    
    return await response.json()
  }
  
  // 恢复爬取
  async resumeCrawl(taskId) {
    const response = await fetch(`${API_BASE_URL}/crawl/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ task_id: taskId })
    })
    
    if (!response.ok) {
      throw new Error('恢复爬取失败')
    }
    
    return await response.json()
  }
  
  // 停止爬取
  async stopCrawl(taskId) {
    const response = await fetch(`${API_BASE_URL}/crawl/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ task_id: taskId })
    })
    
    if (!response.ok) {
      throw new Error('停止爬取失败')
    }
    
    return await response.json()
  }
  
  // 获取爬取状态
  async getCrawlStatus(taskId) {
    const response = await fetch(`${API_BASE_URL}/crawl/status/${taskId}`)
    
    if (!response.ok) {
      throw new Error('获取状态失败')
    }
    
    return await response.json()
  }
  
  // 导出数据
  async exportData(taskId) {
    const response = await fetch(`${API_BASE_URL}/crawl/export/${taskId}`)
    
    if (!response.ok) {
      throw new Error('导出数据失败')
    }
    
    // 处理文件下载
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `拼多多商品数据_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }
  
  // 创建WebSocket连接
  createWebSocket(taskId, callbacks) {
    const ws = new WebSocket(`${WS_URL}/${taskId}`)
    
    ws.onopen = () => {
      console.log('WebSocket连接已建立')
      callbacks.onOpen && callbacks.onOpen()
    }
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      callbacks.onMessage && callbacks.onMessage(data)
    }
    
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      callbacks.onError && callbacks.onError(error)
    }
    
    ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      callbacks.onClose && callbacks.onClose()
    }
    
    return ws
  }
}

export default new ApiService()