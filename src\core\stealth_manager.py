"""
Stealth脚本管理器 - 基于MediaCrawler实现
管理和注入反检测脚本
"""
from pathlib import Path
from typing import Optional
from playwright.async_api import Page, BrowserContext
from loguru import logger


class StealthManager:
    """
    Stealth脚本管理器
    负责管理和注入stealth反检测脚本
    """
    
    def __init__(self, script_path: str = "libs/stealth.min.js"):
        """
        初始化Stealth管理器
        
        Args:
            script_path: stealth脚本文件路径
        """
        self.script_path = Path(script_path)
        self.stealth_script: Optional[str] = None
        self._load_stealth_script()
        
    def _load_stealth_script(self):
        """加载stealth.min.js脚本"""
        try:
            if self.script_path.exists():
                self.stealth_script = self.script_path.read_text(encoding="utf-8")
                logger.info(f"成功加载stealth脚本: {self.script_path}")
            else:
                logger.warning(f"stealth脚本文件不存在: {self.script_path}")
                self.stealth_script = None
        except Exception as e:
            logger.error(f"加载stealth脚本失败: {e}")
            self.stealth_script = None
    
    async def apply_stealth_to_context(self, context: BrowserContext):
        """
        应用stealth脚本到浏览器上下文
        
        Args:
            context: 浏览器上下文
        """
        if not self.stealth_script:
            logger.warning("stealth脚本未加载，跳过注入")
            return
            
        try:
            # 使用add_init_script注入脚本，确保每个新页面都会执行
            await context.add_init_script(self.stealth_script)
            logger.info("已将stealth脚本注入到浏览器上下文")
        except Exception as e:
            logger.error(f"注入stealth脚本到上下文失败: {e}")
    
    async def apply_stealth_to_page(self, page: Page):
        """
        应用stealth脚本到单个页面
        
        Args:
            page: 页面对象
        """
        if not self.stealth_script:
            logger.warning("stealth脚本未加载，跳过注入")
            return
            
        try:
            # 直接在页面上执行脚本
            await page.add_init_script(self.stealth_script)
            logger.info("已将stealth脚本注入到页面")
        except Exception as e:
            logger.error(f"注入stealth脚本到页面失败: {e}")
    
    def is_loaded(self) -> bool:
        """
        检查stealth脚本是否已加载
        
        Returns:
            bool: 脚本是否已加载
        """
        return self.stealth_script is not None
    
    def reload_script(self):
        """重新加载stealth脚本"""
        logger.info("重新加载stealth脚本...")
        self._load_stealth_script()
    
    @staticmethod
    async def apply_additional_evasions(page: Page):
        """
        应用额外的反检测措施
        这些是MediaCrawler中除了stealth.min.js之外的额外反检测代码
        
        Args:
            page: 页面对象
        """
        try:
            # 移除webdriver属性
            await page.evaluate("""
                // 移除 navigator.webdriver
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                
                // 修复 chrome 对象
                if (!window.chrome) {
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };
                }
                
                // 修复权限查询
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // 隐藏自动化扩展
                const originalToString = Function.prototype.toString;
                Function.prototype.toString = function() {
                    if (this === window.navigator.permissions.query) {
                        return 'function query() { [native code] }';
                    }
                    return originalToString.call(this);
                };
            """)
            
            logger.debug("已应用额外的反检测措施")
            
        except Exception as e:
            logger.warning(f"应用额外反检测措施失败: {e}")
    
    @staticmethod
    def get_stealth_config() -> dict:
        """
        获取stealth相关的配置建议
        基于MediaCrawler的最佳实践
        
        Returns:
            dict: stealth配置建议
        """
        return {
            "stealth": {
                "enabled": True,
                "script_path": "libs/stealth.min.js",
                "apply_to_context": True,  # 应用到上下文（推荐）
                "apply_to_page": False,    # 应用到每个页面（可选）
                "additional_evasions": True  # 应用额外的反检测措施
            },
            "recommendations": {
                "use_cdp_mode": True,  # 推荐使用CDP模式
                "headless": False,     # 推荐使用有头模式
                "user_data_dir": True, # 推荐保存用户数据
                "avoid_automation_flags": True  # 避免自动化标志
            }
        }