"""
重试机制模块
提供智能重试功能，支持指数退避策略
"""

import asyncio
import random
from typing import Any, Callable, Optional, Type, Union, Tuple
from tenacity import (
    retry, stop_after_attempt, wait_exponential, 
    retry_if_exception_type, before_sleep_log
)
from loguru import logger

from .helpers import load_config


class RetryManager:
    """重试管理器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化重试管理器"""
        self.config = load_config(config_path)
        self.retry_config = self.config.get("retry", {})
        
        # 重试配置
        self.max_attempts = self.retry_config.get("max_attempts", 2)
        self.wait_time = self.retry_config.get("wait_time", 1)
        self.backoff_factor = self.retry_config.get("backoff_factor", 2)
        self.max_wait_time = self.retry_config.get("max_wait_time", 30)
        
        # 新增超时配置
        self.operation_timeout = self.retry_config.get("operation_timeout", 15)
        self.total_timeout = self.retry_config.get("total_timeout", 300)
        
        logger.info("重试管理器初始化完成（优化版）")
    
    def create_retry_decorator(
        self,
        max_attempts: Optional[int] = None,
        wait_time: Optional[float] = None,
        backoff_factor: Optional[float] = None,
        max_wait_time: Optional[float] = None,
        retry_exceptions: Tuple[Type[Exception], ...] = (Exception,),
        stop_on_exceptions: Tuple[Type[Exception], ...] = ()
    ):
        """
        创建重试装饰器
        
        Args:
            max_attempts: 最大重试次数
            wait_time: 初始等待时间
            backoff_factor: 退避倍数
            max_wait_time: 最大等待时间
            retry_exceptions: 需要重试的异常类型
            stop_on_exceptions: 不重试的异常类型
            
        Returns:
            装饰器函数
        """
        # 使用配置的默认值
        max_attempts = max_attempts or self.max_attempts
        wait_time = wait_time or self.wait_time
        backoff_factor = backoff_factor or self.backoff_factor
        max_wait_time = max_wait_time or self.max_wait_time
        
        # 创建重试条件
        retry_condition = retry_if_exception_type(retry_exceptions)
        
        # 如果指定了不重试的异常，则排除它们
        if stop_on_exceptions:
            def should_retry(exception):
                if isinstance(exception, stop_on_exceptions):
                    return False
                return isinstance(exception, retry_exceptions)
            
            retry_condition = should_retry
        
        return retry(
            stop=stop_after_attempt(max_attempts),
            wait=wait_exponential(
                multiplier=wait_time,
                max=max_wait_time,
                exp_base=backoff_factor
            ),
            retry=retry_condition,
            before_sleep=before_sleep_log(logger, "WARNING"),
            reraise=True
        )
    
    async def retry_async_operation(
        self,
        operation: Callable,
        *args,
        max_attempts: Optional[int] = None,
        wait_time: Optional[float] = None,
        backoff_factor: Optional[float] = None,
        retry_exceptions: Tuple[Type[Exception], ...] = (Exception,),
        stop_on_exceptions: Tuple[Type[Exception], ...] = (),
        **kwargs
    ) -> Any:
        """
        异步重试操作
        
        Args:
            operation: 要重试的异步操作
            *args: 操作的位置参数
            max_attempts: 最大重试次数
            wait_time: 初始等待时间
            backoff_factor: 退避倍数
            retry_exceptions: 需要重试的异常类型
            stop_on_exceptions: 不重试的异常类型
            **kwargs: 操作的关键字参数
            
        Returns:
            操作结果
        """
        max_attempts = max_attempts or self.max_attempts
        wait_time = wait_time or self.wait_time
        backoff_factor = backoff_factor or self.backoff_factor
        
        last_exception = None
        
        for attempt in range(1, max_attempts + 1):
            try:
                logger.debug(f"尝试执行操作 (第 {attempt}/{max_attempts} 次)")
                
                if asyncio.iscoroutinefunction(operation):
                    result = await operation(*args, **kwargs)
                else:
                    result = operation(*args, **kwargs)
                
                if attempt > 1:
                    logger.info(f"操作在第 {attempt} 次尝试后成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # 检查是否为不重试的异常
                if stop_on_exceptions and isinstance(e, stop_on_exceptions):
                    logger.error(f"遇到不重试异常: {type(e).__name__}: {str(e)}")
                    raise e
                
                # 检查是否为需要重试的异常
                if not isinstance(e, retry_exceptions):
                    logger.error(f"遇到不在重试范围内的异常: {type(e).__name__}: {str(e)}")
                    raise e
                
                if attempt < max_attempts:
                    # 计算等待时间（指数退避 + 随机抖动）
                    base_wait = wait_time * (backoff_factor ** (attempt - 1))
                    jitter = random.uniform(0.1, 0.3) * base_wait
                    actual_wait = min(base_wait + jitter, self.max_wait_time)
                    
                    logger.warning(
                        f"操作失败 (第 {attempt}/{max_attempts} 次): {type(e).__name__}: {str(e)}, "
                        f"{actual_wait:.1f}秒后重试"
                    )
                    
                    await asyncio.sleep(actual_wait)
                else:
                    logger.error(f"操作在 {max_attempts} 次尝试后仍然失败")
        
        # 如果所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception
    
    def retry_sync_operation(
        self,
        operation: Callable,
        *args,
        max_attempts: Optional[int] = None,
        wait_time: Optional[float] = None,
        backoff_factor: Optional[float] = None,
        retry_exceptions: Tuple[Type[Exception], ...] = (Exception,),
        stop_on_exceptions: Tuple[Type[Exception], ...] = (),
        **kwargs
    ) -> Any:
        """
        同步重试操作
        
        Args:
            operation: 要重试的同步操作
            *args: 操作的位置参数
            max_attempts: 最大重试次数
            wait_time: 初始等待时间
            backoff_factor: 退避倍数
            retry_exceptions: 需要重试的异常类型
            stop_on_exceptions: 不重试的异常类型
            **kwargs: 操作的关键字参数
            
        Returns:
            操作结果
        """
        import time
        
        max_attempts = max_attempts or self.max_attempts
        wait_time = wait_time or self.wait_time
        backoff_factor = backoff_factor or self.backoff_factor
        
        last_exception = None
        
        for attempt in range(1, max_attempts + 1):
            try:
                logger.debug(f"尝试执行操作 (第 {attempt}/{max_attempts} 次)")
                result = operation(*args, **kwargs)
                
                if attempt > 1:
                    logger.info(f"操作在第 {attempt} 次尝试后成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # 检查是否为不重试的异常
                if stop_on_exceptions and isinstance(e, stop_on_exceptions):
                    logger.error(f"遇到不重试异常: {type(e).__name__}: {str(e)}")
                    raise e
                
                # 检查是否为需要重试的异常
                if not isinstance(e, retry_exceptions):
                    logger.error(f"遇到不在重试范围内的异常: {type(e).__name__}: {str(e)}")
                    raise e
                
                if attempt < max_attempts:
                    # 计算等待时间（指数退避 + 随机抖动）
                    base_wait = wait_time * (backoff_factor ** (attempt - 1))
                    jitter = random.uniform(0.1, 0.3) * base_wait
                    actual_wait = min(base_wait + jitter, self.max_wait_time)
                    
                    logger.warning(
                        f"操作失败 (第 {attempt}/{max_attempts} 次): {type(e).__name__}: {str(e)}, "
                        f"{actual_wait:.1f}秒后重试"
                    )
                    
                    time.sleep(actual_wait)
                else:
                    logger.error(f"操作在 {max_attempts} 次尝试后仍然失败")
        
        # 如果所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception


# 创建全局重试管理器实例
retry_manager = RetryManager()


# 便捷装饰器
def retry_on_failure(
    max_attempts: int = 3,
    wait_time: float = 2,
    backoff_factor: float = 2,
    retry_exceptions: Tuple[Type[Exception], ...] = (Exception,),
    stop_on_exceptions: Tuple[Type[Exception], ...] = ()
):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        wait_time: 初始等待时间
        backoff_factor: 退避倍数
        retry_exceptions: 需要重试的异常类型
        stop_on_exceptions: 不重试的异常类型
    """
    return retry_manager.create_retry_decorator(
        max_attempts=max_attempts,
        wait_time=wait_time,
        backoff_factor=backoff_factor,
        retry_exceptions=retry_exceptions,
        stop_on_exceptions=stop_on_exceptions
    )


# 便捷函数
async def retry_async(operation: Callable, *args, **kwargs) -> Any:
    """异步重试便捷函数"""
    return await retry_manager.retry_async_operation(operation, *args, **kwargs)


def retry_sync(operation: Callable, *args, **kwargs) -> Any:
    """同步重试便捷函数"""
    return retry_manager.retry_sync_operation(operation, *args, **kwargs)
