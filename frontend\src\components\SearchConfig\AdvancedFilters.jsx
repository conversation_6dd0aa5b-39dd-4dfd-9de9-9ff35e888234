import React, { useState } from 'react'
import './AdvancedFilters.css'

export default function AdvancedFilters({ filters, onChange, disabled }) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleFilterChange = (filterKey, value) => {
    onChange({
      ...filters,
      [filterKey]: value
    })
  }

  // 价格范围预设
  const pricePresets = [
    { label: '不限', min: '', max: '' },
    { label: '0-50', min: 0, max: 50 },
    { label: '50-200', min: 50, max: 200 },
    { label: '200-500', min: 200, max: 500 },
    { label: '500+', min: 500, max: '' }
  ]

  // 销量范围预设
  const salesPresets = [
    { label: '不限', min: '' },
    { label: '100+', min: 100 },
    { label: '1000+', min: 1000 },
    { label: '1万+', min: 10000 },
    { label: '10万+', min: 100000 }
  ]

  const activeFiltersCount = Object.values(filters).filter(v => v && v !== '').length

  return (
    <div className="advanced-filters">
      <button
        className={`filters-toggle ${isExpanded ? 'expanded' : ''}`}
        onClick={() => setIsExpanded(!isExpanded)}
        type="button"
      >
        <div className="toggle-content">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M5 10H15M2 5H18M8 15H12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          </svg>
          <span>高级筛选</span>
          {activeFiltersCount > 0 && (
            <span className="active-count">{activeFiltersCount}</span>
          )}
        </div>
        <svg 
          className="toggle-arrow" 
          width="16" 
          height="16" 
          viewBox="0 0 16 16" 
          fill="none"
        >
          <path d="M4 6L8 10L12 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>

      {isExpanded && (
        <div className="filters-content">
          {/* 价格范围 */}
          <div className="filter-group">
            <label className="filter-label">价格范围（元）</label>
            <div className="price-filter">
              <input
                type="number"
                className="price-input"
                placeholder="最低价"
                value={filters.minPrice || ''}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                disabled={disabled}
                min="0"
              />
              <span className="price-separator">-</span>
              <input
                type="number"
                className="price-input"
                placeholder="最高价"
                value={filters.maxPrice || ''}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                disabled={disabled}
                min="0"
              />
            </div>
            <div className="filter-presets">
              {pricePresets.map(preset => (
                <button
                  key={preset.label}
                  className={`preset-button ${
                    filters.minPrice == preset.min && filters.maxPrice == preset.max ? 'active' : ''
                  }`}
                  onClick={() => {
                    handleFilterChange('minPrice', preset.min)
                    handleFilterChange('maxPrice', preset.max)
                  }}
                  disabled={disabled}
                  type="button"
                >
                  {preset.label}
                </button>
              ))}
            </div>
          </div>

          {/* 销量筛选 */}
          <div className="filter-group">
            <label className="filter-label">最低销量</label>
            <div className="sales-filter">
              <input
                type="number"
                className="sales-input"
                placeholder="输入最低销量"
                value={filters.minSales || ''}
                onChange={(e) => handleFilterChange('minSales', e.target.value)}
                disabled={disabled}
                min="0"
              />
            </div>
            <div className="filter-presets">
              {salesPresets.map(preset => (
                <button
                  key={preset.label}
                  className={`preset-button ${
                    filters.minSales == preset.min ? 'active' : ''
                  }`}
                  onClick={() => handleFilterChange('minSales', preset.min)}
                  disabled={disabled}
                  type="button"
                >
                  {preset.label}
                </button>
              ))}
            </div>
          </div>

          {/* 商品类型 */}
          <div className="filter-group">
            <label className="filter-label">商品类型</label>
            <div className="type-filter">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={filters.hasCoupon || false}
                  onChange={(e) => handleFilterChange('hasCoupon', e.target.checked)}
                  disabled={disabled}
                />
                <span className="checkbox-text">仅看有优惠券</span>
              </label>
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={filters.isBrandGoods || false}
                  onChange={(e) => handleFilterChange('isBrandGoods', e.target.checked)}
                  disabled={disabled}
                />
                <span className="checkbox-text">仅看品牌商品</span>
              </label>
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={filters.hasPromotion || false}
                  onChange={(e) => handleFilterChange('hasPromotion', e.target.checked)}
                  disabled={disabled}
                />
                <span className="checkbox-text">仅看促销商品</span>
              </label>
            </div>
          </div>

          {/* 发货地 */}
          <div className="filter-group">
            <label className="filter-label">发货地</label>
            <input
              type="text"
              className="location-input"
              placeholder="输入发货地，如：北京、上海"
              value={filters.location || ''}
              onChange={(e) => handleFilterChange('location', e.target.value)}
              disabled={disabled}
            />
          </div>

          {/* 清除筛选 */}
          {activeFiltersCount > 0 && (
            <button
              className="clear-filters"
              onClick={() => onChange({})}
              disabled={disabled}
              type="button"
            >
              清除所有筛选条件
            </button>
          )}
        </div>
      )}
    </div>
  )
}