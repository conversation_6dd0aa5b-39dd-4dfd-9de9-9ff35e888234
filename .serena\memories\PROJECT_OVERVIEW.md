# 拼多多爬虫项目总览

## 项目简介
一个基于Playwright的拼多多商品数据爬虫，支持关键词搜索、数据收集和Excel导出。

## 技术栈
- **语言**: Python 3.8+
- **核心框架**: Playwright (浏览器自动化)
- **异步框架**: asyncio
- **数据处理**: pandas, openpyxl
- **日志**: loguru
- **其他**: tenacity (重试机制), PyYAML (配置管理)

## 项目结构
```
pdd-crawler/
├── src/
│   ├── main.py              # 主程序入口
│   ├── core/                # 核心功能模块
│   │   ├── browser_manager.py      # 浏览器管理
│   │   ├── anti_detection.py       # 反风控检测
│   │   ├── api_interceptor.py      # API数据拦截
│   │   ├── account_manager.py      # 账号管理
│   │   ├── login_detector.py       # 登录状态检测
│   │   ├── scroll_manager.py       # 智能滚动管理
│   │   ├── sort_manager.py         # 排序管理
│   │   ├── user_agent_manager.py   # User-Agent管理
│   │   └── lightweight_fingerprint.py # 设备指纹
│   ├── data/                # 数据处理模块
│   │   ├── processor.py     # 数据处理器
│   │   └── exporter.py      # Excel导出器
│   └── utils/               # 工具函数
│       ├── helpers.py       # 辅助函数
│       ├── logger.py        # 日志配置
│       └── retry.py         # 重试机制
├── config/                  # 配置文件
│   ├── settings.yaml        # 主配置文件
│   ├── cookies.json         # Cookie配置
│   └── user_agents.json     # User-Agent列表
├── logs/                    # 日志目录
├── output/                  # 数据输出目录
└── run_main.py             # 启动脚本
```

## 核心功能

### 1. 商品搜索
- 支持多关键词批量搜索
- 直接URL导航，避免搜索框操作
- URL格式: `search_result.html?search_key={keyword}`

### 2. 数据收集
- API拦截方式获取数据
- 实时数据去重（基于商品ID）
- 线程安全的并发数据收集

### 3. 反风控措施
- 多级风控检测和处理
- User-Agent轮换
- 设备指纹伪装
- 人类行为模拟

### 4. 数据导出
- Excel格式导出
- 按关键词分组
- 支持多种排序方式（需启用）

## 使用方法

### 基础使用
```bash
# 安装依赖
pip install -r requirements.txt

# 运行爬虫
python run_main.py
```

### 配置说明
1. **设置Cookie**: 编辑 `config/cookies.json`，添加真实的拼多多Cookie
2. **配置关键词**: 在 `config/settings.yaml` 中设置搜索关键词
3. **调整参数**: 根据需要调整爬取速度、目标数量等参数

### 启用排序功能
编辑 `config/settings.yaml`：
```yaml
sorting:
  enabled: true  # 改为true启用排序
```

## 最新优化 (2025-01-27)

### 性能优化
- 浏览器启动参数从168个精简到40个
- 启动速度提升约30%
- 内存占用减少

### 稳定性增强
- 修复time模块导入错误
- 添加线程安全机制
- 完善错误处理

### 代码质量
- 实现数据去重功能
- 清理冗余代码
- 提升可读性

## 注意事项

1. **Cookie要求**: 必须使用真实的拼多多登录Cookie
2. **频率控制**: 避免过快爬取触发风控
3. **本地使用**: 这是私人项目，仅供本地使用
4. **数据隐私**: Cookie等敏感信息已在.gitignore中排除

## 项目状态
- **健康度**: 🟢 正常运行
- **代码质量**: ✅ 优化完成
- **性能**: ⚡ 快速稳定
- **维护性**: 📝 结构清晰