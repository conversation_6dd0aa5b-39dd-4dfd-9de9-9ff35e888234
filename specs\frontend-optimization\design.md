# 拼多多爬虫前端技术设计文档

## 架构概览

### 整体架构
```mermaid
graph TB
    subgraph Frontend
        UI[React UI层]
        SM[状态管理 Zustand]
        API[API客户端]
        WS[WebSocket客户端]
    end
    
    subgraph Backend
        FastAPI[FastAPI服务器]
        Crawler[爬虫引擎]
        CM[Cookie管理器]
    end
    
    UI --> SM
    SM --> API
    SM --> WS
    API --> FastAPI
    WS --> FastAPI
    FastAPI --> Crawler
    FastAPI --> CM
```

## 组件设计

### 1. Cookie管理模块

#### 数据模型
```typescript
interface Cookie {
  name: string;
  value: string;
  domain: string;
  path: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

interface CookieState {
  cookies: Cookie[];
  isValid: boolean;
  expiryWarning: boolean;
  lastUpdated: Date | null;
}
```

#### 组件结构
```
components/
├── CookieManager/
│   ├── CookieManager.jsx       # 主容器组件
│   ├── CookieInput.jsx         # Cookie输入组件
│   ├── CookieDisplay.jsx       # Cookie显示组件
│   ├── CookieValidator.jsx     # Cookie验证组件
│   └── CookieManager.css       # 样式文件
```

#### API设计
```typescript
// Cookie相关API
POST   /api/cookies/validate    // 验证Cookie
POST   /api/cookies/save        // 保存Cookie
GET    /api/cookies/current     // 获取当前Cookie
DELETE /api/cookies/clear       // 清除Cookie
GET    /api/cookies/status      // 检查Cookie状态
```

### 2. 搜索配置增强

#### 数据模型
```typescript
interface SearchConfig {
  keywords: string[];           // 关键词数组
  targetCount: number;         // 目标数量
  maxPages: number;            // 最大页数
  sortType: SortType;          // 排序方式
  filters?: SearchFilters;     // 可选的过滤条件
}

interface SearchFilters {
  priceRange?: [number, number];
  salesRange?: [number, number];
  shopType?: 'all' | 'mall' | 'personal';
}

enum SortType {
  DEFAULT = 'default',
  SALES_DESC = 'sales_desc',
  PRICE_ASC = 'price_asc',
  PRICE_DESC = 'price_desc',
  NEWEST = 'newest'
}
```

#### 组件设计
```
components/
├── SearchConfig/
│   ├── SearchConfig.jsx         # 主配置组件
│   ├── KeywordInput.jsx         # 关键词输入（支持标签）
│   ├── ParameterSettings.jsx    # 参数设置
│   ├── SortSelector.jsx         # 排序选择器
│   └── SearchConfig.css
```

### 3. 实时进度监控

#### 数据模型
```typescript
interface CrawlProgress {
  taskId: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error';
  currentKeyword: string;
  currentKeywordIndex: number;
  totalKeywords: number;
  itemsCollected: number;
  targetItems: number;
  startTime: Date;
  estimatedEndTime?: Date;
  errors: CrawlError[];
}

interface CrawlError {
  timestamp: Date;
  message: string;
  retryCount: number;
  resolved: boolean;
}
```

#### WebSocket消息格式
```typescript
interface WSMessage {
  type: 'progress' | 'data' | 'error' | 'completed';
  data: any;
  timestamp: number;
}
```

#### 组件设计
```
components/
├── ProgressMonitor/
│   ├── ProgressMonitor.jsx      # 主监控组件
│   ├── ProgressBar.jsx          # 进度条
│   ├── StatusIndicator.jsx      # 状态指示器
│   ├── ErrorDisplay.jsx         # 错误显示
│   └── ProgressMonitor.css
```

### 4. 数据预览增强

#### 数据模型
```typescript
interface ProductData {
  goodsId: string;
  goodsName: string;
  price: number;
  salesTip: string;
  mallName: string;
  thumbUrl: string;
  hdThumbUrl?: string;
  brandName?: string;
  categoryName?: string;
  couponDiscount?: number;
  // ... 其他29个字段
}

interface PreviewState {
  data: ProductData[];
  selectedItem: ProductData | null;
  viewMode: 'table' | 'grid';
  sortBy: string;
  filterBy: any;
}
```

#### 组件设计
```
components/
├── DataPreview/
│   ├── DataPreview.jsx          # 主预览组件
│   ├── DataTable.jsx            # 表格视图
│   ├── DataGrid.jsx             # 网格视图
│   ├── ProductDetail.jsx        # 商品详情
│   ├── ImagePreview.jsx         # 图片预览
│   └── DataPreview.css
```

### 5. 状态管理设计

使用Zustand进行全局状态管理：

```typescript
interface AppStore {
  // Cookie状态
  cookieState: CookieState;
  updateCookie: (cookie: string) => Promise<void>;
  clearCookie: () => Promise<void>;
  
  // 搜索配置
  searchConfig: SearchConfig;
  updateSearchConfig: (config: Partial<SearchConfig>) => void;
  
  // 爬虫状态
  crawlProgress: CrawlProgress;
  startCrawl: () => Promise<void>;
  pauseCrawl: () => Promise<void>;
  stopCrawl: () => Promise<void>;
  
  // 数据预览
  previewData: ProductData[];
  addPreviewData: (data: ProductData[]) => void;
  clearPreviewData: () => void;
  
  // WebSocket连接
  wsConnection: WebSocket | null;
  connectWebSocket: (taskId: string) => void;
  disconnectWebSocket: () => void;
}
```

## UI/UX设计规范

### 设计系统
- **主色调**: #FF5C00 (拼多多橙色)
- **辅助色**: #52C41A (成功), #FF4D4F (错误), #FAAD14 (警告)
- **字体**: -apple-system, "PingFang SC", "Microsoft YaHei"
- **圆角**: 4px (小), 8px (中), 12px (大)
- **阴影**: 0 2px 8px rgba(0,0,0,0.1)

### 响应式断点
- 桌面: ≥ 1200px
- 平板: 768px - 1199px
- 移动: < 768px (不支持)

### 交互规范
- 所有按钮点击后显示loading状态
- 表单验证实时进行，错误信息即时显示
- 危险操作（清除Cookie）需要二次确认
- 成功/错误提示使用toast通知

## 安全设计

### Cookie处理
1. Cookie在传输时使用Base64编码
2. 敏感Cookie字段（如PDDAccessToken）在前端显示时部分遮蔽
3. Cookie验证在后端进行，前端只做格式检查

### API安全
1. 所有API请求包含CSRF token
2. 实施请求频率限制
3. 错误信息不暴露系统细节

## 性能优化

### 渲染优化
1. 使用React.memo优化组件重渲染
2. 虚拟滚动处理大量数据展示
3. 图片懒加载
4. 代码分割，按需加载

### 数据优化
1. WebSocket消息批量处理，减少渲染次数
2. 数据去重在前端进行，减少传输量
3. 使用IndexedDB缓存历史数据

### 网络优化
1. API请求使用缓存策略
2. WebSocket断线重连机制
3. 请求失败自动重试（最多3次）

## 技术栈选择

### 核心依赖
- React 18.2.0
- Vite 5.0
- Zustand 4.4.0 (状态管理)
- Ant Design 5.12.0 (UI组件库)
- Axios 1.6.0 (HTTP客户端)
- Socket.io-client 4.6.0 (WebSocket)

### 开发依赖
- TypeScript 5.3.0
- ESLint + Prettier
- Vitest (单元测试)
- Playwright (E2E测试)

## 部署架构

```mermaid
graph LR
    User[用户浏览器] --> Nginx[Nginx反向代理]
    Nginx --> Frontend[前端静态资源]
    Nginx --> Backend[FastAPI后端]
    Backend --> Redis[Redis缓存]
    Backend --> Browser[Playwright浏览器]
```

## 开发规范

### 文件命名
- 组件文件: PascalCase (如 CookieManager.jsx)
- 工具文件: camelCase (如 apiClient.js)
- 样式文件: 与组件同名 (如 CookieManager.css)

### 代码规范
- 使用函数组件和Hooks
- Props使用解构赋值
- 使用async/await处理异步操作
- 错误边界包裹关键组件

### Git提交规范
- feat: 新功能
- fix: 修复bug
- refactor: 重构
- style: 样式调整
- docs: 文档更新