import React, { useState, useEffect } from 'react'
import { Input, InputNumber, Select, Form, Space, Tag, Tooltip, Alert } from 'antd'
import { SearchOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons'
import './SearchConfig.css'

interface SearchConfigProps {
  config: {
    keyword: string
    keywords: string
    maxPages: number
    targetCount: number
    sortType: string
  }
  onChange: (config: any) => void
  disabled?: boolean
}

const { Option } = Select

export default function SearchConfig({ config, onChange, disabled }: SearchConfigProps) {
  const [keywords, setKeywords] = useState<string[]>([])
  const [inputValue, setInputValue] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)

  // 解析已有的关键词
  useEffect(() => {
    if (config.keywords) {
      const parsed = config.keywords.split(',').map(k => k.trim()).filter(k => k)
      setKeywords(parsed)
    }
  }, [config.keywords])

  // 添加关键词
  const handleAddKeyword = () => {
    const trimmed = inputValue.trim()
    if (trimmed && !keywords.includes(trimmed)) {
      const newKeywords = [...keywords, trimmed]
      setKeywords(newKeywords)
      setInputValue('')
      onChange({
        ...config,
        keywords: newKeywords.join(','),
        keyword: newKeywords[0] || ''
      })
    }
  }

  // 删除关键词
  const handleRemoveKeyword = (keyword: string) => {
    const newKeywords = keywords.filter(k => k !== keyword)
    setKeywords(newKeywords)
    onChange({
      ...config,
      keywords: newKeywords.join(','),
      keyword: newKeywords[0] || ''
    })
  }

  // 更新配置
  const handleChange = (field: string, value: any) => {
    onChange({
      ...config,
      [field]: value
    })
  }

  // 计算预估数据
  const estimatedTime = Math.ceil(config.maxPages * 2.5)
  const estimatedItems = config.maxPages * 30

  return (
    <div className="search-config">
      <div className="config-header">
        <h3><SearchOutlined /> 搜索配置</h3>
        <Tooltip title="配置搜索参数以获取精准的商品数据">
          <InfoCircleOutlined className="info-icon" />
        </Tooltip>
      </div>

      <Form layout="vertical" className="config-form">
        {/* 关键词输入 */}
        <Form.Item
          label="搜索关键词"
          required
          help="输入商品名称、品牌或类别，按回车添加多个关键词"
        >
          <Space.Compact style={{ width: '100%' }}>
            <Input
              placeholder="输入关键词后按回车添加"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onPressEnter={handleAddKeyword}
              disabled={disabled}
              suffix={
                <Tooltip title="添加关键词">
                  <PlusOutlined 
                    onClick={handleAddKeyword} 
                    style={{ cursor: 'pointer', color: '#1890ff' }}
                  />
                </Tooltip>
              }
            />
          </Space.Compact>
          
          {/* 关键词标签展示 */}
          {keywords.length > 0 && (
            <div className="keywords-tags">
              {keywords.map((keyword) => (
                <Tag
                  key={keyword}
                  closable={!disabled}
                  onClose={() => handleRemoveKeyword(keyword)}
                  color="blue"
                >
                  {keyword}
                </Tag>
              ))}
              <span className="keywords-count">
                共 {keywords.length} 个关键词
              </span>
            </div>
          )}
        </Form.Item>

        {/* 基本参数 */}
        <div className="config-grid">
          <Form.Item
            label="爬取页数"
            help="每页约20-40个商品"
          >
            <InputNumber
              min={1}
              max={100}
              value={config.maxPages}
              onChange={(value) => handleChange('maxPages', value || 1)}
              disabled={disabled}
              style={{ width: '100%' }}
              addonAfter="页"
            />
          </Form.Item>

          <Form.Item
            label="目标数量"
            help="期望采集的商品总数"
          >
            <InputNumber
              min={1}
              max={10000}
              step={10}
              value={config.targetCount}
              onChange={(value) => handleChange('targetCount', value || 100)}
              disabled={disabled}
              style={{ width: '100%' }}
              addonAfter="个"
            />
          </Form.Item>
        </div>

        {/* 排序方式 */}
        <Form.Item
          label="排序方式"
          help="选择商品的排序规则"
        >
          <Select
            value={config.sortType}
            onChange={(value) => handleChange('sortType', value)}
            disabled={disabled}
            style={{ width: '100%' }}
          >
            <Option value="default">
              <Space>
                <span>综合排序</span>
                <span style={{ color: '#999', fontSize: '12px' }}>默认推荐</span>
              </Space>
            </Option>
            <Option value="sales">
              <Space>
                <span>销量优先</span>
                <span style={{ color: '#999', fontSize: '12px' }}>按销量从高到低</span>
              </Space>
            </Option>
            <Option value="price_asc">
              <Space>
                <span>价格升序</span>
                <span style={{ color: '#999', fontSize: '12px' }}>从低到高</span>
              </Space>
            </Option>
            <Option value="price_desc">
              <Space>
                <span>价格降序</span>
                <span style={{ color: '#999', fontSize: '12px' }}>从高到低</span>
              </Space>
            </Option>
            <Option value="new">
              <Space>
                <span>最新发布</span>
                <span style={{ color: '#999', fontSize: '12px' }}>最近上架商品</span>
              </Space>
            </Option>
          </Select>
        </Form.Item>

        {/* 高级选项 */}
        <div className="advanced-section">
          <a onClick={() => setShowAdvanced(!showAdvanced)}>
            {showAdvanced ? '收起' : '展开'}高级选项
          </a>
          
          {showAdvanced && (
            <Alert
              message="高级过滤功能"
              description="价格区间、店铺类型、发货地等高级过滤功能将在后续版本中推出"
              type="info"
              showIcon
              style={{ marginTop: '12px' }}
            />
          )}
        </div>

        {/* 预估信息 */}
        <div className="estimate-info">
          <div className="estimate-item">
            <span className="estimate-label">预计耗时：</span>
            <span className="estimate-value">约 {estimatedTime} 分钟</span>
          </div>
          <div className="estimate-item">
            <span className="estimate-label">预计商品数：</span>
            <span className="estimate-value">约 {estimatedItems} 个</span>
          </div>
        </div>
      </Form>
    </div>
  )
}