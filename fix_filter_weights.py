#!/usr/bin/env python3
"""
修复筛选器权重问题
"""

# 当前权重配置
brand_weight = 0.4
product_weight = 0.25
spec_weight = 0.25
keyword_weight = 0.1

# 统帅商品的典型得分
brand_score = 0.8  # 海尔->统帅
product_score = 1.0  # 冰箱->冰箱
spec_score = 0.5  # 没有规格要求时的默认分
keyword_score = 0.5  # 关键词部分匹配

# 计算总分
total_score = (
    brand_score * brand_weight +
    product_score * product_weight +
    spec_score * spec_weight +
    keyword_score * keyword_weight
)

print(f"统帅冰箱的总分计算：")
print(f"品牌得分: {brand_score} × {brand_weight} = {brand_score * brand_weight:.3f}")
print(f"产品得分: {product_score} × {product_weight} = {product_score * product_weight:.3f}")
print(f"规格得分: {spec_score} × {spec_weight} = {spec_score * spec_weight:.3f}")
print(f"关键词得分: {keyword_score} × {keyword_weight} = {keyword_score * keyword_weight:.3f}")
print(f"总分: {total_score:.3f}")
print(f"匹配阈值: 0.6")
print(f"是否通过: {'是' if total_score >= 0.6 else '否'}")