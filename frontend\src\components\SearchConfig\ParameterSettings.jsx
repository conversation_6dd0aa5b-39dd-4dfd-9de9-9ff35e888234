import React from 'react'
import './ParameterSettings.css'

export default function ParameterSettings({ 
  maxPages, 
  targetCount, 
  onMaxPagesChange, 
  onTargetCountChange, 
  disabled 
}) {
  // 参数配置信息
  const parameters = [
    {
      id: 'maxPages',
      label: '爬取页数',
      value: maxPages,
      onChange: onMaxPagesChange,
      min: 1,
      max: 100,
      step: 1,
      unit: '页',
      description: '每页包含约20-40个商品',
      helpText: '建议根据需求设置，过多会增加等待时间',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <rect x="3" y="2" width="14" height="16" rx="2" stroke="currentColor" strokeWidth="1.5"/>
          <path d="M7 6H13M7 10H13M7 14H10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
        </svg>
      )
    },
    {
      id: 'targetCount',
      label: '目标数量',
      value: targetCount,
      onChange: onTargetCountChange,
      min: 1,
      max: 10000,
      step: 10,
      unit: '个',
      description: '期望采集的商品总数',
      helpText: '实际数量可能因搜索结果而有所差异',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <rect x="2" y="2" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="11" y="2" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="2" y="11" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="11" y="11" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="1.5"/>
        </svg>
      )
    }
  ]

  const handleInputChange = (param, value) => {
    const numValue = parseInt(value) || param.min
    const clampedValue = Math.max(param.min, Math.min(param.max, numValue))
    param.onChange(clampedValue)
  }

  const handleIncrement = (param) => {
    const newValue = Math.min(param.max, param.value + param.step)
    param.onChange(newValue)
  }

  const handleDecrement = (param) => {
    const newValue = Math.max(param.min, param.value - param.step)
    param.onChange(newValue)
  }

  return (
    <div className="parameter-settings">
      <h3 className="settings-title">参数设置</h3>
      
      <div className="parameters-grid">
        {parameters.map(param => (
          <div key={param.id} className="parameter-card">
            <div className="parameter-header">
              <div className="parameter-icon">{param.icon}</div>
              <div className="parameter-info">
                <label className="parameter-label">{param.label}</label>
                <p className="parameter-description">{param.description}</p>
              </div>
            </div>
            
            <div className="parameter-control">
              <button
                className="control-button minus"
                onClick={() => handleDecrement(param)}
                disabled={disabled || param.value <= param.min}
                type="button"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M3 8H13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
              
              <div className="parameter-input-wrapper">
                <input
                  type="number"
                  className="parameter-input"
                  value={param.value}
                  onChange={(e) => handleInputChange(param, e.target.value)}
                  min={param.min}
                  max={param.max}
                  disabled={disabled}
                />
                <span className="parameter-unit">{param.unit}</span>
              </div>
              
              <button
                className="control-button plus"
                onClick={() => handleIncrement(param)}
                disabled={disabled || param.value >= param.max}
                type="button"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M8 3V13M3 8H13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
            
            <p className="parameter-help">{param.helpText}</p>
            
            {/* 进度条显示当前值 */}
            <div className="parameter-progress">
              <div 
                className="progress-bar"
                style={{ 
                  width: `${((param.value - param.min) / (param.max - param.min)) * 100}%` 
                }}
              />
            </div>
          </div>
        ))}
      </div>
      
      {/* 参数验证提示 */}
      <div className="parameters-validation">
        {targetCount > maxPages * 40 && (
          <div className="validation-warning">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 1.5A6.5 6.5 0 1014.5 8 6.5 6.5 0 008 1.5zm0 10a.75.75 0 110-1.5.75.75 0 010 1.5zm.75-3.5a.75.75 0 01-1.5 0V5a.75.75 0 011.5 0v3z"/>
            </svg>
            <span>目标数量可能超过可获取的最大商品数，建议增加爬取页数</span>
          </div>
        )}
      </div>
    </div>
  )
}