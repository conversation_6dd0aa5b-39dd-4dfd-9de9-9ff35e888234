#!/usr/bin/env python3
"""
应用所有修复方案
"""

import re
from pathlib import Path
from loguru import logger

def apply_subsidy_fix():
    """应用百亿补贴识别修复"""
    processor_file = Path("src/data/processor.py")
    content = processor_file.read_text(encoding='utf-8')
    
    # 查找并修改必需字段列表
    pattern = r"if field not in export_fields and field not in \[(.*?)\]:"
    match = re.search(pattern, content)
    
    if match:
        current_fields = match.group(1)
        if 'iconIds' not in current_fields:
            # 添加 iconIds 和 icon_list 到必需字段列表
            new_fields = current_fields.rstrip(']').rstrip() + ", 'iconIds', 'icon_list']"
            new_line = f"if field not in export_fields and field not in [{new_fields}:"
            
            content = re.sub(pattern, new_line, content)
            processor_file.write_text(content, encoding='utf-8')
            logger.info("✅ 已修复百亿补贴识别问题：添加 iconIds 和 icon_list 到必需字段列表")
            return True
        else:
            logger.info("ℹ️ iconIds 已在必需字段列表中，无需修改")
            return False
    else:
        logger.error("❌ 未找到需要修改的代码")
        return False

if __name__ == "__main__":
    logger.remove()
    logger.add(lambda msg: print(msg), format="{message}")
    
    logger.info("开始应用修复...")
    apply_subsidy_fix()