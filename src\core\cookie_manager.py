"""
Cookie管理器 - 基于MediaCrawler实现
负责Cookie的加载、保存和管理
"""

import json
import os
import time
from typing import List, Dict, Optional
from pathlib import Path
from playwright.async_api import BrowserContext
from loguru import logger


class CookieManager:
    """
    Cookie管理器
    负责管理浏览器Cookie的加载、保存和更新
    """
    
    def __init__(self, cookie_dir: str = "browser_data/cookies"):
        """
        初始化Cookie管理器
        
        Args:
            cookie_dir: Cookie存储目录
        """
        self.cookie_dir = Path(cookie_dir)
        self.cookie_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Cookie管理器初始化完成，存储目录: {self.cookie_dir}")
    
    def get_cookie_file_path(self, site_name: str) -> Path:
        """
        获取特定站点的Cookie文件路径
        
        Args:
            site_name: 站点名称（如'pdd'）
            
        Returns:
            Path: Cookie文件路径
        """
        return self.cookie_dir / f"{site_name}_cookies.json"
    
    async def load_cookies(self, context: BrowserContext, site_name: str) -> bool:
        """
        加载Cookie到浏览器上下文
        
        Args:
            context: 浏览器上下文
            site_name: 站点名称
            
        Returns:
            bool: 是否成功加载Cookie
        """
        cookie_file = self.get_cookie_file_path(site_name)
        
        if not cookie_file.exists():
            logger.warning(f"Cookie文件不存在: {cookie_file}")
            return False
        
        try:
            # 读取Cookie文件
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            
            if not cookies:
                logger.warning(f"Cookie文件为空: {cookie_file}")
                return False
            
            # 过滤有效的Cookie
            valid_cookies = self._filter_valid_cookies(cookies)
            
            if valid_cookies:
                # 添加Cookie到上下文
                await context.add_cookies(valid_cookies)
                logger.info(f"成功加载 {len(valid_cookies)} 个Cookie")
                return True
            else:
                logger.warning("没有有效的Cookie可加载")
                return False
                
        except Exception as e:
            logger.error(f"加载Cookie失败: {e}")
            return False
    
    async def save_cookies(self, context: BrowserContext, site_name: str) -> bool:
        """
        保存浏览器上下文的Cookie
        
        Args:
            context: 浏览器上下文
            site_name: 站点名称
            
        Returns:
            bool: 是否成功保存Cookie
        """
        try:
            # 获取当前所有Cookie
            cookies = await context.cookies()
            
            if not cookies:
                logger.warning("没有Cookie可保存")
                return False
            
            # 保存Cookie到文件
            cookie_file = self.get_cookie_file_path(site_name)
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, indent=2, ensure_ascii=False)
            
            logger.info(f"成功保存 {len(cookies)} 个Cookie到 {cookie_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存Cookie失败: {e}")
            return False
    
    def _filter_valid_cookies(self, cookies: List[Dict]) -> List[Dict]:
        """
        过滤有效的Cookie
        
        Args:
            cookies: Cookie列表
            
        Returns:
            List[Dict]: 过滤后的有效Cookie列表
        """
        valid_cookies = []
        current_time = int(time.time())
        
        for cookie in cookies:
            # 检查必要字段
            if not cookie.get("name") or not cookie.get("value"):
                continue
            
            # 跳过占位符Cookie
            if (cookie.get("name", "").startswith("请在此处") or 
                cookie.get("value", "").startswith("请在此处")):
                continue
            
            # 检查Cookie是否过期
            if "expires" in cookie:
                # 如果expires是字符串，尝试转换
                if isinstance(cookie["expires"], str):
                    try:
                        # 尝试解析时间戳
                        expires = float(cookie["expires"])
                        if expires < current_time:
                            logger.debug(f"Cookie {cookie['name']} 已过期，跳过")
                            continue
                    except:
                        # 无法解析，移除expires字段
                        cookie.pop("expires", None)
                elif isinstance(cookie["expires"], (int, float)):
                    if cookie["expires"] < current_time:
                        logger.debug(f"Cookie {cookie['name']} 已过期，跳过")
                        continue
            
            # 修复sameSite字段
            if "sameSite" in cookie:
                same_site = cookie["sameSite"]
                if same_site not in ["Strict", "Lax", "None"]:
                    cookie["sameSite"] = "Lax"  # 默认值
            
            valid_cookies.append(cookie)
        
        return valid_cookies
    
    def load_cookies_from_config(self, config_path: str = "config/cookies.json") -> List[Dict]:
        """
        从配置文件加载Cookie（兼容旧格式）
        
        Args:
            config_path: Cookie配置文件路径
            
        Returns:
            List[Dict]: Cookie列表
        """
        try:
            if not os.path.exists(config_path):
                logger.warning(f"Cookie配置文件不存在: {config_path}")
                return []
            
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 兼容旧格式
            cookies = data.get("cookies", []) if isinstance(data, dict) else data
            
            return self._filter_valid_cookies(cookies)
            
        except Exception as e:
            logger.error(f"加载Cookie配置文件失败: {e}")
            return []
    
    async def import_cookies_from_browser(self, context: BrowserContext, site_name: str) -> bool:
        """
        从浏览器导入Cookie（CDP模式下特别有用）
        
        Args:
            context: 浏览器上下文
            site_name: 站点名称
            
        Returns:
            bool: 是否成功导入
        """
        try:
            # 获取当前Cookie
            cookies = await context.cookies()
            
            if cookies:
                # 保存到特定站点的Cookie文件
                await self.save_cookies(context, site_name)
                
                # 同时更新配置文件格式的Cookie（兼容性）
                config_path = Path("config/cookies.json")
                # 确保目录存在
                config_path.parent.mkdir(parents=True, exist_ok=True)
                config_data = {"cookies": cookies}
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"成功从浏览器导入 {len(cookies)} 个Cookie")
                return True
            else:
                logger.warning("浏览器中没有Cookie可导入")
                return False
                
        except Exception as e:
            logger.error(f"从浏览器导入Cookie失败: {e}")
            return False
    
    def clear_cookies(self, site_name: str) -> bool:
        """
        清除特定站点的Cookie
        
        Args:
            site_name: 站点名称
            
        Returns:
            bool: 是否成功清除
        """
        try:
            cookie_file = self.get_cookie_file_path(site_name)
            
            if cookie_file.exists():
                cookie_file.unlink()
                logger.info(f"已清除 {site_name} 的Cookie")
                return True
            else:
                logger.info(f"{site_name} 没有保存的Cookie")
                return False
                
        except Exception as e:
            logger.error(f"清除Cookie失败: {e}")
            return False
    
    def list_saved_cookies(self) -> List[str]:
        """
        列出所有已保存的Cookie站点
        
        Returns:
            List[str]: 站点名称列表
        """
        sites = []
        
        for cookie_file in self.cookie_dir.glob("*_cookies.json"):
            site_name = cookie_file.stem.replace("_cookies", "")
            sites.append(site_name)
        
        return sites
    
    def parse_cookie_input(self, cookie_input: str) -> List[Dict]:
        """
        智能解析各种格式的cookie输入
        
        Args:
            cookie_input: Cookie输入（可以是字符串、JSON等格式）
            
        Returns:
            List[Dict]: 标准化的Cookie列表
        """
        cookies = []
        
        # 清理输入
        cookie_input = cookie_input.strip()
        
        # 尝试1：检查是否是JSON格式
        if cookie_input.startswith('[') or cookie_input.startswith('{'):
            try:
                parsed = json.loads(cookie_input)
                if isinstance(parsed, list):
                    return self._filter_valid_cookies(parsed)
                elif isinstance(parsed, dict):
                    # 如果是单个cookie对象，转为列表
                    return self._filter_valid_cookies([parsed])
            except json.JSONDecodeError:
                logger.debug("输入不是有效的JSON格式，尝试其他解析方式")
        
        # 尝试2：按分号分割的cookie字符串格式
        # 格式: "name1=value1; name2=value2; ..."
        cookie_pairs = []
        
        # 支持多种分隔符
        if '; ' in cookie_input:
            cookie_pairs = cookie_input.split('; ')
        elif ';' in cookie_input:
            cookie_pairs = cookie_input.split(';')
        elif '\n' in cookie_input:
            # 支持换行分隔的格式
            cookie_pairs = [line.strip() for line in cookie_input.split('\n') if line.strip()]
        else:
            # 可能只有一个cookie
            cookie_pairs = [cookie_input]
        
        # 解析每个cookie对
        for pair in cookie_pairs:
            pair = pair.strip()
            if '=' in pair:
                name, value = pair.split('=', 1)
                name = name.strip()
                value = value.strip()
                
                if name and value:
                    cookie = {
                        "name": name,
                        "value": value,
                        "domain": ".yangkeduo.com",
                        "path": "/",
                        "expires": 1888189891.0,  # 2029年
                        "httpOnly": False,
                        "secure": False,
                        "sameSite": "Lax"
                    }
                    
                    # 特殊处理重要的cookie
                    if name in ["PDDAccessToken", "jrpl"]:
                        cookie["httpOnly"] = True
                        cookie["secure"] = True
                    
                    cookies.append(cookie)
        
        if not cookies:
            logger.warning("无法从输入中解析出有效的Cookie")
            return []
        
        logger.info(f"成功解析 {len(cookies)} 个Cookie")
        return cookies
    
    async def import_cookies_from_string(self, cookie_string: str, site_name: str = "pdd") -> bool:
        """
        从字符串导入Cookie
        
        Args:
            cookie_string: Cookie字符串（支持多种格式）
            site_name: 站点名称
            
        Returns:
            bool: 是否成功导入
        """
        try:
            # 解析cookie
            cookies = self.parse_cookie_input(cookie_string)
            
            if not cookies:
                logger.error("无法解析Cookie字符串")
                return False
            
            # 保存到文件
            cookie_file = self.get_cookie_file_path(site_name)
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, indent=2, ensure_ascii=False)
            
            logger.info(f"成功导入 {len(cookies)} 个Cookie到 {cookie_file}")
            
            # 同时更新兼容格式的配置文件
            config_path = Path("config/cookies.json")
            # 确保目录存在
            config_path.parent.mkdir(parents=True, exist_ok=True)
            config_data = {"cookies": cookies}
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"导入Cookie失败: {e}")
            return False
    
    def update_cookie_from_file(self, file_path: str, site_name: str = "pdd") -> bool:
        """
        从文件读取并更新Cookie
        
        Args:
            file_path: Cookie文件路径
            site_name: 站点名称
            
        Returns:
            bool: 是否成功更新
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用智能解析
            cookies = self.parse_cookie_input(content)
            
            if cookies:
                # 保存到站点cookie文件
                cookie_file = self.get_cookie_file_path(site_name)
                with open(cookie_file, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, indent=2, ensure_ascii=False)
                
                logger.info(f"成功从 {file_path} 更新 {len(cookies)} 个Cookie")
                return True
            else:
                logger.error(f"无法从文件 {file_path} 解析Cookie")
                return False
                
        except Exception as e:
            logger.error(f"从文件更新Cookie失败: {e}")
            return False


