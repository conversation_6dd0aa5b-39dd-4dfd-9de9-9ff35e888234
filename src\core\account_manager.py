"""
账号管理器
处理429风控和账号切换
"""

import json
import time
import random
from typing import Dict, List, Optional
from pathlib import Path
from loguru import logger

from src.utils.helpers import load_json, save_json


class AccountManager:
    """账号管理器"""
    
    def __init__(self, config_path: str = "config"):
        """初始化账号管理器"""
        self.config_path = Path(config_path)
        self.cookies_file = self.config_path / "cookies.json"
        self.backup_cookies_file = self.config_path / "backup_cookies.json"
        self.account_status_file = self.config_path / "account_status.json"
        
        # 加载账号状态
        self.account_status = self._load_account_status()
        
        # 当前使用的账号
        self.current_account = None
        
        logger.info("账号管理器初始化完成")
    
    def _load_account_status(self) -> Dict:
        """加载账号状态"""
        try:
            if self.account_status_file.exists():
                return load_json(str(self.account_status_file))
            else:
                return {
                    "accounts": {},
                    "current_account_id": None,
                    "last_switch_time": 0
                }
        except Exception as e:
            logger.error(f"加载账号状态失败: {e}")
            return {
                "accounts": {},
                "current_account_id": None,
                "last_switch_time": 0
            }
    
    def _save_account_status(self) -> None:
        """保存账号状态"""
        try:
            save_json(str(self.account_status_file), self.account_status)
        except Exception as e:
            logger.error(f"保存账号状态失败: {e}")
    
    def check_account_status(self, response_status: int = None, error_message: str = None) -> bool:
        """
        检查账号状态
        
        Args:
            response_status: HTTP响应状态码
            error_message: 错误消息
            
        Returns:
            bool: True表示账号正常，False表示需要切换账号
        """
        current_time = time.time()
        account_id = self._get_current_account_id()
        
        # 检查429风控
        if response_status == 429:
            logger.warning(f"账号 {account_id} 遇到429风控")
            self._mark_account_blocked(account_id, current_time)
            return False
        
        # 检查其他风控指示器
        if error_message and any(keyword in error_message.lower() for keyword in [
            "too many requests", "请求过于频繁", "访问过于频繁",
            "系统繁忙", "网络繁忙", "请稍后再试"
        ]):
            logger.warning(f"账号 {account_id} 遇到风控: {error_message}")
            self._mark_account_blocked(account_id, current_time)
            return False
        
        # 账号正常，更新最后使用时间
        self._update_account_last_used(account_id, current_time)
        return True
    
    def _get_current_account_id(self) -> str:
        """获取当前账号ID"""
        if not self.current_account:
            # 从Cookie中提取账号ID
            try:
                cookies_data = load_json(str(self.cookies_file))
                cookies = cookies_data.get("cookies", [])
                
                for cookie in cookies:
                    if cookie.get("name") == "pdd_user_id":
                        self.current_account = cookie.get("value", "unknown")
                        break
                
                if not self.current_account:
                    self.current_account = "default"
                    
            except Exception as e:
                logger.error(f"获取账号ID失败: {e}")
                self.current_account = "default"
        
        return self.current_account
    
    def _mark_account_blocked(self, account_id: str, block_time: float) -> None:
        """标记账号被封"""
        if account_id not in self.account_status["accounts"]:
            self.account_status["accounts"][account_id] = {}
        
        self.account_status["accounts"][account_id].update({
            "status": "blocked",
            "block_time": block_time,
            "block_count": self.account_status["accounts"][account_id].get("block_count", 0) + 1,
            "last_error": "429 Too Many Requests"
        })
        
        self._save_account_status()
        logger.info(f"账号 {account_id} 已标记为被封状态")
    
    def _update_account_last_used(self, account_id: str, use_time: float) -> None:
        """更新账号最后使用时间"""
        if account_id not in self.account_status["accounts"]:
            self.account_status["accounts"][account_id] = {}
        
        self.account_status["accounts"][account_id].update({
            "status": "active",
            "last_used": use_time,
            "success_count": self.account_status["accounts"][account_id].get("success_count", 0) + 1
        })
        
        self._save_account_status()
    
    def get_available_account(self) -> Optional[Dict]:
        """
        获取可用的账号
        
        Returns:
            Dict: 可用的账号Cookie数据，如果没有可用账号返回None
        """
        current_time = time.time()
        
        # 检查当前账号是否可用
        current_account_id = self._get_current_account_id()
        if self._is_account_available(current_account_id, current_time):
            logger.info(f"当前账号 {current_account_id} 可用")
            return load_json(str(self.cookies_file))
        
        # 检查是否有备用账号
        if self.backup_cookies_file.exists():
            logger.info("尝试使用备用账号")
            backup_data = load_json(str(self.backup_cookies_file))
            
            # 获取备用账号ID
            backup_account_id = self._extract_account_id_from_cookies(backup_data.get("cookies", []))
            
            if self._is_account_available(backup_account_id, current_time):
                logger.info(f"切换到备用账号 {backup_account_id}")
                # 切换账号
                self._switch_to_backup_account()
                return backup_data
        
        logger.warning("没有可用的账号")
        return None
    
    def _is_account_available(self, account_id: str, current_time: float) -> bool:
        """检查账号是否可用"""
        if account_id not in self.account_status["accounts"]:
            return True  # 新账号，假设可用
        
        account_info = self.account_status["accounts"][account_id]
        
        # 检查是否被封
        if account_info.get("status") == "blocked":
            block_time = account_info.get("block_time", 0)
            # 被封超过1小时后可以重试
            if current_time - block_time > 3600:
                logger.info(f"账号 {account_id} 封禁时间已过，可以重试")
                return True
            else:
                remaining_time = 3600 - (current_time - block_time)
                logger.info(f"账号 {account_id} 还需等待 {remaining_time/60:.1f} 分钟")
                return False
        
        return True
    
    def _extract_account_id_from_cookies(self, cookies: List[Dict]) -> str:
        """从Cookie中提取账号ID"""
        for cookie in cookies:
            if cookie.get("name") == "pdd_user_id":
                return cookie.get("value", "unknown")
        return "unknown"
    
    def _switch_to_backup_account(self) -> None:
        """切换到备用账号"""
        try:
            # 备份当前账号
            current_data = load_json(str(self.cookies_file))
            save_json(str(self.config_path / "current_backup.json"), current_data)
            
            # 加载备用账号
            backup_data = load_json(str(self.backup_cookies_file))
            save_json(str(self.cookies_file), backup_data)
            
            # 更新状态
            self.account_status["last_switch_time"] = time.time()
            self.current_account = None  # 重置当前账号，下次会重新获取
            self._save_account_status()
            
            logger.info("账号切换完成")
            
        except Exception as e:
            logger.error(f"账号切换失败: {e}")
    
    def get_account_info(self) -> Dict:
        """获取账号信息"""
        current_account_id = self._get_current_account_id()
        account_info = self.account_status["accounts"].get(current_account_id, {})
        
        return {
            "account_id": current_account_id,
            "status": account_info.get("status", "unknown"),
            "last_used": account_info.get("last_used", 0),
            "block_count": account_info.get("block_count", 0),
            "success_count": account_info.get("success_count", 0),
            "last_error": account_info.get("last_error", "")
        }
    
    def reset_account_status(self, account_id: str = None) -> None:
        """重置账号状态"""
        if not account_id:
            account_id = self._get_current_account_id()
        
        if account_id in self.account_status["accounts"]:
            self.account_status["accounts"][account_id]["status"] = "active"
            self.account_status["accounts"][account_id].pop("block_time", None)
            self.account_status["accounts"][account_id].pop("last_error", None)
            self._save_account_status()
            logger.info(f"账号 {account_id} 状态已重置")
    
    def wait_for_account_recovery(self, account_id: str = None) -> float:
        """
        计算账号恢复等待时间
        
        Returns:
            float: 等待时间（秒）
        """
        if not account_id:
            account_id = self._get_current_account_id()
        
        if account_id not in self.account_status["accounts"]:
            return 0
        
        account_info = self.account_status["accounts"][account_id]
        if account_info.get("status") != "blocked":
            return 0
        
        block_time = account_info.get("block_time", 0)
        current_time = time.time()
        
        # 基础等待时间1小时，根据封禁次数递增
        block_count = account_info.get("block_count", 1)
        base_wait_time = 3600  # 1小时
        wait_time = base_wait_time * min(block_count, 4)  # 最多4小时
        
        elapsed_time = current_time - block_time
        remaining_time = max(0, wait_time - elapsed_time)
        
        return remaining_time