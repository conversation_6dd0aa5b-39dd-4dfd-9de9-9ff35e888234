.data-preview-enhanced {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
}

/* 工具栏 */
.preview-toolbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  gap: 24px;
  flex-wrap: wrap;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-count {
  font-size: 14px;
  color: #8c8c8c;
}

/* 搜索框 */
.toolbar-center {
  flex: 1;
  max-width: 400px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-box svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #8c8c8c;
}

.search-box input {
  width: 100%;
  height: 36px;
  padding: 0 36px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
}

.search-box input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-box .clear-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.search-box .clear-btn:hover {
  background: #e8e8e8;
}

/* 工具栏右侧 */
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 视图切换 */
.view-switcher {
  display: flex;
  background: #f0f0f0;
  border-radius: 8px;
  padding: 2px;
}

.view-btn {
  width: 36px;
  height: 32px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  border-radius: 6px;
  transition: all 0.2s;
}

.view-btn:hover {
  color: #262626;
}

.view-btn.active {
  background: #fff;
  color: #1890ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 批量操作 */
.batch-actions {
  display: flex;
  gap: 8px;
  animation: slideIn 0.2s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.batch-btn {
  height: 32px;
  padding: 0 16px;
  border: 1px solid #d9d9d9;
  background: #fff;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.batch-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.batch-btn.primary {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.batch-btn.primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 筛选器 */
.filter-bar {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 24px;
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  background: #fff;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.filter-toggle:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.filter-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
}

.clear-filters {
  font-size: 14px;
  color: #1890ff;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s;
}

.clear-filters:hover {
  color: #40a9ff;
}

/* 筛选内容 */
.filter-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
  animation: expand 0.3s ease;
}

@keyframes expand {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.filter-group input {
  height: 32px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
}

.filter-group input:focus {
  outline: none;
  border-color: #1890ff;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-inputs input {
  flex: 1;
}

.price-inputs span {
  color: #8c8c8c;
}

.brand-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 100px;
  overflow-y: auto;
}

.brand-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
}

.brand-item:hover {
  background: #e8e8e8;
}

.brand-item input[type="checkbox"] {
  width: 14px;
  height: 14px;
  cursor: pointer;
}

/* 内容区域 */
.preview-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

/* 响应式 */
@media (max-width: 768px) {
  .preview-toolbar-container {
    padding: 12px 16px;
  }
  
  .toolbar-center {
    order: 3;
    flex-basis: 100%;
    max-width: none;
  }
  
  .filter-content {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 12px 16px 16px;
  }
  
  .preview-content {
    padding: 12px;
  }
}