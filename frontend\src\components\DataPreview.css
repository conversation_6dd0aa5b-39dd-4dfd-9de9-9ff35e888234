.data-preview {
  animation: fadeIn 0.4s ease;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.preview-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-title svg {
  color: var(--primary);
}

.preview-count {
  font-size: 14px;
  color: var(--text-secondary);
  background: var(--secondary);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid var(--border);
}

/* Tab Navigation Styles */
.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--border);
  margin-bottom: 20px;
  gap: 4px;
  overflow-x: auto;
  scrollbar-width: thin;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
  font-family: inherit;
}

.tab-button:hover {
  background: var(--secondary);
}

.tab-button.active {
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: currentColor;
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  background: var(--surface);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: var(--secondary);
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: var(--text);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border);
  vertical-align: middle;
}

.data-row {
  transition: all 0.2s ease;
  cursor: pointer;
}

.data-row:hover {
  background: var(--secondary);
}

.data-row:last-child td {
  border-bottom: none;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  border: 1px solid var(--border);
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-name {
  font-size: 14px;
  color: var(--text);
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary);
}

.product-sales {
  font-size: 14px;
  color: var(--text);
  font-weight: 500;
}

.shop-name {
  font-size: 14px;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px !important;
  text-align: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.empty-content p {
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
  margin: 0;
}

.empty-content span {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 骨架屏 */
.skeleton-row td {
  padding: 12px 16px;
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--secondary) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    var(--secondary) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  border-radius: var(--radius-sm);
}

.skeleton-image {
  width: 60px;
  height: 60px;
}

.skeleton-text {
  height: 36px;
  width: 100%;
}

.skeleton-price {
  height: 20px;
  width: 80px;
}

.skeleton-sales {
  height: 20px;
  width: 60px;
}

.skeleton-shop {
  height: 20px;
  width: 120px;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

.modal-content {
  background: var(--surface);
  border-radius: var(--radius);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--border);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--secondary);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--border);
  color: var(--text);
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

.detail-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  margin-bottom: 20px;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-info p {
  margin: 0;
  font-size: 14px;
  color: var(--text);
}

.detail-info strong {
  color: var(--text);
  margin-right: 8px;
}

@media (max-width: 768px) {
  .data-table {
    font-size: 12px;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }
  
  .product-image {
    width: 40px;
    height: 40px;
  }
  
  .product-name {
    max-width: 200px;
  }
}