import React from 'react'
import './SearchConfig.css'

function SearchConfig({ config, setConfig, onChange, disabled }) {
  const handleChange = (field, value) => {
    // Handle onChange prop instead of setConfig
    if (typeof setConfig === 'function') {
      setConfig(prev => ({
        ...prev,
        [field]: value
      }))
    } else if (typeof onChange === 'function') {
      onChange({
        [field]: value,
        // Also set keywords when keyword changes
        ...(field === 'keyword' ? { keywords: value } : {})
      })
    }
  }

  // Calculate total products based on keywords
  const keywordCount = config.keyword ? config.keyword.split(',').filter(k => k.trim()).length : 0
  const totalProducts = keywordCount * config.targetCount

  return (
    <div className="search-config card">
      <h2 className="config-title">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
        </svg>
        搜索配置
      </h2>
      
      <div className="config-content">
        <div className="form-group">
          <label className="label" htmlFor="keyword">
            搜索关键词 <span className="required">*</span>
            <span className="keyword-count" style={{ fontSize: '12px', color: '#666', marginLeft: '8px' }}>
              {keywordCount > 0 && `(${keywordCount}个关键词)`}
            </span>
          </label>
          <div className="input-wrapper">
            <input
              id="keyword"
              type="text"
              className="input"
              placeholder="输入关键词，多个用逗号分隔，如: 手机,耳机,充电器"
              value={config.keyword}
              onChange={(e) => handleChange('keyword', e.target.value)}
              disabled={disabled}
            />
            {config.keyword && (
              <button 
                className="input-clear"
                onClick={() => handleChange('keyword', '')}
                disabled={disabled}
              >
                ✕
              </button>
            )}
          </div>
          <p className="help-text">
            支持多关键词搜索：使用英文逗号分隔多个关键词，每个关键词将独立搜索指定数量的商品
          </p>
          {keywordCount > 1 && (
            <div className="keyword-tags" style={{ marginTop: '8px', display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
              {config.keyword.split(',').filter(k => k.trim()).map((keyword, index) => (
                <span key={index} className="keyword-tag" style={{
                  background: '#e3f2fd',
                  color: '#1976d2',
                  padding: '2px 8px',
                  borderRadius: '12px',
                  fontSize: '12px'
                }}>
                  {keyword.trim()}
                </span>
              ))}
            </div>
          )}
        </div>
        
        <div className="form-row">
          <div className="form-group" style={{ display: 'none' }}>
            <label className="label" htmlFor="maxPages">
              爬取页数
            </label>
            <input
              id="maxPages"
              type="number"
              className="input"
              min="1"
              max="100"
              value={config.maxPages}
              onChange={(e) => handleChange('maxPages', parseInt(e.target.value) || 1)}
              disabled={disabled}
            />
            <p className="help-text">每页约20-40个商品</p>
          </div>
          
          <div className="form-group" style={{ width: '100%' }}>
            <label className="label" htmlFor="targetCount">
              每个关键词商品数量
            </label>
            <input
              id="targetCount"
              type="number"
              className="input"
              min="1"
              max="10000"
              value={config.targetCount}
              onChange={(e) => handleChange('targetCount', parseInt(e.target.value) || 1)}
              disabled={disabled}
            />
            <p className="help-text">
              每个关键词将采集 {config.targetCount} 个商品
              {keywordCount > 1 && `，总计 ${totalProducts} 个商品`}
            </p>
          </div>
        </div>
        
        <div className="form-group">
          <label className="label">
            爬取模式设置
          </label>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {/* 无头模式 */}
            <div className="checkbox-group" style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '12px', 
              background: '#f5f5f5', 
              borderRadius: '8px'
            }}>
              <label style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: disabled ? 'not-allowed' : 'pointer',
                opacity: disabled ? 0.6 : 1
              }}>
                <input
                  type="checkbox"
                  checked={config.headless !== false}
                  onChange={(e) => handleChange('headless', e.target.checked)}
                  disabled={disabled}
                  style={{ marginRight: '8px' }}
                />
                <span style={{ fontWeight: '500' }}>无头模式</span>
                <span style={{ 
                  marginLeft: '8px', 
                  fontSize: '12px', 
                  color: '#666',
                  fontWeight: 'normal'
                }}>
                  (更快速、更稳定，但不显示浏览器窗口)
                </span>
              </label>
            </div>
            
            {/* 商品筛选 */}
            <div className="checkbox-group" style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '12px', 
              background: '#f5f5f5', 
              borderRadius: '8px'
            }}>
              <label style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: disabled ? 'not-allowed' : 'pointer',
                opacity: disabled ? 0.6 : 1
              }}>
                <input
                  type="checkbox"
                  checked={config.enableFilter === true}
                  onChange={(e) => handleChange('enableFilter', e.target.checked)}
                  disabled={disabled}
                  style={{ marginRight: '8px' }}
                />
                <span style={{ fontWeight: '500' }}>精确商品筛选</span>
                <span style={{ 
                  marginLeft: '8px', 
                  fontSize: '12px', 
                  color: '#666',
                  fontWeight: 'normal'
                }}>
                  (仅采集与关键词高度相关的商品，过滤无关商品)
                </span>
              </label>
            </div>
          </div>
          <p className="help-text">
            无头模式：浏览器在后台运行，提高爬取效率<br/>
            精确筛选：使用智能算法过滤不相关商品，提高数据质量
          </p>
        </div>
        
        <div className="config-stats">
          <div className="stat-item">
            <span className="stat-label">关键词数量</span>
            <span className="stat-value">{keywordCount || 0} 个</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">总商品数</span>
            <span className="stat-value">{totalProducts || 0} 个</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">预计耗时</span>
            <span className="stat-value">~{Math.ceil(totalProducts / 30 * 2)} 分钟</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SearchConfig