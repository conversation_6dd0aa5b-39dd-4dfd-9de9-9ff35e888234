import React from 'react'
import './ProgressRing.css'

export default function ProgressRing({ status, progress }) {
  const percentage = progress.targetItems > 0 
    ? Math.round((progress.itemsCollected / progress.targetItems) * 100)
    : 0

  const radius = 90
  const strokeWidth = 12
  const normalizedRadius = radius - strokeWidth * 2
  const circumference = normalizedRadius * 2 * Math.PI
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  const getStatusConfig = () => {
    switch (status) {
      case 'running':
        return {
          icon: (
            <svg className="status-icon spinning" width="32" height="32" viewBox="0 0 32 32" fill="none">
              <path d="M16 4a12 12 0 0112 12h-3a9 9 0 00-9-9V4z" fill="currentColor"/>
            </svg>
          ),
          text: '正在爬取',
          color: 'var(--primary)',
          pulse: true
        }
      case 'paused':
        return {
          icon: (
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <rect x="10" y="8" width="4" height="16" rx="1" fill="currentColor"/>
              <rect x="18" y="8" width="4" height="16" rx="1" fill="currentColor"/>
            </svg>
          ),
          text: '已暂停',
          color: '#ff9800',
          pulse: false
        }
      case 'completed':
        return {
          icon: (
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <path d="M13 18.25L8.75 14l-1.5 1.5L13 21.25 25 9.25l-1.5-1.5L13 18.25z" fill="currentColor"/>
            </svg>
          ),
          text: '已完成',
          color: '#4caf50',
          pulse: false
        }
      default:
        return {
          icon: (
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="8" fill="currentColor"/>
            </svg>
          ),
          text: '等待开始',
          color: 'var(--text-secondary)',
          pulse: false
        }
    }
  }

  const statusConfig = getStatusConfig()

  return (
    <div className="progress-ring-container">
      {/* 头部 */}
      <div className="progress-header">
        <div className="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2v6m0 12v2m10-10h-6m-12 0H2m16.364 6.364l-4.243-4.243m-4.242 4.243l-4.243 4.243m12.728 0l-4.243-4.243m-4.242-4.243L5.636 5.636" 
              stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </div>
        <h2 className="progress-title">爬取进度</h2>
      </div>

      {/* 进度环 */}
      <div className="ring-wrapper">
        <div className={`progress-ring ${statusConfig.pulse ? 'pulsing' : ''}`}>
          <svg height={radius * 2} width={radius * 2}>
            {/* 背景圆环 */}
            <circle
              className="progress-ring-background"
              stroke="var(--border)"
              fill="transparent"
              strokeWidth={strokeWidth}
              r={normalizedRadius}
              cx={radius}
              cy={radius}
            />
            {/* 进度圆环 */}
            <circle
              className="progress-ring-circle"
              stroke={statusConfig.color}
              fill="transparent"
              strokeWidth={strokeWidth}
              strokeDasharray={circumference + ' ' + circumference}
              style={{ strokeDashoffset }}
              r={normalizedRadius}
              cx={radius}
              cy={radius}
            />
          </svg>

          {/* 中心内容 */}
          <div className="ring-content">
            <div className="percentage">{percentage}%</div>
            <div className="status-info" style={{ color: statusConfig.color }}>
              {statusConfig.icon}
              <span>{statusConfig.text}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <rect x="3" y="3" width="14" height="14" rx="2" stroke="currentColor" strokeWidth="1.5"/>
              <path d="M7 10l2 2 4-4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className="stat-info">
            <div className="stat-value">{progress.itemsCollected}</div>
            <div className="stat-label">已采集</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <rect x="3" y="4" width="14" height="2" rx="1" fill="currentColor"/>
              <rect x="3" y="9" width="14" height="2" rx="1" fill="currentColor"/>
              <rect x="3" y="14" width="10" height="2" rx="1" fill="currentColor"/>
            </svg>
          </div>
          <div className="stat-info">
            <div className="stat-value">{progress.currentPage}/{progress.totalPages}</div>
            <div className="stat-label">页数</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <circle cx="10" cy="10" r="7" stroke="currentColor" strokeWidth="1.5"/>
              <path d="M10 6v4l3 3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </div>
          <div className="stat-info">
            <div className="stat-value">
              {status === 'running' 
                ? `${Math.ceil((progress.totalPages - progress.currentPage) * 3)}分`
                : '--'
              }
            </div>
            <div className="stat-label">剩余时间</div>
          </div>
        </div>
      </div>

      {/* 实时指示器 */}
      {status === 'running' && (
        <div className="live-indicator">
          <span className="live-dot"></span>
          <span className="live-text">实时爬取中...</span>
        </div>
      )}
    </div>
  )
}