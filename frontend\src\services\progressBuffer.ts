interface ProgressUpdate {
  timestamp: number;
  itemsCollected: number;
  currentPage: number;
  totalPages: number;
  currentKeyword?: string;
  keywordIndex?: number;
  totalKeywords?: number;
}

interface BufferConfig {
  maxSize?: number;
  updateInterval?: number;
  smoothingFactor?: number;
}

class ProgressBuffer {
  private buffer: ProgressUpdate[] = [];
  private config: BufferConfig;
  private updateTimer: number | null = null;
  private listeners: Array<(progress: ProgressUpdate) => void> = [];
  private lastEmittedProgress: ProgressUpdate | null = null;
  private startTime: number = Date.now();

  constructor(config: BufferConfig = {}) {
    this.config = {
      maxSize: 50,
      updateInterval: 100, // ms
      smoothingFactor: 0.3,
      ...config
    };
  }

  addProgress(update: Omit<ProgressUpdate, 'timestamp'>): void {
    const progressUpdate: ProgressUpdate = {
      ...update,
      timestamp: Date.now()
    };

    this.buffer.push(progressUpdate);

    // 限制缓冲区大小
    if (this.buffer.length > this.config.maxSize!) {
      this.buffer = this.buffer.slice(-this.config.maxSize!);
    }

    // 启动更新定时器
    if (!this.updateTimer) {
      this.scheduleUpdate();
    }
  }

  private scheduleUpdate(): void {
    this.updateTimer = window.setTimeout(() => {
      this.emitProgress();
      this.updateTimer = null;

      // 如果还有待处理的更新，继续调度
      if (this.buffer.length > 0) {
        this.scheduleUpdate();
      }
    }, this.config.updateInterval);
  }

  private emitProgress(): void {
    if (this.buffer.length === 0) return;

    // 获取最新的进度
    const latestProgress = this.buffer[this.buffer.length - 1];

    // 如果有上次发送的进度，进行平滑处理
    if (this.lastEmittedProgress) {
      const smoothedProgress = this.smoothProgress(this.lastEmittedProgress, latestProgress);
      this.notifyListeners(smoothedProgress);
      this.lastEmittedProgress = smoothedProgress;
    } else {
      this.notifyListeners(latestProgress);
      this.lastEmittedProgress = latestProgress;
    }

    // 清理已处理的更新
    this.buffer = [];
  }

  private smoothProgress(prev: ProgressUpdate, current: ProgressUpdate): ProgressUpdate {
    const factor = this.config.smoothingFactor!;
    
    return {
      ...current,
      itemsCollected: Math.round(
        prev.itemsCollected * (1 - factor) + current.itemsCollected * factor
      ),
      currentPage: current.currentPage, // 页数不需要平滑
      totalPages: current.totalPages
    };
  }

  private notifyListeners(progress: ProgressUpdate): void {
    this.listeners.forEach(listener => {
      try {
        listener(progress);
      } catch (error) {
        console.error('Progress listener error:', error);
      }
    });
  }

  subscribe(listener: (progress: ProgressUpdate) => void): () => void {
    this.listeners.push(listener);

    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  getStatistics(): {
    totalUpdates: number;
    updatesPerSecond: number;
    averageItemsPerUpdate: number;
    estimatedTimeRemaining: number | null;
  } {
    const allUpdates = [...this.buffer];
    if (this.lastEmittedProgress) {
      allUpdates.push(this.lastEmittedProgress);
    }

    const totalUpdates = allUpdates.length;
    const elapsedTime = (Date.now() - this.startTime) / 1000; // seconds
    const updatesPerSecond = totalUpdates / elapsedTime;

    let averageItemsPerUpdate = 0;
    let estimatedTimeRemaining = null;

    if (allUpdates.length > 1) {
      // 计算平均每次更新的商品数
      const itemsIncrement = allUpdates.reduce((sum, update, index) => {
        if (index === 0) return 0;
        const increment = update.itemsCollected - allUpdates[index - 1].itemsCollected;
        return sum + Math.max(0, increment);
      }, 0);

      averageItemsPerUpdate = itemsIncrement / (allUpdates.length - 1);

      // 估算剩余时间
      if (this.lastEmittedProgress && averageItemsPerUpdate > 0) {
        const remainingPages = this.lastEmittedProgress.totalPages - this.lastEmittedProgress.currentPage;
        const timePerPage = elapsedTime / this.lastEmittedProgress.currentPage;
        estimatedTimeRemaining = remainingPages * timePerPage;
      }
    }

    return {
      totalUpdates,
      updatesPerSecond,
      averageItemsPerUpdate,
      estimatedTimeRemaining
    };
  }

  reset(): void {
    this.buffer = [];
    this.lastEmittedProgress = null;
    this.startTime = Date.now();
    
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
  }

  destroy(): void {
    this.reset();
    this.listeners = [];
  }
}

export default ProgressBuffer;
export type { ProgressUpdate, BufferConfig };