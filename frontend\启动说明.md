# 拼多多爬虫控制面板 - 启动说明

## 快速启动步骤

### 1. 进入前端目录
```bash
cd /mnt/c/Users/<USER>/Desktop/pdd2/frontend
```

### 2. 安装项目依赖
```bash
npm install
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 访问页面
启动成功后，在浏览器中访问：
- http://localhost:3000

## 可能遇到的问题

### 如果没有 Node.js
1. 先安装 Node.js（推荐 v16 或更高版本）
   - Windows: 访问 https://nodejs.org/ 下载安装包
   - 或使用 nvm 管理版本

### 如果端口被占用
修改 `vite.config.js` 中的端口：
```javascript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3001,  // 改为其他端口
    open: true
  }
})
```

### 如果依赖安装失败
尝试清理缓存后重新安装：
```bash
npm cache clean --force
npm install
```

## 项目结构说明

```
frontend/
├── index.html          # 入口 HTML 文件
├── package.json        # 项目配置和依赖
├── vite.config.js      # Vite 构建配置
├── src/
│   ├── main.jsx        # React 应用入口
│   ├── App.jsx         # 主应用组件
│   ├── index.css       # 全局样式
│   └── components/     # 所有组件
└── node_modules/       # 依赖包（npm install 后生成）
```

## 功能测试

启动后可以测试以下功能：
1. 输入搜索关键词（如：手机、冰箱）
2. 设置爬取页数和目标数量
3. 选择排序方式
4. 点击"开始爬取"查看模拟效果
5. 切换深色/浅色主题

## 生产环境构建

如果需要构建生产版本：
```bash
npm run build
```

构建后的文件在 `dist` 目录，可以部署到任何静态服务器。

## 预览生产版本
```bash
npm run preview
```

这会在本地启动一个服务器预览构建后的版本。