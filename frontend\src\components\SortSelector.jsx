import React from 'react'
import './SortSelector.css'

function SortSelector({ value, onChange, disabled }) {
  const sortOptions = [
    { value: 'comprehensive', label: '综合排序', icon: '🏆' },
    { value: 'sales_desc', label: '销量从高到低', icon: '📈' },
    { value: 'price_asc', label: '价格从低到高', icon: '💰' },
    { value: 'price_desc', label: '价格从高到低', icon: '💎' }
  ]

  return (
    <div className="sort-selector card">
      <h2 className="sort-title">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M3 3a1 1 0 000 2h11a1 1 0 100-2H3zM3 7a1 1 0 000 2h7a1 1 0 100-2H3zM3 11a1 1 0 000 2h4a1 1 0 100-2H3zM15 8a1 1 0 10-2 0v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L15 13.586V8z"/>
        </svg>
        排序方式
      </h2>
      
      <div className="sort-options">
        {sortOptions.map((option) => (
          <label 
            key={option.value} 
            className={`sort-option ${value === option.value ? 'active' : ''}`}
          >
            <input
              type="radio"
              name="sort"
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange(e.target.value)}
              disabled={disabled}
            />
            <span className="sort-option-content">
              <span className="sort-icon">{option.icon}</span>
              <span className="sort-label">{option.label}</span>
            </span>
            {value === option.value && (
              <svg className="check-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path fillRule="evenodd" d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
              </svg>
            )}
          </label>
        ))}
      </div>
      
      <div className="sort-info">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path fillRule="evenodd" d="M8 1.5a6.5 6.5 0 100 13 6.5 6.5 0 000-13zM0 8a8 8 0 1116 0A8 8 0 010 8zm8-4a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 018 4zm0 8a1 1 0 100-2 1 1 0 000 2z"/>
        </svg>
        <span>排序方式会影响爬取的商品顺序和结果</span>
      </div>
    </div>
  )
}

export default SortSelector