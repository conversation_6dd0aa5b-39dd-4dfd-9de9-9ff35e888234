import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: '0.0.0.0',
    open: false  // 由启动脚本控制打开浏览器
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        cookieDemo: resolve(__dirname, 'cookie-demo.html')
      },
      output: {
        // 代码分割
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['./src/utils/lazyLoad.js', './src/hooks/useKeyboardShortcuts.js']
        }
      }
    },
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 大文件警告阈值
    chunkSizeWarningLimit: 1000
  }
})