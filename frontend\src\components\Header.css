.header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  transition: all var(--duration-300) var(--ease-out);
  backdrop-filter: blur(10px);
  background-color: rgba(var(--bg-card-rgb), 0.8);
}

/* 深色模式下的半透明背景 */
[data-theme="light"] .header {
  --bg-card-rgb: 255, 255, 255;
}

[data-theme="dark"] .header {
  --bg-card-rgb: 31, 41, 55;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-5);
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn var(--duration-500) var(--ease-out);
  transition: transform var(--duration-300) var(--ease-spring);
}

.logo:hover {
  transform: scale(1.05);
}

.logo svg {
  filter: drop-shadow(var(--shadow-sm));
  transition: filter var(--duration-300) var(--ease-out);
}

.logo:hover svg {
  filter: drop-shadow(var(--shadow-md));
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.025em;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-5);
}

/* 主题切换按钮样式已移至 ThemeToggle 组件 */

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  background: rgba(34, 197, 94, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid rgba(34, 197, 94, 0.3);
  transition: all var(--duration-300) var(--ease-out);
}

.status-indicator:hover {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.4);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--state-success);
  animation: pulseScale 2s var(--ease-in-out) infinite;
  box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
}

.status-text {
  font-size: var(--font-size-sm);
  color: var(--state-success);
  font-weight: var(--font-weight-medium);
}

/* 深色模式调整 */
[data-theme="dark"] .status-indicator {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.4);
}

[data-theme="dark"] .status-dot {
  box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.6);
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--spacing-4);
  }
  
  .header-title {
    font-size: var(--font-size-base);
  }
  
  .status-text {
    display: none;
  }
  
  .status-indicator {
    padding: var(--spacing-2);
  }
  
  .header-actions {
    gap: var(--spacing-3);
  }
}