/* Toast 通知样式 */
.toast-container {
  position: fixed;
  top: var(--spacing-5);
  right: var(--spacing-5);
  z-index: var(--z-index-notification);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  pointer-events: none;
}

.toast {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
  min-width: 300px;
  max-width: 500px;
  pointer-events: auto;
  transition: all var(--duration-300) var(--ease-out);
}

/* 类型样式 */
.toast-success {
  border-color: var(--color-success-300);
  background-color: var(--color-success-50);
}

.toast-error {
  border-color: var(--color-error-300);
  background-color: var(--color-error-50);
}

.toast-warning {
  border-color: var(--color-warning-300);
  background-color: var(--color-warning-50);
}

.toast-info {
  border-color: var(--color-info-300);
  background-color: var(--color-info-50);
}

/* 深色模式调整 */
[data-theme="dark"] .toast-success {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .toast-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="dark"] .toast-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .toast-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* 图标样式 */
.toast-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.toast-success .toast-icon {
  color: var(--color-success-600);
}

.toast-error .toast-icon {
  color: var(--color-error-600);
}

.toast-warning .toast-icon {
  color: var(--color-warning-600);
}

.toast-info .toast-icon {
  color: var(--color-info-600);
}

/* 消息样式 */
.toast-message {
  flex: 1;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 关闭按钮 */
.toast-close {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: var(--radius-base);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  padding: 0;
}

.toast-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

[data-theme="dark"] .toast-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 动画 */
.toast-enter {
  animation: toastSlideIn var(--duration-300) var(--ease-spring);
}

.toast-exit {
  animation: toastSlideOut var(--duration-300) var(--ease-in);
  opacity: 0;
  transform: translateX(100%);
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes toastSlideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* 响应式 */
@media (max-width: 640px) {
  .toast-container {
    top: var(--spacing-3);
    right: var(--spacing-3);
    left: var(--spacing-3);
  }
  
  .toast {
    min-width: auto;
    width: 100%;
  }
}