# Cookie管理组件使用说明

## 概述

Cookie管理组件提供了一套完整的Cookie输入、验证、显示和管理功能，专门为拼多多爬虫系统设计。

## 组件结构

- **CookieManager**: 主组件，整合所有Cookie管理功能
- **CookieInput**: Cookie输入组件，支持多种格式
- **CookieDisplay**: Cookie显示组件，带脱敏功能
- **CookieValidator**: Cookie验证组件

## 功能特性

### 1. 多格式支持
- **字符串格式**: `name1=value1; name2=value2`
- **JSON格式**: 完整的Cookie对象数组
- **浏览器格式**: 从浏览器开发者工具复制的格式
- **Header格式**: HTTP请求头中的Cookie格式

### 2. 安全特性
- Cookie值默认脱敏显示
- 点击眼睛图标查看完整值
- 敏感信息不会在日志中暴露

### 3. 验证功能
- 自动格式检测
- Cookie名称和值的合法性验证
- 重复Cookie检测
- 过期时间检查
- 必需Cookie提醒（PDDAccessToken、api_uid）

### 4. 便捷操作
- 一键粘贴剪贴板内容
- 格式化功能
- 单个或批量复制
- 示例展示

## 使用方法

```tsx
import { CookieManager } from '@/components/CookieManager';

function App() {
  const handleCookieUpdate = (cookies) => {
    console.log('Cookie更新:', cookies);
  };

  const handleCookieSave = async (cookies) => {
    // 调用API保存Cookie
    await api.saveCookies(cookies);
  };

  return (
    <CookieManager
      onCookieUpdate={handleCookieUpdate}
      onCookieSave={handleCookieSave}
      initialCookies={[]}
    />
  );
}
```

## 开发运行

1. 安装依赖：
```bash
npm install
```

2. 运行演示：
```bash
npm run dev
```

3. 访问演示页面：
- 主页面: http://localhost:5173
- Cookie管理演示: http://localhost:5173/cookie-demo.html

## 注意事项

1. Cookie验证会自动进行，输入后1秒触发
2. 过期的Cookie会显示红色标签
3. 即将过期（24小时内）的Cookie会显示黄色警告
4. 建议定期更新Cookie以确保爬虫正常工作