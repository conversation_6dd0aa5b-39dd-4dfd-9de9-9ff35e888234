---
title: 项目结构与模块职责
description: 详细的项目目录结构、模块职责划分和文件命名规范
inclusion: always
---

# 项目结构与模块职责

## 目录结构概览

```
pdd2/                           # 项目根目录
├── .ai-rules/                  # AI助手指导文档
│   ├── product.md             # 产品愿景与功能
│   ├── tech.md                # 技术栈与架构
│   └── structure.md           # 项目结构（本文档）
├── .claude/                    # Claude AI配置
│   ├── agents/                # AI代理配置
│   └── settings.local.json    # 本地设置
├── .github/                    # GitHub配置
│   └── workflows/             # CI/CD工作流
├── .serena/                    # Serena工具配置
│   └── memories/              # 项目记忆文档
├── backend/                    # 后端API服务（可选）
│   └── api_server.py          # FastAPI服务器
├── browser_data/              # 浏览器数据存储
│   ├── cookies/               # Cookie持久化
│   └── pdd_user_data_dir/     # CDP用户数据
├── config/                     # 配置文件目录
│   ├── settings.yaml          # 主配置文件
│   ├── cookies.json           # Cookie配置
│   └── user_agents.json       # User-Agent列表
├── docs/                       # 项目文档
│   └── quick-troubleshooting-guide.md
├── frontend/                   # Web前端界面
│   ├── public/                # 静态资源
│   ├── src/                   # React源代码
│   │   ├── components/        # React组件
│   │   ├── utils/            # 前端工具函数
│   │   └── App.jsx           # 主应用组件
│   ├── package.json          # Node依赖配置
│   └── vite.config.js        # Vite构建配置
├── libs/                       # 第三方库（如需要）
├── logs/                       # 日志文件目录
│   └── pdd_crawler_*.log      # 运行日志
├── output/                     # 数据输出目录
│   └── *.xlsx                 # Excel数据文件
├── specs/                      # 规范文档
├── src/                        # 爬虫核心源代码
│   ├── core/                  # 核心功能模块
│   ├── data/                  # 数据处理模块
│   ├── utils/                 # 工具函数模块
│   ├── main.py               # 主程序入口
│   └── __init__.py           # 包初始化
├── tests/                      # 测试代码
├── .gitignore                 # Git忽略配置
├── CHANGELOG.md               # 更新日志
├── README.md                  # 项目说明
├── requirements.txt           # Python依赖
└── run_main.py               # 启动脚本
```

## 核心模块详解

### /src/core/ - 核心功能模块

#### browser_manager.py
- **职责**：浏览器实例管理和生命周期控制
- **主要类**：`BrowserManager`
- **核心功能**：
  - 浏览器启动参数配置
  - Playwright实例管理
  - 页面上下文创建
  - 资源清理

#### api_response_monitor.py
- **职责**：API响应拦截和数据提取
- **主要类**：`APIResponseMonitor`
- **核心功能**：
  - 监听网络请求
  - 拦截目标API响应
  - JSON数据解析（支持orjson）
  - 数据回调处理

#### anti_detection_simple.py
- **职责**：反检测和风控处理
- **主要类**：`SimpleAntiDetectionManager`
- **核心功能**：
  - 风控状态检测
  - 自动恢复策略
  - 延迟控制
  - 身份切换

#### scroll_manager.py
- **职责**：智能页面滚动控制
- **主要类**：`SmartScrollManager`
- **核心功能**：
  - 自适应滚动速度
  - 滚动距离随机化
  - 页面底部检测
  - API触发监控

#### login_detector.py
- **职责**：登录状态检测
- **主要类**：`LoginDetector`
- **核心功能**：
  - 登录指示器检测
  - Cookie有效性验证
  - 登录状态监控

#### cookie_manager.py
- **职责**：Cookie管理和持久化
- **主要类**：`CookieManager`
- **核心功能**：
  - Cookie导入/导出
  - Cookie验证
  - Cookie更新

#### user_agent_manager.py
- **职责**：User-Agent管理
- **主要类**：`UserAgentManager`
- **核心功能**：
  - UA池管理
  - 随机UA生成
  - 设备类型模拟

#### lightweight_fingerprint.py
- **职责**：轻量级浏览器指纹
- **主要类**：`LightweightFingerprintManager`
- **核心功能**：
  - Canvas指纹随机化
  - WebGL参数调整
  - 字体指纹处理

#### stealth_manager.py
- **职责**：Stealth模式管理
- **主要类**：`StealthManager`
- **核心功能**：
  - playwright-stealth集成
  - 自动化特征隐藏

#### sort_manager.py
- **职责**：排序功能管理
- **主要类**：`SortManager`
- **核心功能**：
  - 多排序方式支持
  - 排序切换逻辑

#### cdp_browser_manager.py
- **职责**：CDP模式浏览器管理
- **主要类**：`CDPBrowserManager`
- **核心功能**：
  - 真实Chrome控制
  - 调试端口连接

### /src/data/ - 数据处理模块

#### processor.py
- **职责**：数据处理和转换
- **主要类**：`DataProcessor`
- **核心功能**：
  - 数据清洗和验证
  - 字段映射转换
  - 去重处理
  - 品牌识别（rapidfuzz）

#### exporter.py
- **职责**：数据导出功能
- **主要类**：`ExcelExporter`
- **核心功能**：
  - Excel文件生成
  - 多工作表支持
  - 数据格式化
  - 统计信息生成

### /src/utils/ - 工具函数模块

#### logger.py
- **职责**：日志管理
- **主要类**：`LoggerManager`
- **核心功能**：
  - Loguru配置
  - 日志轮转
  - 多级别日志
  - 性能日志

#### helpers.py
- **职责**：通用工具函数
- **核心函数**：
  - `load_config()` - 加载配置
  - `ensure_dir()` - 确保目录存在
  - `format_price()` - 价格格式化
  - `sanitize_filename()` - 文件名清理

#### retry.py
- **职责**：重试机制
- **主要类**：`RetryManager`
- **核心功能**：
  - Tenacity配置
  - 指数退避
  - 异常分类

### /src/main.py - 主程序

#### PDDCrawler类
- **职责**：爬虫主控制器
- **核心方法**：
  - `run()` - 主运行循环
  - `search_keyword()` - 关键词搜索
  - `start_data_collection()` - 数据收集
  - `export_data()` - 数据导出

## 配置文件说明

### /config/settings.yaml
主配置文件，包含：
- 浏览器配置
- 搜索参数
- 滚动策略
- 反检测设置
- 导出配置

### /config/cookies.json
Cookie存储文件：
```json
{
  "pdd_cookies": [
    {
      "name": "cookie_name",
      "value": "cookie_value",
      "domain": ".yangkeduo.com"
    }
  ]
}
```

### /config/user_agents.json
User-Agent列表：
```json
{
  "desktop": ["UA strings..."],
  "mobile": ["UA strings..."]
}
```

## 前端结构（/frontend/）

### 技术栈
- React 18.2
- Ant Design 5.12
- Vite构建工具
- TypeScript支持

### 主要组件
- 爬虫控制面板
- 实时数据展示
- Cookie管理界面
- 任务状态监控

## 命名规范

### 文件命名
- **Python文件**：snake_case.py
- **配置文件**：kebab-case.yaml/json
- **日志文件**：app_name_YYYY-MM-DD.log
- **数据文件**：描述性名称_时间戳.xlsx

### 类和函数命名
- **类名**：PascalCase（如 `BrowserManager`）
- **函数名**：snake_case（如 `load_config`）
- **常量**：UPPER_CASE（如 `MAX_RETRIES`）
- **私有方法**：_leading_underscore

### 变量命名
- **实例变量**：self.snake_case
- **局部变量**：snake_case
- **布尔变量**：is_/has_/can_ 前缀

## 数据流向

```
用户配置(settings.yaml) 
    ↓
主程序(main.py)
    ↓
浏览器管理(browser_manager.py)
    ↓
页面导航 → API监听(api_response_monitor.py)
    ↓          ↓
滚动控制 ← 数据收集
    ↓
数据处理(processor.py)
    ↓
Excel导出(exporter.py)
    ↓
输出文件(output/*.xlsx)
```

## 扩展指南

### 添加新的数据字段
1. 修改 `api_response_monitor.py` 的解析逻辑
2. 更新 `processor.py` 的字段映射
3. 调整 `settings.yaml` 的column_mapping
4. 更新 `exporter.py` 的导出逻辑

### 添加新的排序方式
1. 在 `settings.yaml` 添加排序配置
2. 更新 `sort_manager.py` 的选择器
3. 修改 `main.py` 的排序处理逻辑

### 集成新的反检测策略
1. 在 `anti_detection_simple.py` 添加检测逻辑
2. 更新 `browser_manager.py` 的启动参数
3. 调整 `settings.yaml` 的相关配置