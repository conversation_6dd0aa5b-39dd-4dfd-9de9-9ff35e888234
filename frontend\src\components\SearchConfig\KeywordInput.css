.keyword-input-container {
  margin-bottom: 24px;
}

.keyword-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
  margin-bottom: 8px;
}

.required-mark {
  color: var(--primary);
  font-weight: 600;
}

.keyword-input-wrapper {
  position: relative;
  min-height: 48px;
  background: var(--secondary);
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: text;
  transition: all 0.2s ease;
}

.keyword-input-wrapper:hover {
  border-color: rgba(238, 77, 45, 0.2);
}

.keyword-input-wrapper.focused {
  border-color: var(--primary);
  background: var(--surface);
  box-shadow: 0 0 0 4px rgba(238, 77, 45, 0.1);
}

.input-icon {
  flex-shrink: 0;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.keyword-input-wrapper.focused .input-icon {
  color: var(--primary);
}

.keyword-tags-area {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.keyword-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  background: var(--primary);
  color: white;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  animation: tagFadeIn 0.2s ease;
}

@keyframes tagFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.tag-text {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tag-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  padding: 0;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
}

.tag-remove:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.tag-remove:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.keyword-input {
  flex: 1;
  min-width: 120px;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  color: var(--text);
  padding: 4px 0;
}

.keyword-input::placeholder {
  color: var(--text-secondary);
}

.clear-all-button {
  flex-shrink: 0;
  padding: 6px 12px;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 13px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-button:hover:not(:disabled) {
  background: var(--border);
  color: var(--text);
}

.clear-all-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.keyword-hints {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hint-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.keyword-count {
  font-size: 12px;
  color: var(--primary);
  font-weight: 500;
  margin: 0;
}

/* 暗色主题适配 */
[data-theme="dark"] .keyword-input-wrapper {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .keyword-input-wrapper.focused {
  background: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .clear-all-button {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .clear-all-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .keyword-tags-area {
    gap: 4px;
  }
  
  .keyword-tag {
    font-size: 12px;
    padding: 3px 8px;
  }
  
  .tag-text {
    max-width: 100px;
  }
}