import React, { useState, useRef } from 'react'
import './KeywordInput.css'

export default function KeywordInput({ keywords, onChange, disabled }) {
  const [inputValue, setInputValue] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef(null)

  // 将关键词字符串转换为标签数组
  const keywordTags = keywords
    .split(',')
    .map(k => k.trim())
    .filter(k => k.length > 0)

  // 添加关键词
  const addKeyword = (keyword) => {
    const trimmedKeyword = keyword.trim()
    if (trimmedKeyword && !keywordTags.includes(trimmedKeyword)) {
      const newKeywords = [...keywordTags, trimmedKeyword].join(', ')
      onChange(newKeywords)
    }
  }

  // 删除关键词
  const removeKeyword = (index) => {
    const newTags = keywordTags.filter((_, i) => i !== index)
    onChange(newTags.join(', '))
  }

  // 处理输入框回车事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      if (inputValue.trim()) {
        addKeyword(inputValue)
        setInputValue('')
      }
    } else if (e.key === 'Backspace' && !inputValue && keywordTags.length > 0) {
      // 如果输入框为空且按下退格键，删除最后一个标签
      removeKeyword(keywordTags.length - 1)
    }
  }

  // 处理粘贴事件
  const handlePaste = (e) => {
    e.preventDefault()
    const pastedText = e.clipboardData.getData('text')
    const pastedKeywords = pastedText.split(/[,，\s]+/).filter(k => k.trim())
    
    pastedKeywords.forEach(keyword => {
      if (keyword && !keywordTags.includes(keyword)) {
        addKeyword(keyword)
      }
    })
  }

  // 处理输入框失焦
  const handleBlur = () => {
    setIsFocused(false)
    if (inputValue.trim()) {
      addKeyword(inputValue)
      setInputValue('')
    }
  }

  return (
    <div className="keyword-input-container">
      <label className="keyword-label">
        搜索关键词
        <span className="required-mark">*</span>
      </label>
      
      <div 
        className={`keyword-input-wrapper ${isFocused ? 'focused' : ''} ${keywordTags.length > 0 ? 'has-tags' : ''}`}
        onClick={() => inputRef.current?.focus()}
      >
        <div className="input-icon">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M9 17A8 8 0 109 1a8 8 0 000 16z" stroke="currentColor" strokeWidth="1.5"/>
            <path d="M19 19L14.65 14.65" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          </svg>
        </div>

        <div className="keyword-tags-area">
          {keywordTags.map((tag, index) => (
            <span key={index} className="keyword-tag">
              <span className="tag-text">{tag}</span>
              <button
                className="tag-remove"
                onClick={(e) => {
                  e.stopPropagation()
                  removeKeyword(index)
                }}
                disabled={disabled}
                type="button"
              >
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M9 3L3 9M3 3L9 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
              </button>
            </span>
          ))}
          
          <input
            ref={inputRef}
            type="text"
            className="keyword-input"
            placeholder={keywordTags.length === 0 ? "输入关键词，按回车或逗号分隔" : "继续添加"}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            onFocus={() => setIsFocused(true)}
            onBlur={handleBlur}
            disabled={disabled}
          />
        </div>

        {keywordTags.length > 0 && (
          <button
            className="clear-all-button"
            onClick={(e) => {
              e.stopPropagation()
              onChange('')
              setInputValue('')
            }}
            disabled={disabled}
            type="button"
          >
            清空
          </button>
        )}
      </div>

      <div className="keyword-hints">
        <p className="hint-text">
          支持多个关键词搜索，用逗号或回车分隔。例如：手机, 笔记本电脑, 平板
        </p>
        {keywordTags.length > 0 && (
          <p className="keyword-count">
            已添加 {keywordTags.length} 个关键词
          </p>
        )}
      </div>
    </div>
  )
}