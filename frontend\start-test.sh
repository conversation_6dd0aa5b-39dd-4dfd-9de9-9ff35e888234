#!/bin/bash

echo "========================================"
echo "拼多多爬虫前端测试启动脚本"
echo "========================================"
echo

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "[错误] 未检测到 Node.js，请先安装 Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "[信息] Node.js 版本:"
node --version
echo

# 检查是否在frontend目录
if [ ! -f "package.json" ]; then
    echo "[错误] 请在frontend目录下运行此脚本"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "[信息] 正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "[错误] 依赖安装失败"
        exit 1
    fi
fi

echo "[信息] 正在启动前端开发服务器..."
echo
echo "========================================"
echo "访问地址: http://localhost:5173"
echo "快捷键帮助: 按 ? 键"
echo "停止服务: 按 Ctrl+C"
echo "========================================"
echo

# 启动开发服务器
npm run dev