/* 图片预览遮罩 */
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 2000;
  animation: fadeIn 0.2s ease;
}

/* 预览容器 */
.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 工具栏 */
.preview-toolbar {
  position: relative;
  height: 60px;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  z-index: 10;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.image-counter {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.zoom-level {
  color: #fff;
  font-size: 14px;
  min-width: 50px;
  text-align: center;
}

.toolbar-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn.close-btn:hover {
  background: rgba(255, 77, 79, 0.8);
}

/* 图片显示区域 */
.preview-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  user-select: none;
}

.preview-image-container img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  transition: transform 0.2s ease;
  transform-origin: center;
}

/* 导航按钮 */
.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  z-index: 10;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
}

.nav-btn.prev {
  left: 24px;
}

.nav-btn.next {
  right: 24px;
}

.nav-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-btn.disabled:hover {
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
}

/* 缩略图列表 */
.preview-thumbnails {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 24px;
  overflow-x: auto;
  z-index: 10;
}

.preview-thumbnail {
  width: 64px;
  height: 64px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
  opacity: 0.6;
}

.preview-thumbnail:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.preview-thumbnail.active {
  border-color: #fff;
  opacity: 1;
}

.preview-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 滚动条样式 */
.preview-thumbnails::-webkit-scrollbar {
  height: 6px;
}

.preview-thumbnails::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.preview-thumbnails::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.preview-thumbnails::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .preview-toolbar {
    padding: 0 16px;
  }
  
  .toolbar-left {
    display: none;
  }
  
  .nav-btn {
    width: 40px;
    height: 40px;
  }
  
  .nav-btn.prev {
    left: 16px;
  }
  
  .nav-btn.next {
    right: 16px;
  }
  
  .preview-thumbnails {
    height: 80px;
    padding: 0 16px;
  }
  
  .preview-thumbnail {
    width: 56px;
    height: 56px;
  }
}