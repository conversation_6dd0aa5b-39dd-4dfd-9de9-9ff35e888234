"""
轻量级设备指纹管理器
专注于最小化对页面功能的影响，同时保持基本的反检测能力
"""

import random
import json
import hashlib
import time
from typing import Dict, List, Optional, Any
from playwright.async_api import Page, BrowserContext
from loguru import logger

from src.utils.helpers import load_config


class LightweightFingerprintManager:
    """轻量级设备指纹管理器"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化轻量级指纹管理器"""
        self.config = load_config(config_path)
        self.fingerprint_config = self.config.get("lightweight_fingerprint", {})
        
        # 当前指纹
        self.current_fingerprint = None
        
        logger.info("轻量级指纹管理器初始化完成")
    
    def generate_lightweight_fingerprint(self, user_agent: str) -> Dict[str, Any]:
        """
        生成轻量级设备指纹
        
        Args:
            user_agent: 用户代理字符串
            
        Returns:
            Dict[str, Any]: 轻量级设备指纹信息
        """
        # 解析User-Agent获取基础信息
        device_info = self._parse_user_agent(user_agent)
        
        fingerprint = {
            "user_agent": user_agent,
            "platform": device_info["platform"],
            "device_type": device_info["device_type"],
            "browser": device_info["browser"],
            
            # 基础屏幕信息（不重写screen对象）
            "screen_info": self._get_basic_screen_info(device_info),
            
            # 基础硬件信息（最小化重写）
            "hardware_info": self._get_basic_hardware_info(device_info),
            
            # 语言设置
            "locale_info": self._get_basic_locale_info(),
            
            # 生成时间
            "generated_at": int(time.time())
        }
        
        # 生成指纹ID
        fingerprint["fingerprint_id"] = self._generate_fingerprint_id(fingerprint)
        
        self.current_fingerprint = fingerprint
        logger.info(f"生成轻量级指纹: {fingerprint['fingerprint_id'][:8]}...")
        
        return fingerprint
    
    def _parse_user_agent(self, user_agent: str) -> Dict[str, str]:
        """解析User-Agent获取设备信息"""
        device_info = {
            "platform": "Win32",
            "browser": "Chrome",
            "device_type": "desktop"
        }
        
        # 简化的UA解析
        if "Windows NT 10.0" in user_agent:
            device_info["platform"] = "Win32"
        elif "Macintosh" in user_agent:
            device_info["platform"] = "MacIntel"
        elif "Linux" in user_agent:
            device_info["platform"] = "Linux x86_64"
        elif "iPhone" in user_agent:
            device_info["platform"] = "iPhone"
            device_info["device_type"] = "mobile"
        elif "Android" in user_agent:
            device_info["platform"] = "Linux armv7l"
            device_info["device_type"] = "mobile"
        
        if "Chrome/" in user_agent:
            device_info["browser"] = "Chrome"
        elif "Firefox/" in user_agent:
            device_info["browser"] = "Firefox"
        elif "Safari/" in user_agent and "Chrome" not in user_agent:
            device_info["browser"] = "Safari"
        
        return device_info
    
    def _get_basic_screen_info(self, device_info: Dict) -> Dict[str, int]:
        """获取基础屏幕信息"""
        if device_info["device_type"] == "mobile":
            screens = [
                {"width": 375, "height": 812},
                {"width": 414, "height": 896},
                {"width": 390, "height": 844},
            ]
        else:
            screens = [
                {"width": 1920, "height": 1080},
                {"width": 1366, "height": 768},
                {"width": 1440, "height": 900},
            ]
        
        return random.choice(screens)
    
    def _get_basic_hardware_info(self, device_info: Dict) -> Dict[str, Any]:
        """获取基础硬件信息"""
        if device_info["device_type"] == "mobile":
            return {
                "cpu_cores": random.choice([4, 6, 8]),
                "memory": random.choice([4, 6, 8]),
                "max_touch_points": random.randint(5, 10)
            }
        else:
            return {
                "cpu_cores": random.choice([4, 6, 8, 12]),
                "memory": random.choice([8, 16, 32]),
                "max_touch_points": 0
            }
    
    def _get_basic_locale_info(self) -> Dict[str, Any]:
        """获取基础语言信息"""
        locales = [
            {"language": "zh-CN", "languages": ["zh-CN", "zh", "en"]},
            {"language": "zh-TW", "languages": ["zh-TW", "zh", "en"]},
            {"language": "en-US", "languages": ["en-US", "en"]},
        ]
        
        return random.choice(locales)
    
    def _generate_fingerprint_id(self, fingerprint: Dict) -> str:
        """生成指纹ID"""
        key_info = f"{fingerprint['user_agent']}"
        key_info += f"{fingerprint['screen_info']['width']}x{fingerprint['screen_info']['height']}"
        key_info += f"{fingerprint['hardware_info']['cpu_cores']}"
        
        return hashlib.sha256(key_info.encode()).hexdigest()[:16]
    
    async def apply_lightweight_fingerprint(self, page: Page, fingerprint: Dict) -> None:
        """应用轻量级指纹到页面"""
        try:
            # 使用标准强度的反检测脚本
            script = self._generate_optimized_script(fingerprint)
            await page.add_init_script(script)
            
            logger.debug(f"已应用轻量级指纹: {fingerprint['fingerprint_id'][:8]}...")
            
        except Exception as e:
            logger.error(f"应用轻量级指纹失败: {e}")
    
    def _generate_optimized_script(self, fingerprint: Dict) -> str:
        """生成最小化的反检测脚本（仅保留必要的webdriver隐藏）"""
        script = f"""
        // 最小化反检测脚本 - 仅保留必要功能
        (function() {{
            'use strict';
            
            try {{
                // 1. 隐藏 webdriver 属性（最基础的反检测）
                if (navigator.webdriver) {{
                    Object.defineProperty(navigator, 'webdriver', {{
                        get: () => undefined,
                        configurable: true
                    }});
                }}
                
                // 2. 移除自动化标识
                const cdcProps = [
                    'cdc_adoQpoasnfa76pfcZLmcfl_Array',
                    'cdc_adoQpoasnfa76pfcZLmcfl_Promise',
                    'cdc_adoQpoasnfa76pfcZLmcfl_Symbol'
                ];
                
                cdcProps.forEach(prop => {{
                    if (window[prop]) {{
                        delete window[prop];
                    }}
                }});
                
                // 3. 基础的navigator属性设置（不过度修改）
                if (navigator.hardwareConcurrency === undefined) {{
                    Object.defineProperty(navigator, 'hardwareConcurrency', {{
                        get: () => {fingerprint["hardware_info"]["cpu_cores"]},
                        configurable: true
                    }});
                }}
                
                // 4. 设置语言（使用原生方式）
                if (!navigator.languages || navigator.languages.length === 0) {{
                    Object.defineProperty(navigator, 'languages', {{
                        get: () => {json.dumps(fingerprint["locale_info"]["languages"])},
                        configurable: true
                    }});
                }}
                
                // 注意：不再重写 fetch、XMLHttpRequest、WebSocket、console 等核心API
                // 让浏览器保持原生状态，避免被检测
                
            }} catch (err) {{
                // 静默处理错误，避免暴露
            }}
        }})();
        """
        
        return script
    

    
    def get_current_fingerprint(self) -> Optional[Dict]:
        """获取当前指纹"""
        return self.current_fingerprint