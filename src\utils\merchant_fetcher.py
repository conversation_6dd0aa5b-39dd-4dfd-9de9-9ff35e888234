"""
商家信息获取工具 - 使用Playwright获取准确的商家类型信息
"""

import asyncio
import json
import time
from typing import Dict, List, Optional
from loguru import logger
from playwright.async_api import async_playwright, <PERSON><PERSON>er, Page

class MerchantInfoFetcher:
    """商家信息获取器"""
    
    def __init__(self, cookies: str):
        """
        初始化商家信息获取器
        
        Args:
            cookies: 拼多多cookie字符串
        """
        self.cookies = self._parse_cookies(cookies)
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        
    def _parse_cookies(self, cookie_string: str) -> List[Dict]:
        """解析cookie字符串为Playwright格式"""
        cookies = []
        for item in cookie_string.split(';'):
            if '=' in item:
                name, value = item.strip().split('=', 1)
                cookies.append({
                    'name': name.strip(),
                    'value': value.strip(),
                    'domain': '.yangkeduo.com',
                    'path': '/'
                })
        return cookies
    
    async def start_browser(self):
        """启动浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=True,
            args=['--no-sandbox', '--disable-setuid-sandbox']
        )
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
        })
        
        # 注入cookies
        await self.page.context.add_cookies(self.cookies)
        logger.info("浏览器启动完成，cookies已注入")
    
    async def close_browser(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            logger.info("浏览器已关闭")
    
    async def fetch_merchant_info(self, mall_id: str) -> Dict:
        """
        获取商家详细信息
        
        Args:
            mall_id: 商家ID
            
        Returns:
            Dict: 商家信息
        """
        try:
            # 构建商家页面URL
            shop_url = f"https://mobile.yangkeduo.com/mall_page.html?mall_id={mall_id}"
            logger.info(f"正在获取商家信息: {mall_id}")
            
            # 访问商家页面
            await self.page.goto(shop_url, wait_until='networkidle', timeout=30000)
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 获取页面标题和商家名称
            page_title = await self.page.title()
            
            # 尝试获取商家名称
            merchant_name = ""
            try:
                merchant_name_element = await self.page.query_selector('.shop-name, .mall-name, .store-name, h1')
                if merchant_name_element:
                    merchant_name = await merchant_name_element.inner_text()
            except:
                pass
            
            # 尝试获取商家认证信息
            certification_info = []
            try:
                # 查找认证标签
                cert_elements = await self.page.query_selector_all('.cert-tag, .auth-tag, .badge, .certification')
                for element in cert_elements:
                    cert_text = await element.inner_text()
                    if cert_text.strip():
                        certification_info.append(cert_text.strip())
            except:
                pass
            
            # 尝试获取商家描述信息
            description = ""
            try:
                desc_element = await self.page.query_selector('.shop-desc, .mall-desc, .store-desc')
                if desc_element:
                    description = await desc_element.inner_text()
            except:
                pass
            
            # 检查是否有"官方"、"旗舰"等关键词
            page_content = await self.page.content()
            is_official = any(keyword in page_content for keyword in ['官方', '旗舰', '直营', '授权'])
            is_brand_store = any(keyword in page_content for keyword in ['品牌', '专营', '专卖'])
            
            merchant_info = {
                'mall_id': mall_id,
                'merchant_name': merchant_name,
                'page_title': page_title,
                'certification_info': certification_info,
                'description': description,
                'is_official': is_official,
                'is_brand_store': is_brand_store,
                'url': shop_url,
                'fetch_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            logger.info(f"成功获取商家信息: {mall_id} - {merchant_name}")
            return merchant_info
            
        except Exception as e:
            logger.error(f"获取商家信息失败 {mall_id}: {e}")
            return {
                'mall_id': mall_id,
                'error': str(e),
                'fetch_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
    
    async def fetch_multiple_merchants(self, mall_ids: List[str]) -> List[Dict]:
        """
        批量获取商家信息
        
        Args:
            mall_ids: 商家ID列表
            
        Returns:
            List[Dict]: 商家信息列表
        """
        results = []
        
        await self.start_browser()
        
        try:
            for i, mall_id in enumerate(mall_ids):
                logger.info(f"处理商家 {i+1}/{len(mall_ids)}: {mall_id}")
                
                merchant_info = await self.fetch_merchant_info(mall_id)
                results.append(merchant_info)
                
                # 添加延迟避免被限制
                if i < len(mall_ids) - 1:
                    await asyncio.sleep(2)
                    
        finally:
            await self.close_browser()
        
        return results

async def main():
    """主函数 - 获取所有商家信息"""
    
    # 拼多多cookies
    cookies = "api_uid=CkzjYGiHOJxOPwB2PV2RAg==; jrpl=O458C1f8ceE7XRzytOP2weKHpJYvDrKw; njrpl=O458C1f8ceE7XRzytOP2weKHpJYvDrKw; dilx=I~NXwYTk6a3ILBtpQIPRC; _nano_fp=XpmyXqEbX5XqX0Tyno_G8cF9LUavZXkHzqmC4eKv; webp=1; _tea_utm_cache_10000007=undefined; PDDAccessToken=BWLO3NRCFTZQJF4POFA4VIB5HI5CVWKGXWIGA2HJLBCI4OR2UNWA1206ea6; pdd_user_id=4389435502; pdd_user_uin=M76CUMWMSNHHSN6N3QQPCJYNZM_GEXDA; pdd_vds=gaLLNyGIEbIttQPoitLNPoEIymIinnQOaanNLboQiNNybPaonnNaILGmaPLi"
    
    # 从API响应中提取的商家ID列表
    mall_ids = [
        "875407409", "901511724", "513337164", "840678495", "2208223",
        "395985537", "943298289", "750566664", "841299701", "583679298",
        "767867311", "288384549", "463433338", "647261213", "715518810",
        "652448514", "257573573", "585341751", "107548208", "339929837"
    ]
    
    # 创建获取器
    fetcher = MerchantInfoFetcher(cookies)
    
    # 获取商家信息
    logger.info(f"开始获取 {len(mall_ids)} 个商家的详细信息...")
    merchant_data = await fetcher.fetch_multiple_merchants(mall_ids)
    
    # 保存结果
    output_file = "merchant_info_data.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(merchant_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"商家信息已保存到: {output_file}")
    
    # 打印摘要
    successful = len([m for m in merchant_data if 'error' not in m])
    failed = len(merchant_data) - successful
    
    print(f"\n📊 获取结果摘要:")
    print(f"✅ 成功: {successful} 个商家")
    print(f"❌ 失败: {failed} 个商家")
    
    # 显示部分结果
    print(f"\n🏪 商家信息预览:")
    for merchant in merchant_data[:5]:
        if 'error' not in merchant:
            print(f"- {merchant['mall_id']}: {merchant.get('merchant_name', '未知')} ({'官方店' if merchant.get('is_official') else '普通店'})")

if __name__ == "__main__":
    asyncio.run(main())