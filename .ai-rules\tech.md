---
title: 技术栈与架构
description: 项目的技术栈、架构设计、依赖管理和开发规范
inclusion: always
---

# 技术栈与架构设计

## 核心技术栈

### 编程语言
- **Python 3.8+**：主要开发语言
- **JavaScript/TypeScript**：前端开发（React）
- **异步编程**：基于asyncio的异步架构

### 后端技术

#### 浏览器自动化
- **Playwright 1.54.0+**：现代化浏览器自动化框架
- **playwright-stealth 2.0.0+**：反检测插件，绕过网站反爬虫
- **CDP（Chrome DevTools Protocol）**：可选的真实浏览器控制

#### 数据处理
- **orjson 3.11.0+**：高性能JSON解析（比标准库快2倍）
- **pandas 2.0.0+**：数据分析和处理
- **openpyxl 3.1.5+**：Excel文件操作
- **rapidfuzz 3.10.0+**：高性能模糊字符串匹配（品牌识别）

#### 日志与配置
- **loguru 0.7.3+**：现代化日志管理
- **pyyaml 6.0.2+**：YAML配置文件解析
- **python-dateutil 2.9.0+**：日期时间处理

#### 错误处理
- **tenacity 9.1.0+**：智能重试机制
- **greenlet 3.2.2+**：异步处理支持

### 前端技术

#### 框架与库
- **React 18.2.0**：UI框架
- **Ant Design 5.12.8**：UI组件库
- **TypeScript 5.3.3**：类型安全的JavaScript
- **Vite 5.0.8**：现代化构建工具

#### 数据展示
- **react-window 1.8.10**：虚拟列表，处理大量数据
- **@tanstack/react-virtual 3.0.1**：虚拟滚动优化
- **dayjs 1.11.10**：日期处理

### API服务（可选）
- **FastAPI**：高性能Web API框架
- **WebSocket**：实时数据推送
- **Pydantic**：数据验证

## 系统架构

### 整体架构
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Web前端界面    │ ←→  │   FastAPI后端   │ ←→  │   爬虫核心引擎   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         ↓                       ↓                        ↓
  ┌──────────────┐      ┌──────────────┐       ┌──────────────┐
  │ React + Antd │      │  WebSocket   │       │  Playwright  │
  └──────────────┘      └──────────────┘       └──────────────┘
                                ↓                        ↓
                        ┌──────────────┐       ┌──────────────┐
                        │ 任务管理队列  │       │  数据处理器   │
                        └──────────────┘       └──────────────┘
                                                        ↓
                                                ┌──────────────┐
                                                │ Excel导出器  │
                                                └──────────────┘
```

### 核心模块设计

#### 1. 浏览器管理模块 (browser_manager.py)
- **职责**：浏览器实例管理、反检测配置
- **关键功能**：
  - 浏览器启动参数优化（40个核心参数）
  - Stealth插件集成
  - 资源生命周期管理

#### 2. API拦截模块 (api_response_monitor.py)
- **职责**：拦截和解析API响应数据
- **目标API**：`/proxy/api/search`
- **数据解析路径**：
  - data.goods
  - data.goods_list
  - data.mall_goods_list

#### 3. 反检测模块 (anti_detection_simple.py)
- **职责**：风控检测和应对策略
- **检测指标**：
  - HTTP状态码（429、403、503）
  - 页面关键词检测
  - 风控元素检测

#### 4. 数据处理模块 (processor.py)
- **职责**：数据清洗、去重、格式化
- **核心功能**：
  - 基于商品ID的去重（O(1)复杂度）
  - 品牌识别优化（rapidfuzz）
  - 营销标签处理

#### 5. 滚动管理模块 (scroll_manager.py)
- **职责**：智能页面滚动控制
- **自适应策略**：
  - 动态延迟调整（0.1-0.5秒）
  - 滚动距离随机化（1600-2200像素）
  - 连续无数据检测

## 开发规范

### 代码风格
- **缩进**：4个空格
- **命名规范**：
  - 类名：PascalCase
  - 函数/变量：snake_case
  - 常量：UPPER_CASE
- **文档字符串**：Google风格
- **类型注解**：使用Type Hints

### 项目结构
```
pdd2/
├── .ai-rules/              # AI指导文档
├── backend/                # API服务（可选）
├── config/                 # 配置文件
│   ├── settings.yaml       # 主配置
│   ├── cookies.json        # Cookie存储
│   └── user_agents.json    # User-Agent池
├── frontend/               # Web前端
│   ├── src/               # React源码
│   └── public/            # 静态资源
├── src/                   # 爬虫核心代码
│   ├── core/              # 核心功能模块
│   ├── data/              # 数据处理模块
│   ├── utils/             # 工具函数
│   └── main.py            # 主程序入口
├── output/                # 数据输出目录
├── logs/                  # 日志目录
└── browser_data/          # 浏览器数据（CDP模式）
```

### 异步编程规范
- **统一使用async/await**：所有IO操作异步化
- **并发控制**：使用asyncio.Lock保护共享资源
- **异常处理**：区分可重试和不可重试异常
- **超时控制**：所有网络操作设置合理超时

## 性能优化策略

### 启动优化
- **浏览器参数精简**：从168个减少到40个核心参数
- **按需加载模块**：延迟导入非必要模块
- **预编译正则表达式**：提高匹配效率

### 运行时优化
- **JSON解析优化**：优先使用orjson（性能提升200%）
- **内存管理**：及时清理不需要的数据引用
- **批量处理**：数据批量写入Excel
- **连接复用**：保持浏览器实例复用

### 数据处理优化
- **流式处理**：避免一次性加载大量数据
- **去重优化**：使用set数据结构（O(1)查找）
- **品牌识别**：rapidfuzz替代传统字符串匹配

## 依赖管理

### 版本控制策略
- **主版本锁定**：核心依赖锁定主版本
- **语义化版本**：遵循semver规范
- **最小依赖原则**：只引入必要的依赖

### 依赖更新流程
1. 定期检查依赖更新
2. 在测试环境验证兼容性
3. 逐个更新，避免批量升级
4. 记录更新日志

## 测试策略

### 单元测试
- **覆盖率目标**：核心模块80%以上
- **测试框架**：pytest + pytest-asyncio
- **模拟策略**：Mock外部依赖

### 集成测试
- **端到端测试**：完整流程验证
- **性能测试**：监控资源使用
- **稳定性测试**：长时间运行验证

## 部署考虑

### 运行环境
- **操作系统**：Windows、Linux、macOS
- **Python版本**：3.8-3.11
- **浏览器要求**：Chrome/Chromium 100+

### 资源需求
- **内存**：最低2GB，推荐4GB+
- **CPU**：2核心以上
- **存储**：1GB可用空间
- **网络**：稳定的互联网连接

## 安全考虑

### 数据安全
- **Cookie加密**：敏感信息不明文存储
- **日志脱敏**：不记录敏感数据
- **访问控制**：限制文件权限

### 网络安全
- **HTTPS only**：只访问HTTPS站点
- **证书验证**：可选的证书验证
- **代理支持**：支持HTTP/HTTPS代理