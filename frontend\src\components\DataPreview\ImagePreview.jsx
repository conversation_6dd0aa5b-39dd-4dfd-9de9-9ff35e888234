import React, { useState, useEffect, useCallback } from 'react'
import clsx from 'clsx'
import './ImagePreview.css'

export default function ImagePreview({ 
  images = [], 
  visible, 
  onClose, 
  currentIndex = 0 
}) {
  const [activeIndex, setActiveIndex] = useState(currentIndex)
  const [scale, setScale] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  useEffect(() => {
    if (visible) {
      setActiveIndex(currentIndex)
      setScale(1)
      setPosition({ x: 0, y: 0 })
    }
  }, [visible, currentIndex])

  // 键盘事件处理
  useEffect(() => {
    if (!visible) return

    const handleKeyDown = (e) => {
      switch (e.key) {
        case 'Escape':
          onClose()
          break
        case 'ArrowLeft':
          handlePrevious()
          break
        case 'ArrowRight':
          handleNext()
          break
        case '+':
        case '=':
          handleZoomIn()
          break
        case '-':
        case '_':
          handleZoomOut()
          break
        default:
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [visible, activeIndex, images.length])

  // 切换上一张
  const handlePrevious = useCallback(() => {
    setActiveIndex((prev) => (prev - 1 + images.length) % images.length)
    resetTransform()
  }, [images.length])

  // 切换下一张
  const handleNext = useCallback(() => {
    setActiveIndex((prev) => (prev + 1) % images.length)
    resetTransform()
  }, [images.length])

  // 放大
  const handleZoomIn = useCallback(() => {
    setScale((prev) => Math.min(prev * 1.2, 3))
  }, [])

  // 缩小
  const handleZoomOut = useCallback(() => {
    setScale((prev) => Math.max(prev / 1.2, 0.5))
  }, [])

  // 重置缩放和位置
  const resetTransform = useCallback(() => {
    setScale(1)
    setPosition({ x: 0, y: 0 })
  }, [])

  // 鼠标滚轮缩放
  const handleWheel = useCallback((e) => {
    e.preventDefault()
    if (e.deltaY < 0) {
      handleZoomIn()
    } else {
      handleZoomOut()
    }
  }, [handleZoomIn, handleZoomOut])

  // 拖拽开始
  const handleMouseDown = useCallback((e) => {
    if (scale > 1) {
      setIsDragging(true)
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      })
    }
  }, [scale, position])

  // 拖拽移动
  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }, [isDragging, dragStart])

  // 拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  if (!visible || images.length === 0) return null

  return (
    <div className="image-preview-overlay" onClick={onClose}>
      <div className="preview-container" onClick={(e) => e.stopPropagation()}>
        {/* 工具栏 */}
        <div className="preview-toolbar">
          <div className="toolbar-left">
            <span className="image-counter">
              {activeIndex + 1} / {images.length}
            </span>
          </div>
          <div className="toolbar-center">
            <button 
              className="toolbar-btn"
              onClick={handleZoomOut}
              disabled={scale <= 0.5}
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M5 10h10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                <circle cx="10" cy="10" r="7" fill="none" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </button>
            <span className="zoom-level">{Math.round(scale * 100)}%</span>
            <button 
              className="toolbar-btn"
              onClick={handleZoomIn}
              disabled={scale >= 3}
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 5v10M5 10h10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                <circle cx="10" cy="10" r="7" fill="none" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </button>
            <button className="toolbar-btn" onClick={resetTransform}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M14 6l-8 8m0-8l8 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </button>
          </div>
          <div className="toolbar-right">
            <button className="toolbar-btn close-btn" onClick={onClose}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M15 5L5 15M5 5l10 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        {/* 图片显示区域 */}
        <div 
          className="preview-image-container"
          onWheel={handleWheel}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <img
            src={images[activeIndex]}
            alt={`预览图片 ${activeIndex + 1}`}
            style={{
              transform: `scale(${scale}) translate(${position.x / scale}px, ${position.y / scale}px)`,
              cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
            }}
            draggable={false}
          />
        </div>

        {/* 导航按钮 */}
        {images.length > 1 && (
          <>
            <button 
              className={clsx('nav-btn prev', {
                'disabled': activeIndex === 0
              })}
              onClick={handlePrevious}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
              </svg>
            </button>
            <button 
              className={clsx('nav-btn next', {
                'disabled': activeIndex === images.length - 1
              })}
              onClick={handleNext}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
              </svg>
            </button>
          </>
        )}

        {/* 缩略图列表 */}
        {images.length > 1 && (
          <div className="preview-thumbnails">
            {images.map((img, index) => (
              <div
                key={index}
                className={clsx('preview-thumbnail', {
                  'active': index === activeIndex
                })}
                onClick={() => {
                  setActiveIndex(index)
                  resetTransform()
                }}
              >
                <img src={img} alt={`缩略图 ${index + 1}`} />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}