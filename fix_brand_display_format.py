#!/usr/bin/env python3
"""
修改品牌显示格式
如果您希望子品牌只显示为"统帅"而不是"统帅(海尔)"，运行此脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def fix_brand_display_format():
    """修改品牌显示格式，只显示子品牌"""
    
    processor_file = Path("src/data/processor.py")
    
    print("=== 修改品牌显示格式 ===")
    print("当前格式：子品牌(主品牌) 如 '统帅(海尔)'")
    print("修改后格式：只显示子品牌 如 '统帅'")
    
    response = input("\n是否要修改品牌显示格式？(y/n): ")
    
    if response.lower() != 'y':
        print("取消修改")
        return
    
    # 读取文件内容
    content = processor_file.read_text(encoding='utf-8')
    
    # 查找需要修改的代码行
    old_code = 'return f"{display_sub_brand}({display_main_brand})"'
    new_code = 'return display_sub_brand  # 修改：只显示子品牌'
    
    if old_code in content:
        # 替换代码
        new_content = content.replace(old_code, new_code)
        
        # 备份原文件
        backup_file = processor_file.with_suffix('.py.bak')
        processor_file.rename(backup_file)
        print(f"\n✅ 已备份原文件到: {backup_file}")
        
        # 写入新内容
        processor_file.write_text(new_content, encoding='utf-8')
        print("✅ 品牌显示格式已修改")
        print("\n修改后效果：")
        print("  - 统帅品牌商品将显示为：统帅")
        print("  - 卡萨帝品牌商品将显示为：卡萨帝")
        print("  - 小天鹅品牌商品将显示为：小天鹅")
        
    else:
        print("\n❌ 未找到需要修改的代码，可能已经修改过了")

def restore_brand_display_format():
    """恢复原始品牌显示格式"""
    
    processor_file = Path("src/data/processor.py")
    backup_file = processor_file.with_suffix('.py.bak')
    
    if backup_file.exists():
        response = input("\n是否要恢复原始品牌显示格式？(y/n): ")
        
        if response.lower() == 'y':
            # 恢复备份
            processor_file.unlink()
            backup_file.rename(processor_file)
            print("✅ 已恢复原始品牌显示格式")
    else:
        print("❌ 未找到备份文件")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--restore':
        restore_brand_display_format()
    else:
        fix_brand_display_format()