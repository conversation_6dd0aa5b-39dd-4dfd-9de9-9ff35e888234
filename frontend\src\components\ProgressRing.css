.progress-ring-container {
  background: var(--surface);
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border);
  overflow: hidden;
}

/* 头部 */
.progress-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(238, 77, 45, 0.05) 0%, rgba(238, 77, 45, 0.02) 100%);
  border-bottom: 1px solid var(--border);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.progress-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

/* 进度环 */
.ring-wrapper {
  padding: 40px;
  display: flex;
  justify-content: center;
  background: linear-gradient(180deg, var(--surface) 0%, rgba(238, 77, 45, 0.02) 100%);
}

.progress-ring {
  position: relative;
  width: 180px;
  height: 180px;
}

.progress-ring.pulsing::before {
  content: '';
  position: absolute;
  inset: -20px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(238, 77, 45, 0.1) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0; }
  50% { transform: scale(1.1); opacity: 1; }
}

.progress-ring svg {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.5s ease-in-out;
}

.ring-content {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.percentage {
  font-size: 36px;
  font-weight: 700;
  color: var(--text);
  line-height: 1;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
}

.status-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--secondary);
  border-radius: 12px;
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: var(--surface);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 实时指示器 */
.live-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(33, 150, 243, 0.05);
  border-top: 1px solid rgba(33, 150, 243, 0.2);
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #2196F3;
  animation: blink 1.5s ease-in-out infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(0.8); }
}

.live-text {
  font-size: 14px;
  color: #2196F3;
  font-weight: 500;
}

/* 暗色主题 */
[data-theme="dark"] .progress-ring-container {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .progress-header {
  background: linear-gradient(135deg, rgba(255, 104, 84, 0.1) 0%, rgba(255, 104, 84, 0.05) 100%);
}

[data-theme="dark"] .ring-wrapper {
  background: linear-gradient(180deg, var(--surface) 0%, rgba(255, 104, 84, 0.05) 100%);
}

[data-theme="dark"] .stat-card {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .stat-icon {
  background: rgba(255, 255, 255, 0.08);
}

/* 响应式 */
@media (max-width: 768px) {
  .ring-wrapper {
    padding: 30px;
  }
  
  .progress-ring {
    width: 150px;
    height: 150px;
  }
  
  .percentage {
    font-size: 30px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }
}