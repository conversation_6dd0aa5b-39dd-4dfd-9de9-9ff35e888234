.sort-selector {
  animation: fadeIn 0.4s ease;
}

.sort-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-title svg {
  color: var(--primary);
}

.sort-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.sort-option {
  position: relative;
  cursor: pointer;
  display: block;
}

.sort-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.sort-option-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid var(--border);
  border-radius: var(--radius-sm);
  background: var(--surface);
  transition: all 0.2s ease;
  position: relative;
}

.sort-option:hover .sort-option-content {
  border-color: var(--primary);
  background: rgba(238, 77, 45, 0.05);
}

.sort-option.active .sort-option-content {
  border-color: var(--primary);
  background: rgba(238, 77, 45, 0.1);
  box-shadow: 0 0 0 3px rgba(238, 77, 45, 0.1);
}

.sort-option:disabled .sort-option-content {
  opacity: 0.5;
  cursor: not-allowed;
}

.sort-icon {
  font-size: 20px;
  line-height: 1;
}

.sort-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
}

.check-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  color: var(--primary);
  animation: scaleIn 0.2s ease;
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

.sort-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: var(--radius-sm);
  font-size: 12px;
  color: var(--info);
}

.sort-info svg {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .sort-options {
    grid-template-columns: 1fr;
  }
}