/* Tooltip 样式 */
.tooltip-trigger {
  display: inline-flex;
  cursor: help;
}

.tooltip {
  position: fixed;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
  animation: tooltipFadeIn var(--duration-200) var(--ease-out);
}

.tooltip-content {
  background-color: var(--color-gray-800);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  box-shadow: var(--shadow-lg);
  max-width: 250px;
  word-wrap: break-word;
}

/* 深色模式调整 */
[data-theme="dark"] .tooltip-content {
  background-color: var(--color-gray-700);
  border: 1px solid var(--border-primary);
}

/* 箭头样式 */
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.tooltip-top .tooltip-arrow {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 4px 4px 0 4px;
  border-color: var(--color-gray-800) transparent transparent transparent;
}

.tooltip-bottom .tooltip-arrow {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 4px 4px 4px;
  border-color: transparent transparent var(--color-gray-800) transparent;
}

.tooltip-left .tooltip-arrow {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 4px 0 4px 4px;
  border-color: transparent transparent transparent var(--color-gray-800);
}

.tooltip-right .tooltip-arrow {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 4px 4px 4px 0;
  border-color: transparent var(--color-gray-800) transparent transparent;
}

/* 深色模式箭头调整 */
[data-theme="dark"] .tooltip-top .tooltip-arrow {
  border-color: var(--color-gray-700) transparent transparent transparent;
}

[data-theme="dark"] .tooltip-bottom .tooltip-arrow {
  border-color: transparent transparent var(--color-gray-700) transparent;
}

[data-theme="dark"] .tooltip-left .tooltip-arrow {
  border-color: transparent transparent transparent var(--color-gray-700);
}

[data-theme="dark"] .tooltip-right .tooltip-arrow {
  border-color: transparent var(--color-gray-700) transparent transparent;
}

/* 动画 */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}