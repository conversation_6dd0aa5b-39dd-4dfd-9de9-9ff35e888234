import React, { useState, useCallback, useMemo } from 'react'
import { FixedSizeGrid as Grid } from 'react-window'
import InfiniteLoader from 'react-window-infinite-loader'
import clsx from 'clsx'
import './DataGrid.css'

const COLUMN_WIDTH = 280
const ROW_HEIGHT = 360
const GUTTER_SIZE = 16

export default function DataGrid({
  data = [],
  loading = false,
  hasMore = false,
  onLoadMore = () => {},
  onItemClick = () => {},
  selectedItems = [],
  onSelectionChange = () => {},
  containerWidth = 1200
}) {
  const [hoveredIndex, setHoveredIndex] = useState(null)

  // 计算列数
  const columnCount = useMemo(() => {
    return Math.max(1, Math.floor((containerWidth - GUTTER_SIZE) / (COLUMN_WIDTH + GUTTER_SIZE)))
  }, [containerWidth])

  // 计算行数
  const rowCount = useMemo(() => {
    return Math.ceil(data.length / columnCount)
  }, [data.length, columnCount])

  // 格式化价格
  const formatPrice = useCallback((price) => {
    return `¥${parseFloat(price).toFixed(2)}`
  }, [])

  // 格式化销量
  const formatSales = useCallback((sales) => {
    if (sales >= 10000) {
      return `${(sales / 10000).toFixed(1)}万+`
    }
    if (sales >= 1000) {
      return `${(sales / 1000).toFixed(1)}k+`
    }
    return sales.toString()
  }, [])

  // 获取项目索引
  const getItemIndex = useCallback((rowIndex, columnIndex) => {
    return rowIndex * columnCount + columnIndex
  }, [columnCount])

  // 检查是否已加载
  const isItemLoaded = useCallback((index) => {
    return !hasMore || index < data.length
  }, [hasMore, data.length])

  // 加载更多
  const loadMoreItems = useCallback(() => {
    if (!loading && hasMore) {
      return onLoadMore()
    }
    return Promise.resolve()
  }, [loading, hasMore, onLoadMore])

  // 处理选择
  const handleSelect = useCallback((item, e) => {
    e.stopPropagation()
    const isSelected = selectedItems.some(selected => selected.id === item.id)
    if (isSelected) {
      onSelectionChange(selectedItems.filter(selected => selected.id !== item.id))
    } else {
      onSelectionChange([...selectedItems, item])
    }
  }, [selectedItems, onSelectionChange])

  // 渲染单元格
  const Cell = useCallback(({ columnIndex, rowIndex, style }) => {
    const itemIndex = getItemIndex(rowIndex, columnIndex)
    
    if (itemIndex >= data.length) {
      return null
    }

    if (!isItemLoaded(itemIndex)) {
      return (
        <div style={{
          ...style,
          left: style.left + GUTTER_SIZE,
          top: style.top + GUTTER_SIZE,
          width: style.width - GUTTER_SIZE,
          height: style.height - GUTTER_SIZE,
        }}>
          <div className="grid-item loading">
            <div className="loading-skeleton"></div>
          </div>
        </div>
      )
    }

    const item = data[itemIndex]
    const isSelected = selectedItems.some(selected => selected.id === item.id)
    const isHovered = hoveredIndex === itemIndex

    return (
      <div style={{
        ...style,
        left: style.left + GUTTER_SIZE,
        top: style.top + GUTTER_SIZE,
        width: style.width - GUTTER_SIZE,
        height: style.height - GUTTER_SIZE,
      }}>
        <div
          className={clsx('grid-item', {
            'selected': isSelected,
            'hovered': isHovered
          })}
          onClick={() => onItemClick(item)}
          onMouseEnter={() => setHoveredIndex(itemIndex)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <div className="grid-item-checkbox">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => handleSelect(item, e)}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
          
          <div className="grid-item-image">
            <img
              src={item.image_url || item.thumb_url}
              alt={item.goods_name}
              loading="lazy"
              onError={(e) => {
                e.target.src = 'https://via.placeholder.com/240x240'
              }}
            />
            {item.coupon_price && item.coupon_price < item.price && (
              <div className="coupon-badge">有券</div>
            )}
          </div>

          <div className="grid-item-content">
            <h3 className="grid-item-title">{item.goods_name}</h3>
            
            <div className="grid-item-price">
              <span className="current-price">{formatPrice(item.price)}</span>
              {item.market_price && item.market_price > item.price && (
                <span className="original-price">¥{item.market_price}</span>
              )}
            </div>

            <div className="grid-item-info">
              <div className="info-item">
                <span className="info-value">{formatSales(item.sales)}</span>
                <span className="info-label">已售</span>
              </div>
              {item.comment_count && (
                <div className="info-item">
                  <span className="info-value">{item.comment_count}</span>
                  <span className="info-label">评价</span>
                </div>
              )}
            </div>

            <div className="grid-item-shop">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                <path d="M1 4h10v6a1 1 0 01-1 1H2a1 1 0 01-1-1V4z"/>
                <path d="M1 3l5-2 5 2v1H1V3z"/>
              </svg>
              <span>{item.shop_name}</span>
            </div>

            {item.tags && item.tags.length > 0 && (
              <div className="grid-item-tags">
                {item.tags.slice(0, 3).map((tag, index) => (
                  <span key={index} className="tag">{tag}</span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }, [data, selectedItems, hoveredIndex, isItemLoaded, formatPrice, formatSales, getItemIndex, handleSelect, onItemClick])

  // 空状态
  if (!loading && data.length === 0) {
    return (
      <div className="data-grid-container">
        <div className="empty-state">
          <div className="empty-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
              <rect x="8" y="8" width="20" height="20" rx="4" stroke="currentColor" strokeWidth="2"/>
              <rect x="36" y="8" width="20" height="20" rx="4" stroke="currentColor" strokeWidth="2"/>
              <rect x="8" y="36" width="20" height="20" rx="4" stroke="currentColor" strokeWidth="2"/>
              <rect x="36" y="36" width="20" height="20" rx="4" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <h3 className="empty-title">暂无数据</h3>
          <p className="empty-description">开始爬取后，商品数据将实时显示在这里</p>
        </div>
      </div>
    )
  }

  const itemCount = hasMore ? data.length + columnCount : data.length
  const totalRowCount = Math.ceil(itemCount / columnCount)

  return (
    <div className="data-grid-container">
      <InfiniteLoader
        isItemLoaded={isItemLoaded}
        itemCount={itemCount}
        loadMoreItems={loadMoreItems}
      >
        {({ onItemsRendered, ref }) => (
          <Grid
            ref={ref}
            columnCount={columnCount}
            columnWidth={COLUMN_WIDTH + GUTTER_SIZE}
            height={600}
            rowCount={totalRowCount}
            rowHeight={ROW_HEIGHT + GUTTER_SIZE}
            width={containerWidth}
            onItemsRendered={({
              visibleRowStartIndex,
              visibleRowStopIndex,
              visibleColumnStartIndex,
              visibleColumnStopIndex,
            }) => {
              const visibleStartIndex = visibleRowStartIndex * columnCount + visibleColumnStartIndex
              const visibleStopIndex = visibleRowStopIndex * columnCount + visibleColumnStopIndex
              onItemsRendered({
                visibleStartIndex,
                visibleStopIndex
              })
            }}
          >
            {Cell}
          </Grid>
        )}
      </InfiniteLoader>
    </div>
  )
}