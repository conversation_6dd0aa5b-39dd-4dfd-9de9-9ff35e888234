/* 确认对话框样式 */
.confirm-dialog {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  max-width: 400px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  animation: scaleIn var(--duration-300) var(--ease-spring);
  border: 1px solid var(--border-primary);
  text-align: center;
}

.confirm-dialog-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-6);
}

.confirm-dialog-icon-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-600);
}

.confirm-dialog-icon-danger {
  background-color: var(--color-error-100);
  color: var(--color-error-600);
}

.confirm-dialog-icon-info {
  background-color: var(--color-info-100);
  color: var(--color-info-600);
}

/* 深色模式图标调整 */
[data-theme="dark"] .confirm-dialog-icon-warning {
  background-color: rgba(245, 158, 11, 0.2);
  color: var(--color-warning-400);
}

[data-theme="dark"] .confirm-dialog-icon-danger {
  background-color: rgba(239, 68, 68, 0.2);
  color: var(--color-error-400);
}

[data-theme="dark"] .confirm-dialog-icon-info {
  background-color: rgba(59, 130, 246, 0.2);
  color: var(--color-info-400);
}

.confirm-dialog-content {
  margin-bottom: var(--spacing-6);
}

.confirm-dialog-title {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.confirm-dialog-message {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.confirm-dialog-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
}

/* 按钮样式调整 */
.confirm-dialog .btn {
  min-width: 100px;
}

.confirm-dialog .btn-warning {
  background-color: var(--color-warning-500);
  color: white;
}

.confirm-dialog .btn-warning:hover {
  background-color: var(--color-warning-600);
}

.confirm-dialog .btn-danger {
  background-color: var(--color-error-500);
  color: white;
}

.confirm-dialog .btn-danger:hover {
  background-color: var(--color-error-600);
}

.confirm-dialog .btn-info {
  background-color: var(--color-info-500);
  color: white;
}

.confirm-dialog .btn-info:hover {
  background-color: var(--color-info-600);
}

/* 响应式 */
@media (max-width: 480px) {
  .confirm-dialog {
    padding: var(--spacing-4);
  }
  
  .confirm-dialog-icon {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-4);
  }
  
  .confirm-dialog-icon svg {
    width: 32px;
    height: 32px;
  }
  
  .confirm-dialog-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .confirm-dialog .btn {
    width: 100%;
  }
}