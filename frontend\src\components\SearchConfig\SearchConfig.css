.search-config {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.config-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #999;
  cursor: help;
  font-size: 16px;
}

.config-form {
  width: 100%;
}

.keywords-tags {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.keywords-count {
  color: #666;
  font-size: 14px;
  margin-left: 8px;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.advanced-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.advanced-section a {
  color: #1890ff;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
}

.advanced-section a:hover {
  opacity: 0.8;
}

.estimate-info {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  justify-content: space-around;
}

.estimate-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.estimate-label {
  color: #666;
  font-size: 14px;
}

.estimate-value {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

/* 深色模式支持 */
[data-theme="dark"] .search-config {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .config-header h3 {
  color: #fff;
}

[data-theme="dark"] .keywords-count {
  color: #ccc;
}

[data-theme="dark"] .estimate-info {
  background: #2a2a2a;
}

[data-theme="dark"] .estimate-label {
  color: #ccc;
}

[data-theme="dark"] .estimate-value {
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .estimate-info {
    flex-direction: column;
    gap: 12px;
  }
}