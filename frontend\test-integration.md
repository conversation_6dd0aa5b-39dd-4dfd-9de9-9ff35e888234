# 拼多多爬虫前端集成测试指南

## 测试前准备

1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **启动后端服务**
   ```bash
   cd ..
   python start.py
   ```

3. **启动前端开发服务器**
   ```bash
   cd frontend
   npm run dev
   ```

## 功能测试清单

### 1. Cookie管理功能 ✅
- [ ] 打开应用，检查是否显示"未配置有效的Cookie"警告
- [ ] 点击"配置Cookie"按钮，弹出Cookie管理窗口
- [ ] 测试不同格式的Cookie输入：
  - 字符串格式：`name=value; name2=value2`
  - JSON格式：`[{"name":"test","value":"123"}]`
  - 浏览器格式：从浏览器开发者工具复制
- [ ] 验证Cookie格式检查功能
- [ ] 测试Cookie保存和状态更新
- [ ] 测试Cookie导入/导出功能

### 2. 搜索配置功能 ✅
- [ ] 输入单个关键词，按回车添加
- [ ] 输入多个关键词，验证标签显示
- [ ] 测试删除关键词标签
- [ ] 调整爬取页数和目标数量
- [ ] 选择不同的排序方式
- [ ] 验证预估时间和商品数量显示

### 3. WebSocket实时进度监控 ✅
- [ ] 点击"开始爬取"按钮
- [ ] 检查进度监控组件是否显示
- [ ] 验证进度条动画效果
- [ ] 检查状态指示器（运行中/暂停/完成）
- [ ] 验证时间估算显示
- [ ] 测试错误信息展示
- [ ] 测试暂停/恢复功能

### 4. 数据预览功能 ✅
- [ ] 等待爬取一些数据后，检查数据预览区域
- [ ] 测试表格视图和网格视图切换
- [ ] 测试搜索功能（商品名称、店铺）
- [ ] 测试价格筛选功能
- [ ] 测试列排序功能
- [ ] 测试批量选择功能
- [ ] 点击商品查看详情
- [ ] 测试图片预览功能

### 5. UI/UX功能 ✅
- [ ] 测试深色模式切换（点击右上角主题按钮）
- [ ] 验证动画效果是否流畅
- [ ] 测试键盘快捷键：
  - `Ctrl+S`：开始爬取
  - `Ctrl+Shift+S`：停止爬取
  - `Ctrl+E`：导出数据
  - `Ctrl+M`：打开Cookie管理
  - `?`：显示帮助
- [ ] 验证Toast通知显示
- [ ] 测试确认对话框（导出时）
- [ ] 检查响应式布局（调整浏览器窗口大小）

### 6. 数据导出功能 ✅
- [ ] 爬取完成后，点击"导出数据"按钮
- [ ] 确认导出对话框显示
- [ ] 验证Excel文件下载

## 性能测试

### 1. 加载性能
- [ ] 检查首屏加载时间（<3秒）
- [ ] 验证组件懒加载是否生效

### 2. 大数据量测试
- [ ] 爬取1000+条数据，验证虚拟滚动性能
- [ ] 测试表格和网格视图切换流畅度
- [ ] 验证搜索和筛选响应速度

### 3. 内存使用
- [ ] 长时间运行，检查内存是否持续增长
- [ ] 验证组件卸载时是否正确清理

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome/Edge (最新版)
- [ ] Firefox (最新版)
- [ ] Safari (如果有Mac)

### 2. 设备兼容性
- [ ] 桌面端（1920x1080）
- [ ] 平板端（768x1024）
- [ ] 移动端（375x667）

## 常见问题排查

### 1. Cookie验证失败
- 确保Cookie格式正确
- 检查Cookie是否过期
- 验证后端API是否正常运行

### 2. WebSocket连接失败
- 检查后端WebSocket服务是否启动
- 查看浏览器控制台错误信息
- 确认防火墙没有阻止连接

### 3. 数据加载缓慢
- 检查网络连接
- 验证后端爬虫是否正常工作
- 查看是否触发了反爬虫机制

### 4. 样式显示异常
- 清除浏览器缓存
- 检查是否有CSS文件加载失败
- 验证Ant Design样式是否正确引入

## 测试报告模板

```markdown
## 测试报告

**测试日期**: 2024-XX-XX
**测试环境**: Windows 11 + Chrome 120
**测试人员**: XXX

### 测试结果汇总
- Cookie管理: ✅ 通过
- 搜索配置: ✅ 通过
- 进度监控: ✅ 通过
- 数据预览: ✅ 通过
- UI/UX: ✅ 通过
- 数据导出: ✅ 通过

### 发现的问题
1. [问题描述]
2. [问题描述]

### 改进建议
1. [建议内容]
2. [建议内容]
```

## 部署前检查

1. **生产构建**
   ```bash
   npm run build
   ```

2. **构建产物检查**
   - 检查dist目录是否生成
   - 验证文件大小是否合理
   - 测试生产版本功能

3. **环境变量配置**
   - 确保API地址配置正确
   - WebSocket地址配置正确

4. **性能优化确认**
   - 代码分割是否生效
   - 静态资源是否压缩
   - 图片是否优化