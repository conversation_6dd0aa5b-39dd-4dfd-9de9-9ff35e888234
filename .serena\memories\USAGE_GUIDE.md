# 使用指南

## 快速开始

### 1. 安装项目
```bash
# 克隆或下载项目后
cd "拼多多爬虫项目目录"

# 安装依赖
python install.py
# 或手动安装
pip install -r requirements.txt
```

### 2. 配置Cookie（必需）
编辑 `config/cookies.json`，添加真实的拼多多Cookie：
```json
{
  "cookies": [
    {
      "name": "PDDAccessToken",
      "value": "你的Token值",
      "domain": ".yangkeduo.com",
      "path": "/"
    },
    {
      "name": "pdd_user_id",
      "value": "你的用户ID",
      "domain": ".yangkeduo.com",
      "path": "/"
    }
    // ... 其他必要的Cookie
  ]
}
```

**获取Cookie方法**:
1. 在浏览器中登录拼多多
2. 打开开发者工具 (F12)
3. 进入Application/Storage > Cookies
4. 复制相关Cookie值

### 3. 配置搜索参数
编辑 `config/settings.yaml`：
```yaml
search:
  keywords:
    - 手机      # 添加你要搜索的关键词
    - 耳机
    - 充电宝
  target_count: 40   # 每个关键词爬取的商品数量
  max_pages: 5       # 最大页数限制
```

### 4. 运行爬虫
```bash
# 推荐方式
python run_main.py

# 调试模式（如果需要）
python run_debug.py
```

## 高级配置

### 启用多排序爬取
```yaml
sorting:
  enabled: true    # 设为true启用
  types:
    - name: 综合排序
      value: comprehensive
    - name: 销量排序
      value: sales_desc
    - name: 价格从低到高
      value: price_asc
```
**注意**: 启用排序会显著增加爬取时间

### 调整爬取速度
```yaml
scroll:
  adaptive_delay:
    min: 3    # 减小值可加快速度（风险增加）
    max: 10   # 减小值可加快速度
  max_scrolls: 5   # 增加可获取更多数据
```

### 浏览器模式
```yaml
browser:
  headless: true   # true=无头模式，false=有头模式
  slow_mo: 100     # 减小值可加快操作速度
```

## 常见问题

### 1. 429错误（请求过于频繁）
**原因**: 触发拼多多风控
**解决方案**:
- 增加延迟时间
- 更换IP地址
- 等待一段时间后重试
- 确保Cookie有效

### 2. 未收集到数据
**可能原因**:
- Cookie过期或无效
- 网络连接问题
- 页面结构变化

**排查步骤**:
1. 检查Cookie是否最新
2. 查看logs目录下的日志文件
3. 尝试有头模式运行查看页面

### 3. 程序运行缓慢
**优化建议**:
- 减少target_count数量
- 调整延迟参数
- 使用headless模式
- 减少关键词数量

## 输出说明

### Excel文件位置
`output/拼多多商品数据_YYYYMMDD_HHMMSS.xlsx`

### 数据字段
- 商品ID
- 商品名称
- 价格(元)
- 销量
- 店铺名称
- 商品链接
- 商品图片
- 商品标签
- 商品分类
- 评论数
- 评分
- 采集时间

### 日志文件
- 普通日志: `logs/pdd_crawler_YYYYMMDD.log`
- 错误日志: `logs/error_YYYYMMDD.log`

## 安全建议

1. **仅供学习使用**: 请遵守网站服务条款
2. **控制频率**: 避免过于频繁的请求
3. **保护隐私**: 不要分享包含Cookie的配置文件
4. **定期更新**: Cookie可能过期，需要定期更新

## 命令速查

```bash
# 安装依赖
pip install -r requirements.txt

# 运行爬虫
python run_main.py

# 查看日志
tail -f logs/pdd_crawler_$(date +%Y%m%d).log

# 清理输出
rm -rf output/*.xlsx logs/*.log
```