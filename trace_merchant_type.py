#!/usr/bin/env python3
"""
追踪merchant_type数据流
"""

import sys
from pathlib import Path
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.core.api_response_monitor import APIResponseMonitor
from src.data.processor import DataProcessor

print("=== 追踪merchant_type数据流 ===\n")

# 创建测试数据，模拟API响应中的不同merchant_type
test_api_response = {
    "items": [
        {
            "item_type": 0,
            "item_data": {
                "goods_model": {
                    "goods_id": "123456",
                    "goods_name": "海尔476L冰箱 - 专营店商品", 
                    "price": 269900,  # 2699元
                    "merchant_type": 5,  # 专营店
                    "mall_name": "众沁电器专营店"
                }
            }
        },
        {
            "item_type": 0,
            "item_data": {
                "goods_model": {
                    "goods_id": "234567",
                    "goods_name": "海尔476L冰箱 - 企业店铺商品",
                    "price": 259900,  # 2599元
                    "merchant_type": 1,  # 企业店铺
                    "mall_name": "精选好物大家电"
                }
            }
        },
        {
            "item_type": 0,
            "item_data": {
                "goods_model": {
                    "goods_id": "345678",
                    "goods_name": "海尔476L冰箱 - 品牌直营店商品",
                    "price": 279900,  # 2799元
                    "merchant_type": 4,  # 品牌直营店
                    "mall_name": "海尔品牌直营店"
                }
            }
        },
        {
            "item_type": 0,
            "item_data": {
                "goods_model": {
                    "goods_id": "456789",
                    "goods_name": "海尔476L冰箱 - 普通店铺商品",
                    "price": 249900,  # 2499元
                    "merchant_type": 6,  # 普通店铺
                    "mall_name": "乐享优品家电购"
                }
            }
        },
        {
            "item_type": 0,
            "item_data": {
                "goods_model": {
                    "goods_id": "567890",
                    "goods_name": "海尔476L冰箱 - 官方旗舰店商品",
                    "price": 289900,  # 2899元
                    "merchant_type": 0,  # 官方旗舰店
                    "mall_name": "海尔官方旗舰店"
                }
            }
        }
    ]
}

async def trace_data_flow():
    """追踪数据流"""
    # 1. API监控器处理
    print("1. API监控器处理阶段:")
    monitor = APIResponseMonitor()
    monitor.current_keyword = "海尔476"
    
    # 模拟API响应处理
    goods_list = await monitor._extract_goods_data(test_api_response, {})
    
    print(f"   提取到 {len(goods_list)} 个商品")
    for i, goods in enumerate(goods_list):
        print(f"\n   商品 {i+1}:")
        print(f"     goods_name: {goods.get('goods_name', 'N/A')[:40]}...")
        print(f"     merchant_type: {goods.get('merchant_type', 'N/A')}")
        print(f"     merchant_type_name: {goods.get('merchant_type_name', 'N/A')}")
        print(f"     shop_name: {goods.get('shop_name', 'N/A')}")
    
    # 2. 数据处理器处理
    print("\n\n2. 数据处理器处理阶段:")
    processor = DataProcessor()
    
    # 处理每个商品
    processed_goods = await processor.process_goods_data(goods_list, "海尔476")
    
    print(f"   处理后得到 {len(processed_goods)} 个商品")
    for i, goods in enumerate(processed_goods):
        print(f"\n   商品 {i+1}:")
        print(f"     goods_name: {goods.get('goods_name', 'N/A')[:40]}...")
        print(f"     merchant_type: {goods.get('merchant_type', 'N/A')}")
        print(f"     merchant_type_name: {goods.get('merchant_type_name', 'N/A')}")
        print(f"     shop_name: {goods.get('shop_name', 'N/A')}")
    
    # 3. 检查数据变化
    print("\n\n3. 数据变化分析:")
    for i in range(len(goods_list)):
        original = goods_list[i]
        processed = processed_goods[i] if i < len(processed_goods) else {}
        
        print(f"\n   商品 {i+1}:")
        if original.get('merchant_type') != processed.get('merchant_type'):
            print(f"     ❌ merchant_type 改变: {original.get('merchant_type')} -> {processed.get('merchant_type')}")
        else:
            print(f"     ✅ merchant_type 保持: {original.get('merchant_type')}")
            
        if original.get('merchant_type_name') != processed.get('merchant_type_name'):
            print(f"     ❌ merchant_type_name 改变: {original.get('merchant_type_name')} -> {processed.get('merchant_type_name')}")
        else:
            print(f"     ✅ merchant_type_name 保持: {original.get('merchant_type_name')}")

# 运行追踪
print("开始追踪...\n")
asyncio.run(trace_data_flow())
print("\n=== 追踪完成 ===")