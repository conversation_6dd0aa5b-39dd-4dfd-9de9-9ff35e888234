import React from 'react'
import ThemeToggle from './ThemeToggle'
import './Header.css'

function Header({ theme, onThemeToggle }) {

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <div className="header-brand">
            <div className="logo">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <rect width="32" height="32" rx="8" fill="var(--primary)"/>
                <path d="M8 12h16M8 16h16M8 20h16" stroke="white" strokeWidth="2" strokeLinecap="round"/>
                <circle cx="24" cy="8" r="3" fill="#4caf50"/>
              </svg>
            </div>
            <h1 className="header-title">拼多多爬虫控制面板</h1>
          </div>
          
          <div className="header-actions">
            <ThemeToggle theme={theme} onToggle={onThemeToggle} />
            
            <div className="status-indicator">
              <span className="status-dot"></span>
              <span className="status-text">系统正常</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header