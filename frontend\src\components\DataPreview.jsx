import React, { useState, useEffect, useMemo } from 'react'
import './DataPreview.css'

function DataPreview({ data, loading, keywords }) {
  const [selectedItem, setSelectedItem] = useState(null)
  const [activeTab, setActiveTab] = useState(0)
  
  // Parse keywords from string
  const keywordList = useMemo(() => {
    if (!keywords) return []
    return keywords.split(',').map(k => k.trim()).filter(k => k)
  }, [keywords])
  
  // Generate color for each keyword
  const keywordColors = useMemo(() => {
    const colors = ['#1976d2', '#388e3c', '#d32f2f', '#f57c00', '#7b1fa2', '#0288d1', '#c2185b']
    const colorMap = {}
    keywordList.forEach((keyword, index) => {
      colorMap[keyword] = colors[index % colors.length]
    })
    return colorMap
  }, [keywordList])
  
  // Group data by keyword
  const groupedData = useMemo(() => {
    const groups = {}
    
    // Initialize groups for all keywords
    keywordList.forEach(keyword => {
      groups[keyword] = []
    })
    
    // Group products by keyword
    data.forEach(item => {
      const kw = item.keyword || 'unknown'
      if (groups[kw]) {
        groups[kw].push(item)
      }
    })
    
    return groups
  }, [data, keywordList])
  
  // Get tabs list - only keywords, no "all" tab
  const tabs = useMemo(() => {
    if (keywordList.length === 0) return ['默认']
    return keywordList
  }, [keywordList])
  
  // Get current tab data
  const currentTabData = useMemo(() => {
    const tabKey = tabs[activeTab] || tabs[0]
    return groupedData[tabKey] || []
  }, [tabs, activeTab, groupedData])
  
  const formatPrice = (price) => {
    return `¥${parseFloat(price).toFixed(2)}`
  }
  
  const formatSales = (sales) => {
    if (sales >= 10000) {
      return `${(sales / 10000).toFixed(1)}万`
    }
    return sales.toString()
  }
  
  const renderSkeleton = () => {
    return Array(5).fill(0).map((_, index) => (
      <tr key={`skeleton-${index}`} className="skeleton-row">
        <td><div className="skeleton skeleton-image"></div></td>
        <td><div className="skeleton skeleton-text"></div></td>
        <td><div className="skeleton skeleton-price"></div></td>
        <td><div className="skeleton skeleton-sales"></div></td>
        <td><div className="skeleton skeleton-shop"></div></td>
      </tr>
    ))
  }
  
  return (
    <div className="data-preview card">
      <div className="preview-header">
        <h2 className="preview-title">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
          </svg>
          数据预览
          {keywordList.length > 0 && tabs[activeTab] && (
            <span style={{
              marginLeft: '12px',
              fontSize: '16px',
              color: keywordColors[tabs[activeTab]] || '#666',
              fontWeight: 'normal'
            }}>
              - {tabs[activeTab]}
            </span>
          )}
        </h2>
        <div style={{ marginLeft: 'auto' }}>
          {currentTabData.length > 0 && (
            <span className="preview-count">共 {currentTabData.length} 条商品</span>
          )}
        </div>
      </div>
      
      {/* Tab Navigation - Show tabs when there are keywords */}
      {keywordList.length > 0 && (
        <div className="tab-navigation" style={{
          display: 'flex',
          borderBottom: '1px solid #e0e0e0',
          marginBottom: '20px',
          gap: '4px'
        }}>
          {tabs.map((tab, index) => {
            const isActive = activeTab === index
            const tabColor = keywordColors[tab] || '#666'
            const productCount = (groupedData[tab] || []).length
            
            return (
              <button
                key={tab}
                className={`tab-button ${isActive ? 'active' : ''}`}
                onClick={() => setActiveTab(index)}
                style={{
                  padding: '12px 24px',
                  border: 'none',
                  borderBottom: isActive ? `3px solid ${tabColor}` : '3px solid transparent',
                  background: 'none',
                  color: isActive ? tabColor : '#999',
                  fontSize: '14px',
                  fontWeight: isActive ? '600' : '400',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  position: 'relative'
                }}
              >
                <span>{tab}</span>
                <span style={{
                  marginLeft: '8px',
                  fontSize: '12px',
                  background: isActive ? tabColor : '#e0e0e0',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '10px',
                  fontWeight: 'normal'
                }}>
                  {productCount}
                </span>
              </button>
            )
          })}
        </div>
      )}
      
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th width="80">商品图片</th>
              <th>商品名称</th>
              <th width="100">价格</th>
              <th width="100">销量</th>
              <th width="150">店铺</th>
            </tr>
          </thead>
          <tbody>
            {loading && currentTabData.length === 0 ? (
              renderSkeleton()
            ) : currentTabData.length > 0 ? (
              currentTabData.map((item) => (
                <tr 
                  key={item.id} 
                  className="data-row"
                  onClick={() => setSelectedItem(item)}
                >
                  <td>
                    <div className="product-image">
                      <img 
                        src={item.image_url || 'https://via.placeholder.com/60x60'} 
                        alt={item.goods_name}
                        onError={(e) => {
                          e.target.src = 'https://via.placeholder.com/60x60'
                        }}
                      />
                    </div>
                  </td>
                  <td>
                    <div className="product-name" title={item.goods_name}>
                      {item.goods_name}
                    </div>
                  </td>
                  <td>
                    <div className="product-price">
                      {formatPrice(item.price)}
                    </div>
                  </td>
                  <td>
                    <div className="product-sales">
                      {formatSales(item.sales)}
                    </div>
                  </td>
                  <td>
                    <div className="shop-name" title={item.shop_name}>
                      {item.shop_name}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="empty-state">
                  <div className="empty-content">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                      <rect width="48" height="48" rx="24" fill="var(--secondary)"/>
                      <path d="M22 16h4v4h-4v-4zm0 8h4v12h-4V24z" fill="var(--text-secondary)"/>
                    </svg>
                    <p>暂无数据</p>
                    <span>开始爬取后，数据将实时显示在这里</span>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      {/* 商品详情模态框 */}
      {selectedItem && (
        <div className="modal-overlay" onClick={() => setSelectedItem(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>商品详情</h3>
              <button className="modal-close" onClick={() => setSelectedItem(null)}>
                ✕
              </button>
            </div>
            <div className="modal-body">
              <img 
                src={selectedItem.hd_url || selectedItem.image_url || 'https://via.placeholder.com/200x200'} 
                alt={selectedItem.goods_name}
                className="detail-image"
              />
              <div className="detail-info">
                <p><strong>商品名称：</strong>{selectedItem.goods_name}</p>
                <p><strong>商品ID：</strong>{selectedItem.goods_id || selectedItem.id}</p>
                <p><strong>价格：</strong>{formatPrice(selectedItem.price)}</p>
                <p><strong>销量：</strong>{formatSales(selectedItem.sales)}</p>
                <p><strong>店铺：</strong>{selectedItem.shop_name}</p>
                {selectedItem.brand_name && (
                  <p><strong>品牌：</strong>{selectedItem.brand_name}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DataPreview