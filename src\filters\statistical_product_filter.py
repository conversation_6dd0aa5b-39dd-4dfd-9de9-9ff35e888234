#!/usr/bin/env python3
"""
统计型商品筛选器

基于统计分析的智能商品筛选器，能够识别商品集合中的主要产品类型，
并自动过滤掉少数不相关的商品类型。
"""

import re
import logging
from typing import List, Dict, Tuple, Optional
from collections import Counter
from dataclasses import dataclass

from .product_filter import ProductFilter, MatchResult

logger = logging.getLogger(__name__)


@dataclass
class ProductTypeStats:
    """产品类型统计结果"""
    product_type: str
    count: int
    percentage: float
    product_indices: List[int]  # 该类型商品在原列表中的索引


class StatisticalProductFilter:
    """
    统计型商品筛选器
    
    功能：
    1. 两阶段筛选：先统计分析，后智能过滤
    2. 自动识别主要产品类型
    3. 过滤掉少数不相关的商品类型
    4. 保留原有的精确匹配功能
    """
    
    def __init__(self, config: Dict, brand_processor=None):
        """
        初始化统计型筛选器
        
        Args:
            config: 筛选器配置字典
            brand_processor: DataProcessor实例
        """
        # 创建基础筛选器
        self.base_filter = ProductFilter(config, brand_processor)
        
        # 统计相关配置
        statistical_config = config.get('statistical', {})
        self.enabled = statistical_config.get('enabled', True)
        self.min_sample_size = statistical_config.get('min_sample_size', 10)  # 最小样本量
        self.major_type_threshold = statistical_config.get('major_type_threshold', 0.7)  # 主要类型阈值（70%）
        self.minor_type_threshold = statistical_config.get('minor_type_threshold', 0.1)  # 少数类型阈值（10%）
        self.confidence_threshold = statistical_config.get('confidence_threshold', 0.8)  # 置信度阈值
        
        # 调试配置
        self.debug_enabled = config.get('debug', {}).get('enabled', False)
        self.log_statistics = statistical_config.get('log_statistics', True)
        
        logger.info(f"StatisticalProductFilter initialized: enabled={self.enabled}, "
                   f"major_threshold={self.major_type_threshold}, "
                   f"minor_threshold={self.minor_type_threshold}")
    
    def filter_products(self, products: List[Dict], search_keyword: str) -> List[Dict]:
        """
        统计型商品筛选
        
        Args:
            products: 商品列表
            search_keyword: 搜索关键词
            
        Returns:
            筛选后的商品列表
        """
        # 如果未启用统计筛选，直接使用基础筛选器
        if not self.enabled or not self.base_filter.enabled:
            return self.base_filter.filter_products(products, search_keyword)
        
        # 如果商品数量太少，不适合统计分析
        if len(products) < self.min_sample_size:
            if self.debug_enabled:
                logger.debug(f"样本量太小({len(products)}个)，使用基础筛选器")
            return self.base_filter.filter_products(products, search_keyword)
        
        # 第一阶段：使用基础筛选器进行初步筛选
        initially_filtered = self.base_filter.filter_products(products, search_keyword)
        
        # 如果初步筛选后商品太少，直接返回
        if len(initially_filtered) < self.min_sample_size:
            if self.debug_enabled:
                logger.debug(f"初步筛选后样本量太小({len(initially_filtered)}个)，直接返回")
            return initially_filtered
        
        # 第二阶段：统计分析和智能过滤
        return self._statistical_filter(initially_filtered, search_keyword)
    
    def _statistical_filter(self, products: List[Dict], search_keyword: str) -> List[Dict]:
        """
        基于统计的智能过滤
        
        Args:
            products: 初步筛选后的商品列表
            search_keyword: 搜索关键词
            
        Returns:
            统计过滤后的商品列表
        """
        # 分析商品类型分布
        type_stats = self._analyze_product_types(products)
        
        # 如果没有识别出任何产品类型，返回原列表
        if not type_stats:
            if self.debug_enabled:
                logger.debug("未识别出任何产品类型，返回原列表")
            return products
        
        # 记录统计信息
        if self.log_statistics:
            self._log_type_statistics(type_stats, len(products))
        
        # 确定主要产品类型
        major_types = self._identify_major_types(type_stats)
        
        # 如果没有单一主导类型（>70%），但有明显的主要类型（如60%的冰箱）
        # 仍然应该过滤掉少数类型
        if not major_types and type_stats:
            # 使用占比最高的类型作为主要类型
            if type_stats[0].percentage >= 0.5:  # 如果最高占比≥50%
                major_types = [type_stats[0].product_type]
                if self.debug_enabled:
                    logger.debug(f"没有超过{self.major_type_threshold:.0%}的类型，"
                               f"但{type_stats[0].product_type}占{type_stats[0].percentage:.1%}，"
                               f"设为主要类型")
        
        # 如果还是没有主要类型，返回原列表
        if not major_types:
            if self.debug_enabled:
                logger.debug("没有明显的主要产品类型，返回原列表")
            return products
        
        # 过滤商品：保留主要类型和未识别类型的商品
        filtered_products = self._filter_by_major_types(products, major_types, type_stats)
        
        # 记录过滤结果
        if self.log_statistics:
            filter_rate = (len(products) - len(filtered_products)) / len(products) if products else 0
            logger.info(f"统计过滤完成: {len(filtered_products)}/{len(products)} "
                       f"(过滤率{filter_rate:.1%})，主要类型: {major_types}")
        
        return filtered_products
    
    def _analyze_product_types(self, products: List[Dict]) -> List[ProductTypeStats]:
        """
        分析商品类型分布
        
        Args:
            products: 商品列表
            
        Returns:
            产品类型统计列表
        """
        type_counter = Counter()
        type_indices = {}  # 记录每种类型的商品索引
        
        for idx, product in enumerate(products):
            # 获取商品的产品类型（从match_components中获取）
            match_components = product.get('match_components', {})
            product_components = match_components.get('product_components', {})
            product_type = product_components.get('product_type')
            
            # 如果没有从match_components获取到，尝试直接识别
            if not product_type:
                product_type = self._extract_product_type(product)
            
            if product_type:
                type_counter[product_type] += 1
                if product_type not in type_indices:
                    type_indices[product_type] = []
                type_indices[product_type].append(idx)
        
        # 计算统计信息
        total_typed = sum(type_counter.values())
        type_stats = []
        
        for product_type, count in type_counter.most_common():
            percentage = count / len(products) if products else 0
            stats = ProductTypeStats(
                product_type=product_type,
                count=count,
                percentage=percentage,
                product_indices=type_indices[product_type]
            )
            type_stats.append(stats)
        
        return type_stats
    
    def _extract_product_type(self, product: Dict) -> Optional[str]:
        """
        从商品信息中提取产品类型
        
        Args:
            product: 商品信息
            
        Returns:
            产品类型或None
        """
        goods_name = product.get('goods_name', '')
        
        # 使用基础筛选器的产品类型模式
        for product_type, pattern in self.base_filter.product_patterns.items():
            if pattern.search(goods_name):
                # 检查是否为配件
                is_accessory = any(keyword in goods_name 
                                 for keyword in self.base_filter.accessory_keywords)
                if not is_accessory:
                    return product_type
        
        return None
    
    def _identify_major_types(self, type_stats: List[ProductTypeStats]) -> List[str]:
        """
        识别主要产品类型
        
        Args:
            type_stats: 产品类型统计列表
            
        Returns:
            主要产品类型列表
        """
        major_types = []
        
        for stats in type_stats:
            # 如果某种类型占比超过阈值，认为是主要类型
            if stats.percentage >= self.major_type_threshold:
                major_types.append(stats.product_type)
                if self.debug_enabled:
                    logger.debug(f"识别主要产品类型: {stats.product_type} "
                               f"({stats.count}个, {stats.percentage:.1%})")
        
        # 如果没有单一主导类型，但有几个类型合计占比很高
        if not major_types and len(type_stats) >= 2:
            cumulative_percentage = 0
            for stats in type_stats:
                cumulative_percentage += stats.percentage
                major_types.append(stats.product_type)
                
                # 如果累计占比达到置信度阈值，停止添加
                if cumulative_percentage >= self.confidence_threshold:
                    if self.debug_enabled:
                        logger.debug(f"累计占比达到{cumulative_percentage:.1%}，"
                                   f"主要类型: {major_types}")
                    break
        
        return major_types
    
    def _filter_by_major_types(self, products: List[Dict], 
                              major_types: List[str], 
                              type_stats: List[ProductTypeStats]) -> List[Dict]:
        """
        根据主要类型过滤商品
        
        Args:
            products: 商品列表
            major_types: 主要产品类型列表
            type_stats: 产品类型统计列表
            
        Returns:
            过滤后的商品列表
        """
        # 创建类型到统计信息的映射
        type_stats_dict = {stats.product_type: stats for stats in type_stats}
        
        # 获取所有主要类型的商品索引
        major_indices = set()
        for major_type in major_types:
            if major_type in type_stats_dict:
                major_indices.update(type_stats_dict[major_type].product_indices)
        
        filtered_products = []
        
        for idx, product in enumerate(products):
            # 如果是主要类型的商品，保留
            if idx in major_indices:
                filtered_products.append(product)
                continue
            
            # 获取商品类型
            match_components = product.get('match_components', {})
            product_components = match_components.get('product_components', {})
            product_type = product_components.get('product_type')
            
            if not product_type:
                product_type = self._extract_product_type(product)
            
            # 如果商品没有识别出类型，保留（可能是特殊商品）
            if not product_type:
                filtered_products.append(product)
                if self.debug_enabled:
                    logger.debug(f"保留未识别类型的商品: {product.get('goods_name', '')[:50]}...")
                continue
            
            # 如果是少数类型（占比低于阈值），过滤掉
            if product_type in type_stats_dict:
                stats = type_stats_dict[product_type]
                if stats.percentage < self.minor_type_threshold:
                    if self.debug_enabled:
                        logger.debug(f"过滤少数类型商品: {product_type} "
                                   f"({stats.percentage:.1%}): "
                                   f"{product.get('goods_name', '')[:50]}...")
                    continue
            
            # 其他情况保留
            filtered_products.append(product)
        
        return filtered_products
    
    def _log_type_statistics(self, type_stats: List[ProductTypeStats], total_count: int):
        """记录产品类型统计信息"""
        logger.info(f"产品类型分布统计 (总计{total_count}个商品):")
        for stats in type_stats:
            logger.info(f"  - {stats.product_type}: {stats.count}个 ({stats.percentage:.1%})")
        
        # 计算未识别类型的数量
        identified_count = sum(stats.count for stats in type_stats)
        unidentified_count = total_count - identified_count
        if unidentified_count > 0:
            unidentified_percentage = unidentified_count / total_count
            logger.info(f"  - 未识别类型: {unidentified_count}个 ({unidentified_percentage:.1%})")


def create_statistical_product_filter(config: Dict, brand_processor=None) -> StatisticalProductFilter:
    """
    创建统计型商品筛选器实例
    
    Args:
        config: 配置字典
        brand_processor: DataProcessor实例
        
    Returns:
        StatisticalProductFilter实例
    """
    return StatisticalProductFilter(config, brand_processor)