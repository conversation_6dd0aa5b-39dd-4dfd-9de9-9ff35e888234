// 内存缓存工具
class MemoryCache {
  constructor(options = {}) {
    this.cache = new Map()
    this.maxSize = options.maxSize || 100
    this.ttl = options.ttl || 5 * 60 * 1000 // 默认5分钟
    this.onEvict = options.onEvict || (() => {})
  }

  // 设置缓存
  set(key, value, customTTL) {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      const firstKey = this.cache.keys().next().value
      this.delete(firstKey)
    }

    const expiresAt = Date.now() + (customTTL || this.ttl)
    this.cache.set(key, {
      value,
      expiresAt,
      hits: 0,
      createdAt: Date.now()
    })
  }

  // 获取缓存
  get(key) {
    const item = this.cache.get(key)
    
    if (!item) {
      return undefined
    }

    // 检查是否过期
    if (Date.now() > item.expiresAt) {
      this.delete(key)
      return undefined
    }

    // 更新命中次数
    item.hits++
    
    // LRU: 将访问的项移到末尾
    this.cache.delete(key)
    this.cache.set(key, item)
    
    return item.value
  }

  // 删除缓存
  delete(key) {
    const item = this.cache.get(key)
    if (item) {
      this.cache.delete(key)
      this.onEvict(key, item.value)
    }
  }

  // 清空缓存
  clear() {
    this.cache.forEach((item, key) => {
      this.onEvict(key, item.value)
    })
    this.cache.clear()
  }

  // 获取缓存大小
  size() {
    return this.cache.size
  }

  // 获取缓存统计
  getStats() {
    const stats = {
      size: this.cache.size,
      maxSize: this.maxSize,
      items: []
    }

    this.cache.forEach((item, key) => {
      stats.items.push({
        key,
        hits: item.hits,
        age: Date.now() - item.createdAt,
        ttl: item.expiresAt - Date.now()
      })
    })

    // 按命中次数排序
    stats.items.sort((a, b) => b.hits - a.hits)

    return stats
  }

  // 清理过期项
  prune() {
    const now = Date.now()
    const keysToDelete = []

    this.cache.forEach((item, key) => {
      if (now > item.expiresAt) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.delete(key))
    
    return keysToDelete.length
  }
}

// 创建不同用途的缓存实例
export const apiCache = new MemoryCache({
  maxSize: 50,
  ttl: 2 * 60 * 1000 // API缓存2分钟
})

export const imageCache = new MemoryCache({
  maxSize: 200,
  ttl: 30 * 60 * 1000 // 图片缓存30分钟
})

export const dataCache = new MemoryCache({
  maxSize: 100,
  ttl: 5 * 60 * 1000 // 数据缓存5分钟
})

// React Hook for caching
import { useCallback, useEffect } from 'react'

export const useCache = (cacheInstance = dataCache) => {
  // 定期清理过期项
  useEffect(() => {
    const interval = setInterval(() => {
      cacheInstance.prune()
    }, 60000) // 每分钟清理一次

    return () => clearInterval(interval)
  }, [cacheInstance])

  const getCached = useCallback(async (key, fetcher, ttl) => {
    // 先检查缓存
    const cached = cacheInstance.get(key)
    if (cached !== undefined) {
      return cached
    }

    // 缓存未命中，执行获取函数
    try {
      const result = await fetcher()
      cacheInstance.set(key, result, ttl)
      return result
    } catch (error) {
      // 如果获取失败，尝试返回过期的缓存
      const expiredItem = cacheInstance.cache.get(key)
      if (expiredItem) {
        console.warn('使用过期缓存:', key)
        return expiredItem.value
      }
      throw error
    }
  }, [cacheInstance])

  const invalidate = useCallback((key) => {
    if (key) {
      cacheInstance.delete(key)
    } else {
      cacheInstance.clear()
    }
  }, [cacheInstance])

  return {
    getCached,
    invalidate,
    getStats: () => cacheInstance.getStats()
  }
}

// 缓存键生成器
export const createCacheKey = (...args) => {
  return args.map(arg => {
    if (typeof arg === 'object') {
      return JSON.stringify(arg)
    }
    return String(arg)
  }).join(':')
}