// PDD签名算法模拟
// 注意：这是一个示例实现，实际的签名算法需要通过逆向工程获取

function generatePDDSign(params) {
    const { api, params: requestParams, timestamp } = params;
    
    // 构建签名基础字符串
    let signStr = api + '_' + timestamp;
    
    // 按键排序并拼接参数
    const sortedKeys = Object.keys(requestParams).sort();
    for (const key of sortedKeys) {
        const value = requestParams[key];
        if (value !== null && value !== undefined && value !== '') {
            signStr += '_' + key + '_' + value;
        }
    }
    
    // 添加密钥
    const secret = 'pdd_mobile_secret_2023';
    signStr += '_' + secret;
    
    // 生成签名
    const sign = md5(signStr);
    
    // 生成anti_content
    const antiContent = generateAntiContent();
    
    return {
        sign: sign,
        anti_content: antiContent
    };
}

function generateAntiContent() {
    const deviceInfo = {
        device_id: generateDeviceId(),
        platform: 'android',
        app_version: '6.89.0',
        os_version: '12',
        brand: 'Xiaomi',
        model: 'Mi 11',
        screen: '1080x2400',
        dpi: 440,
        cpu: 'arm64-v8a',
        memory: '8192',
        storage: '128000'
    };
    
    return btoa(JSON.stringify(deviceInfo));
}

function generateDeviceId() {
    const chars = '0123456789abcdef';
    let deviceId = '';
    for (let i = 0; i < 32; i++) {
        deviceId += chars[Math.floor(Math.random() * chars.length)];
    }
    return deviceId;
}

// 简单的MD5实现（生产环境应使用标准库）
function md5(str) {
    // 这里应该是完整的MD5实现
    // 为了示例，返回一个模拟的哈希值
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash).toString(16).padStart(32, '0');
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { generatePDDSign };
}