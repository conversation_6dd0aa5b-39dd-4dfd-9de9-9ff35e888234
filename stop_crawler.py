#!/usr/bin/env python3
"""
拼多多爬虫停止脚本
用于停止正在运行的前端和后端服务
"""

import os
import sys
import platform
import subprocess
import psutil
import signal
from typing import List, Tuple

# ANSI颜色代码（Windows 10+支持）
if platform.system() == 'Windows':
    os.system('color')  # 启用Windows终端的ANSI支持

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header():
    """打印头部信息"""
    print(f"\n{Colors.HEADER}{'='*65}{Colors.ENDC}")
    print(f"{Colors.HEADER}║              拼多多爬虫服务停止工具                           ║{Colors.ENDC}")
    print(f"{Colors.HEADER}{'='*65}{Colors.ENDC}\n")

def check_psutil():
    """检查并安装psutil库"""
    try:
        import psutil
        return True
    except ImportError:
        print(f"{Colors.WARNING}正在安装psutil库...{Colors.ENDC}")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'psutil'], 
                              capture_output=True)
        if result.returncode == 0:
            print(f"{Colors.OKGREEN}✅ psutil安装成功{Colors.ENDC}")
            return True
        else:
            print(f"{Colors.FAIL}❌ psutil安装失败，请手动安装: pip install psutil{Colors.ENDC}")
            return False

def find_processes_by_port(port: int) -> List[Tuple[int, str]]:
    """查找占用指定端口的进程"""
    processes = []
    try:
        for conn in psutil.net_connections():
            if conn.laddr and conn.laddr.port == port and conn.status == 'LISTEN':
                try:
                    process = psutil.Process(conn.pid)
                    processes.append((conn.pid, process.name()))
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
    except psutil.AccessDenied:
        print(f"{Colors.WARNING}⚠️ 需要管理员权限来查看所有进程{Colors.ENDC}")
    return processes

def find_processes_by_pattern(patterns: List[str]) -> List[Tuple[int, str, str]]:
    """根据命令行模式查找进程"""
    processes = []
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                for pattern in patterns:
                    if pattern in cmdline:
                        processes.append((
                            proc.info['pid'],
                            proc.info['name'],
                            cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                        ))
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
    except Exception as e:
        print(f"{Colors.WARNING}搜索进程时出错: {e}{Colors.ENDC}")
    return processes

def kill_process(pid: int, name: str) -> bool:
    """终止指定进程"""
    try:
        process = psutil.Process(pid)
        process.terminate()
        
        # 等待进程结束
        try:
            process.wait(timeout=5)
        except psutil.TimeoutExpired:
            # 如果进程没有响应terminate，使用kill
            process.kill()
            process.wait(timeout=2)
        
        print(f"{Colors.OKGREEN}✅ 已停止进程: {name} (PID: {pid}){Colors.ENDC}")
        return True
    except psutil.NoSuchProcess:
        print(f"{Colors.WARNING}进程已经不存在: PID {pid}{Colors.ENDC}")
        return True
    except psutil.AccessDenied:
        print(f"{Colors.FAIL}❌ 权限不足，无法停止进程: {name} (PID: {pid}){Colors.ENDC}")
        if platform.system() == 'Windows':
            print(f"{Colors.WARNING}提示: 请以管理员身份运行此脚本{Colors.ENDC}")
        return False
    except Exception as e:
        print(f"{Colors.FAIL}❌ 停止进程失败: {name} (PID: {pid}) - {e}{Colors.ENDC}")
        return False

def stop_services():
    """停止所有相关服务"""
    stopped_count = 0
    
    # 1. 查找并停止占用端口的进程
    print(f"{Colors.OKBLUE}[1/3] 查找占用端口的进程...{Colors.ENDC}")
    
    # 检查后端端口 8000
    backend_processes = find_processes_by_port(8000)
    if backend_processes:
        print(f"找到 {len(backend_processes)} 个占用端口8000的进程")
        for pid, name in backend_processes:
            if kill_process(pid, name):
                stopped_count += 1
    else:
        print("端口8000未被占用")
    
    # 检查前端端口 5173
    frontend_processes = find_processes_by_port(5173)
    if frontend_processes:
        print(f"找到 {len(frontend_processes)} 个占用端口5173的进程")
        for pid, name in frontend_processes:
            if kill_process(pid, name):
                stopped_count += 1
    else:
        print("端口5173未被占用")
    
    # 2. 根据命令行特征查找进程
    print(f"\n{Colors.OKBLUE}[2/3] 查找爬虫相关进程...{Colors.ENDC}")
    
    patterns = [
        'api_server.py',      # 后端API服务器
        'npm run dev',        # 前端开发服务器
        'vite',              # Vite开发服务器
        'start_crawler.py'    # 启动脚本本身
    ]
    
    related_processes = find_processes_by_pattern(patterns)
    if related_processes:
        print(f"找到 {len(related_processes)} 个相关进程")
        for pid, name, cmdline in related_processes:
            print(f"  - {name} (PID: {pid})")
            print(f"    命令: {cmdline}")
            if kill_process(pid, name):
                stopped_count += 1
    else:
        print("未找到相关进程")
    
    # 3. 清理Node.js进程（Windows特定）
    if platform.system() == 'Windows':
        print(f"\n{Colors.OKBLUE}[3/3] 清理Node.js进程...{Colors.ENDC}")
        node_processes = find_processes_by_pattern(['node.exe'])
        if node_processes:
            print(f"找到 {len(node_processes)} 个Node.js进程")
            print(f"{Colors.WARNING}注意: 这可能会影响其他Node.js应用{Colors.ENDC}")
            response = input("是否停止这些进程? (y/N): ")
            if response.lower() == 'y':
                for pid, name, cmdline in node_processes:
                    if 'vite' in cmdline or 'frontend' in cmdline:
                        if kill_process(pid, name):
                            stopped_count += 1
    
    return stopped_count

def main():
    """主函数"""
    print_header()
    
    # 检查psutil
    if not check_psutil():
        input("\n按回车键退出...")
        return 1
    
    # 重新导入psutil
    global psutil
    import psutil
    
    print(f"{Colors.OKCYAN}开始停止爬虫服务...{Colors.ENDC}\n")
    
    # 停止服务
    stopped_count = stop_services()
    
    # 打印结果
    print(f"\n{Colors.HEADER}{'='*65}{Colors.ENDC}")
    if stopped_count > 0:
        print(f"{Colors.OKGREEN}✅ 成功停止 {stopped_count} 个进程{Colors.ENDC}")
    else:
        print(f"{Colors.WARNING}⚠️ 没有找到需要停止的进程{Colors.ENDC}")
    print(f"{Colors.HEADER}{'='*65}{Colors.ENDC}")
    
    input("\n按回车键退出...")
    return 0

if __name__ == "__main__":
    sys.exit(main())