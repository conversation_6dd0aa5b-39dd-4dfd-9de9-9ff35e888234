import React, { useState } from 'react'
import './SearchConfigEnhanced.css'

export default function SearchConfigEnhanced({ config, setConfig, disabled }) {
  const [isFocused, setIsFocused] = useState(false)

  const handleChange = (field, value) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="search-config-enhanced">
      {/* 标题部分 */}
      <div className="config-header">
        <div className="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </div>
        <h2 className="config-title">搜索配置</h2>
      </div>

      {/* 表单内容 */}
      <div className="config-body">
        {/* 关键词输入 */}
        <div className="form-field">
          <label className="field-label">
            搜索关键词
            <span className="required-mark">*</span>
          </label>
          <div className={`input-container ${isFocused ? 'focused' : ''} ${config.keyword ? 'has-value' : ''}`}>
            <div className="input-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M9 17A8 8 0 109 1a8 8 0 000 16z" stroke="currentColor" strokeWidth="1.5"/>
                <path d="M19 19L14.65 14.65" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
              </svg>
            </div>
            <input
              type="text"
              className="enhanced-input"
              placeholder="输入商品名称、品牌或类别"
              value={config.keyword}
              onChange={(e) => handleChange('keyword', e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              disabled={disabled}
            />
            {config.keyword && (
              <button 
                className="clear-button"
                onClick={() => handleChange('keyword', '')}
                disabled={disabled}
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            )}
          </div>
          <p className="field-hint">例如：手机、冰箱、运动鞋等</p>
        </div>

        {/* 数量配置 */}
        <div className="config-grid">
          <div className="form-field">
            <label className="field-label">爬取页数</label>
            <div className="number-input-wrapper">
              <button 
                className="number-button minus"
                onClick={() => handleChange('maxPages', Math.max(1, config.maxPages - 1))}
                disabled={disabled || config.maxPages <= 1}
              >
                −
              </button>
              <input
                type="number"
                className="number-input"
                min="1"
                max="100"
                value={config.maxPages}
                onChange={(e) => handleChange('maxPages', parseInt(e.target.value) || 1)}
                disabled={disabled}
              />
              <button 
                className="number-button plus"
                onClick={() => handleChange('maxPages', Math.min(100, config.maxPages + 1))}
                disabled={disabled || config.maxPages >= 100}
              >
                +
              </button>
            </div>
            <p className="field-hint">每页约20-40个商品</p>
          </div>

          <div className="form-field">
            <label className="field-label">目标数量</label>
            <div className="number-input-wrapper">
              <button 
                className="number-button minus"
                onClick={() => handleChange('targetCount', Math.max(1, config.targetCount - 10))}
                disabled={disabled || config.targetCount <= 1}
              >
                −
              </button>
              <input
                type="number"
                className="number-input"
                min="1"
                max="10000"
                value={config.targetCount}
                onChange={(e) => handleChange('targetCount', parseInt(e.target.value) || 1)}
                disabled={disabled}
              />
              <button 
                className="number-button plus"
                onClick={() => handleChange('targetCount', Math.min(10000, config.targetCount + 10))}
                disabled={disabled || config.targetCount >= 10000}
              >
                +
              </button>
            </div>
            <p className="field-hint">期望采集的商品数</p>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="stats-container">
          <div className="stat-card">
            <div className="stat-icon time">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <circle cx="10" cy="10" r="9" stroke="currentColor" strokeWidth="1.5"/>
                <path d="M10 6V10L13 13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
              </svg>
            </div>
            <div className="stat-content">
              <span className="stat-label">预计耗时</span>
              <span className="stat-value">~{Math.ceil(config.maxPages * 3)} 分钟</span>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon items">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <rect x="3" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                <rect x="11" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                <rect x="3" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                <rect x="11" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
              </svg>
            </div>
            <div className="stat-content">
              <span className="stat-label">预计商品数</span>
              <span className="stat-value">{config.maxPages * 30} 个</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}