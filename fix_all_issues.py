#!/usr/bin/env python3
"""
修复所有数据处理问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

print("开始修复所有问题...")
print("=" * 80)

# 1. 修复筛选阈值，提高到0.8以更严格过滤
print("\n1. 修复筛选阈值...")
import yaml

config_file = "config/settings.yaml"
with open(config_file, 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 提高匹配阈值
config['product_filter']['match_threshold'] = 0.8
config['product_filter']['strict_mode'] = True

with open(config_file, 'w', encoding='utf-8') as f:
    yaml.dump(config, f, allow_unicode=True, default_flow_style=False)

print("✅ 筛选阈值已提高到0.8，启用严格模式")

# 2. 检查商家类型映射问题
print("\n2. 检查商家类型映射...")
from src.data.processor import DataProcessor

processor = DataProcessor()
print(f"商家类型映射表: {processor.merchant_type_mapping}")

# 3. 修复主程序中的CSV导出
print("\n3. 已添加CSV导出功能")

# 4. 调试品牌识别逻辑
print("\n4. 调试品牌识别逻辑...")
test_names = [
    "海尔统帅BCD-476WGLTDD9G9U1十字开门一级变频家用无霜纤薄冰箱",
    "海尔476L十字对开门黑金净化一级双变频无霜冰箱BCD-476WGHTDEDXM"
]

for name in test_names:
    # 使用processor的品牌识别方法
    brand = processor._extract_brand_name_from_goods_name(name, "")
    print(f"商品名: {name[:50]}...")
    print(f"识别的品牌: {brand}")
    print("-" * 40)

print("\n修复建议：")
print("1. 筛选阈值已提高到0.8，应该能更好地过滤子品牌")
print("2. 需要修复商家类型的数据传递问题") 
print("3. 需要修复百亿补贴字段的数据传递问题")
print("4. 品牌识别逻辑需要调整，避免将'海尔统帅'识别为'海尔'")

print("\n下一步：重新运行爬虫测试修复效果")