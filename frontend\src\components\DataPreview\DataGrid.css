.data-grid-container {
  background: #f5f5f5;
  border-radius: 12px;
  padding: 16px 0 0 0;
  position: relative;
}

/* 网格项 */
.grid-item {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.grid-item:hover .product-name {
  color: #1890ff;
  text-decoration: underline;
}

/* 添加链接图标提示 */
.grid-item::after {
  content: "🔗";
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 16px;
  opacity: 0;
  transition: opacity 0.2s;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grid-item:hover::after {
  opacity: 1;
}

.grid-item.selected {
  box-shadow: 0 0 0 2px #1890ff;
}

.grid-item.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 340px;
}

.loading-skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e8e8e8 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 复选框 */
.grid-item-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.grid-item:hover .grid-item-checkbox,
.grid-item.selected .grid-item-checkbox {
  opacity: 1;
}

.grid-item-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin: 0;
}

/* 图片区域 */
.grid-item-image {
  position: relative;
  width: 100%;
  height: 240px;
  overflow: hidden;
  background: #f5f5f5;
}

.grid-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.grid-item:hover .grid-item-image img {
  transform: scale(1.05);
}

.coupon-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff4d4f;
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

/* 内容区域 */
.grid-item-content {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.grid-item-title {
  font-size: 14px;
  line-height: 1.4;
  color: #262626;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  min-height: 40px;
}

/* 价格 */
.grid-item-price {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #ff4d4f;
}

.original-price {
  font-size: 14px;
  color: #8c8c8c;
  text-decoration: line-through;
}

/* 信息栏 */
.grid-item-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.info-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 店铺 */
.grid-item-shop {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #595959;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.grid-item-shop svg {
  flex-shrink: 0;
  opacity: 0.6;
}

.grid-item-shop span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 标签 */
.grid-item-tags {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.grid-item-tags .tag {
  font-size: 11px;
  padding: 2px 6px;
  background: #f0f0f0;
  color: #595959;
  border-radius: 4px;
  white-space: nowrap;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #8c8c8c;
}

.empty-icon {
  margin-bottom: 24px;
  opacity: 0.3;
}

.empty-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

/* 滚动条 */
.data-grid-container ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.data-grid-container ::-webkit-scrollbar-track {
  background: transparent;
}

.data-grid-container ::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 3px;
}

.data-grid-container ::-webkit-scrollbar-thumb:hover {
  background: #8c8c8c;
}