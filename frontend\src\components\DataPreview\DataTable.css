.data-table-container {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 表头 */
.table-header-row {
  display: flex;
  align-items: center;
  height: 56px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
  font-weight: 500;
  color: #262626;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header-cell {
  display: flex;
  align-items: center;
  gap: 4px;
  user-select: none;
}

.table-header-cell.sortable {
  cursor: pointer;
  transition: color 0.2s;
}

.table-header-cell.sortable:hover {
  color: #1890ff;
}

.table-header-cell.sorted {
  color: #1890ff;
}

.sort-icon {
  font-size: 12px;
  margin-left: 4px;
}

/* 表格列宽 */
.checkbox-cell {
  width: 48px;
  flex-shrink: 0;
}

.image-cell {
  width: 80px;
  flex-shrink: 0;
}

.name-cell {
  flex: 1;
  min-width: 200px;
  padding-right: 16px;
}

.price-cell {
  width: 120px;
  flex-shrink: 0;
}

.sales-cell {
  width: 100px;
  flex-shrink: 0;
}

.shop-cell {
  width: 180px;
  flex-shrink: 0;
}

/* 表格行 */
.table-body {
  position: relative;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.table-row:hover {
  background-color: #fafafa;
  transform: translateX(2px);
  box-shadow: -2px 0 0 0 #1890ff;
}

.table-row:hover .product-name {
  color: #1890ff;
  text-decoration: underline;
}

.table-row.selected {
  background-color: #e6f4ff;
}

.table-row.selected:hover {
  background-color: #d0e8ff;
}

.table-cell {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 8px 0;
}

/* 图片 */
.image-cell img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  background: #f5f5f5;
}

/* 商品名称 */
.product-name {
  font-size: 14px;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
}

.product-brand {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

/* 价格 */
.product-price {
  font-size: 16px;
  font-weight: 500;
  color: #ff4d4f;
}

.coupon-price {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 2px;
}

/* 销量 */
.product-sales {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.sales-number {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.sales-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 店铺 */
.shop-name {
  font-size: 14px;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shop-type {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 复选框 */
.checkbox-cell input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 加载状态 */
.loading-row {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  font-size: 14px;
}

.loading-cell {
  padding: 20px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #8c8c8c;
}

.empty-icon {
  margin-bottom: 24px;
  opacity: 0.3;
}

.empty-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

/* 滚动条样式 */
.data-table-container ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.data-table-container ::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.data-table-container ::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 3px;
}

.data-table-container ::-webkit-scrollbar-thumb:hover {
  background: #8c8c8c;
}