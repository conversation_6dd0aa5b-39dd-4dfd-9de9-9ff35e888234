"""
浏览器启动器 - 基于MediaCrawler实现
用于检测和启动用户的Chrome/Edge浏览器
支持Windows、macOS和Linux系统
"""
import os
import sys
import time
import socket
import platform
import subprocess
import asyncio
from typing import List, Optional, Tuple
from loguru import logger


class BrowserLauncher:
    """
    浏览器启动器，用于检测和启动用户的Chrome/Edge浏览器
    支持Windows、macOS和Linux系统
    """
    
    def __init__(self):
        self.system = platform.system()
        self.browser_process = None
        self.debug_port = None
        
    def detect_browser_paths(self) -> List[str]:
        """
        检测系统中可用的浏览器路径
        返回按优先级排序的浏览器路径列表
        """
        paths = []
        
        if self.system == "Windows":
            # Windows下的常见Chrome/Edge安装路径
            possible_paths = [
                # Chrome标准安装路径
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",

                # Chrome用户目录安装
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                os.path.expanduser(r"~\AppData\Local\Google\Chrome Beta\Application\chrome.exe"),
                os.path.expanduser(r"~\AppData\Local\Google\Chrome Dev\Application\chrome.exe"),
                os.path.expanduser(r"~\AppData\Local\Google\Chrome SxS\Application\chrome.exe"),  # Canary

                # Edge标准安装路径
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",

                # Edge用户目录安装
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge\Application\msedge.exe"),
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge Beta\Application\msedge.exe"),
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge Dev\Application\msedge.exe"),
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge SxS\Application\msedge.exe"),  # Canary

                # 便携版常见路径
                r"C:\PortableApps\GoogleChromePortable\App\Chrome-bin\chrome.exe",
                r"D:\PortableApps\GoogleChromePortable\App\Chrome-bin\chrome.exe",
                r"E:\PortableApps\GoogleChromePortable\App\Chrome-bin\chrome.exe",

                # 自定义安装常见路径
                r"D:\Google\Chrome\Application\chrome.exe",
                r"E:\Google\Chrome\Application\chrome.exe",
                r"D:\Chrome\chrome.exe",
                r"E:\Chrome\chrome.exe",
                r"D:\Software\Chrome\chrome.exe",
                r"E:\Software\Chrome\chrome.exe",

                # 企业部署常见路径
                r"C:\Apps\Chrome\chrome.exe",
                r"C:\Software\Google\Chrome\Application\chrome.exe",
                r"D:\Apps\Chrome\chrome.exe",
            ]
        elif self.system == "Darwin":  # macOS
            # macOS下的常见Chrome/Edge安装路径
            possible_paths = [
                # Chrome路径
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta",
                "/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev",
                "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
                # Edge路径
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
                "/Applications/Microsoft Edge Beta.app/Contents/MacOS/Microsoft Edge Beta",
                "/Applications/Microsoft Edge Dev.app/Contents/MacOS/Microsoft Edge Dev",
                "/Applications/Microsoft Edge Canary.app/Contents/MacOS/Microsoft Edge Canary",
            ]
        else:
            # Linux等其他系统
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/google-chrome-beta",
                "/usr/bin/google-chrome-unstable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/snap/bin/chromium",
                "/usr/bin/microsoft-edge",
                "/usr/bin/microsoft-edge-stable",
                "/usr/bin/microsoft-edge-beta",
                "/usr/bin/microsoft-edge-dev",
            ]
        
        # 检查路径是否存在且可执行
        for path in possible_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                paths.append(path)

        # 如果标准路径都没找到，尝试智能搜索
        if not paths:
            smart_paths = self._smart_browser_search()
            paths.extend(smart_paths)

        return paths

    def _smart_browser_search(self) -> List[str]:
        """
        智能搜索浏览器路径
        在标准路径检测失败时使用
        """
        found_paths = []

        if self.system == "Windows":
            # 搜索所有驱动器的常见目录
            drives = ['C:', 'D:', 'E:', 'F:']
            search_patterns = [
                r"\Google\Chrome\Application\chrome.exe",
                r"\Chrome\chrome.exe",
                r"\Software\Chrome\chrome.exe",
                r"\Apps\Chrome\chrome.exe",
                r"\PortableApps\GoogleChromePortable\App\Chrome-bin\chrome.exe",
                r"\Microsoft\Edge\Application\msedge.exe",
                r"\Edge\msedge.exe",
            ]

            for drive in drives:
                if os.path.exists(drive):
                    for pattern in search_patterns:
                        full_path = drive + pattern
                        if os.path.isfile(full_path) and os.access(full_path, os.X_OK):
                            found_paths.append(full_path)

            # 搜索注册表（Windows特有）
            registry_paths = self._search_windows_registry()
            found_paths.extend(registry_paths)

        elif self.system == "Darwin":  # macOS
            # 搜索常见的应用程序目录
            search_dirs = [
                "/Applications",
                os.path.expanduser("~/Applications"),
                "/System/Applications",
            ]

            for search_dir in search_dirs:
                if os.path.exists(search_dir):
                    for app_name in ["Google Chrome.app", "Microsoft Edge.app"]:
                        app_path = os.path.join(search_dir, app_name)
                        if os.path.exists(app_path):
                            if "Chrome" in app_name:
                                exe_path = os.path.join(app_path, "Contents/MacOS/Google Chrome")
                            else:
                                exe_path = os.path.join(app_path, "Contents/MacOS/Microsoft Edge")

                            if os.path.isfile(exe_path) and os.access(exe_path, os.X_OK):
                                found_paths.append(exe_path)

        else:  # Linux
            # 使用which命令搜索
            import subprocess
            commands = [
                "google-chrome", "google-chrome-stable", "chromium-browser",
                "chromium", "microsoft-edge", "microsoft-edge-stable"
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(["which", cmd], capture_output=True, text=True)
                    if result.returncode == 0 and result.stdout.strip():
                        path = result.stdout.strip()
                        if os.path.isfile(path) and os.access(path, os.X_OK):
                            found_paths.append(path)
                except:
                    continue

        return found_paths

    def _search_windows_registry(self) -> List[str]:
        """
        搜索Windows注册表中的浏览器安装信息
        """
        found_paths = []

        if self.system != "Windows":
            return found_paths

        try:
            import winreg

            # Chrome注册表路径
            chrome_keys = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe",
                r"SOFTWARE\Google\Chrome\BLBeacon",
            ]

            # Edge注册表路径
            edge_keys = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\msedge.exe",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\App Paths\msedge.exe",
            ]

            all_keys = chrome_keys + edge_keys

            for key_path in all_keys:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
                        path, _ = winreg.QueryValueEx(key, "")
                        if os.path.isfile(path) and os.access(path, os.X_OK):
                            found_paths.append(path)
                except (FileNotFoundError, OSError):
                    continue

        except ImportError:
            # winreg不可用（非Windows系统）
            pass
        except Exception:
            # 其他注册表访问错误
            pass

        return found_paths

    def find_available_port(self, start_port: int = 9222) -> int:
        """
        查找可用的端口
        
        Args:
            start_port: 起始端口号
            
        Returns:
            int: 可用的端口号
        """
        port = start_port
        while port < start_port + 100:  # 最多尝试100个端口
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                port += 1
        
        raise RuntimeError(f"无法找到可用的端口，已尝试 {start_port} 到 {port-1}")
    
    async def launch_browser(
        self, 
        browser_path: str, 
        debug_port: int, 
        headless: bool = False, 
        user_data_dir: Optional[str] = None
    ) -> subprocess.Popen:
        """
        启动浏览器进程
        
        Args:
            browser_path: 浏览器可执行文件路径
            debug_port: 远程调试端口
            headless: 是否无头模式
            user_data_dir: 用户数据目录
            
        Returns:
            subprocess.Popen: 浏览器进程对象
        """
        # 基本启动参数（基于MediaCrawler的参数）
        args = [
            browser_path,
            f"--remote-debugging-port={debug_port}",
            "--remote-debugging-address=0.0.0.0",  # 允许远程访问
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-web-security",  # 关键参数 - 禁用Web安全策略
            "--disable-features=VizDisplayCompositor",
            "--disable-dev-shm-usage",  # 避免共享内存问题
            "--no-sandbox",  # 在CDP模式下关闭沙箱
        ]
        
        # 无头模式
        if headless:
            args.extend([
                "--headless",
                "--disable-gpu",
            ])
        else:
            # 非无头模式下也保持一些稳定性参数
            args.append("--disable-blink-features=AutomationControlled")
        
        # 用户数据目录
        if user_data_dir:
            args.append(f"--user-data-dir={user_data_dir}")
        
        logger.info(f"启动浏览器: {browser_path}")
        logger.info(f"调试端口: {debug_port}")
        logger.info(f"无头模式: {headless}")
        
        try:
            # 在Windows上，使用CREATE_NEW_PROCESS_GROUP避免Ctrl+C影响子进程
            if self.system == "Windows":
                process = await asyncio.create_subprocess_exec(
                    *args,
                    stdout=asyncio.subprocess.DEVNULL,
                    stderr=asyncio.subprocess.DEVNULL,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                process = await asyncio.create_subprocess_exec(
                    *args,
                    stdout=asyncio.subprocess.DEVNULL,
                    stderr=asyncio.subprocess.DEVNULL,
                    preexec_fn=os.setsid  # 创建新的进程组
                )
            
            self.debug_port = debug_port
            return process
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            raise
    
    async def wait_for_browser_ready(self, debug_port: int, timeout: int = 30) -> bool:
        """
        等待浏览器准备就绪
        
        Args:
            debug_port: 调试端口
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否准备就绪
        """
        logger.info(f"等待浏览器在端口 {debug_port} 上准备就绪...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', debug_port))
                    if result == 0:
                        logger.info(f"浏览器已在端口 {debug_port} 上准备就绪")
                        return True
            except Exception:
                pass
            
            await asyncio.sleep(0.5)
        
        logger.error(f"浏览器在 {timeout} 秒内未能准备就绪")
        return False
    
    def get_browser_info(self, browser_path: str) -> Tuple[str, str]:
        """
        获取浏览器信息（名称和版本）
        
        Args:
            browser_path: 浏览器路径
            
        Returns:
            Tuple[str, str]: (浏览器名称, 版本信息)
        """
        try:
            if "chrome" in browser_path.lower():
                name = "Google Chrome"
            elif "edge" in browser_path.lower() or "msedge" in browser_path.lower():
                name = "Microsoft Edge"
            elif "chromium" in browser_path.lower():
                name = "Chromium"
            else:
                name = "Unknown Browser"
            
            # 尝试获取版本信息
            try:
                result = subprocess.run([browser_path, "--version"], 
                                      capture_output=True, text=True, timeout=5)
                version = result.stdout.strip() if result.stdout else "Unknown Version"
            except:
                version = "Unknown Version"
            
            return name, version
            
        except Exception:
            return "Unknown Browser", "Unknown Version"
    
    async def cleanup(self):
        """
        清理资源，关闭浏览器进程
        """
        if self.browser_process:
            try:
                logger.info("正在关闭浏览器进程...")
                
                if self.system == "Windows":
                    # Windows下使用taskkill强制终止进程树
                    subprocess.run(
                        ["taskkill", "/F", "/T", "/PID", str(self.browser_process.pid)], 
                        capture_output=True
                    )
                else:
                    # Unix系统下终止进程组
                    try:
                        self.browser_process.terminate()
                        await self.browser_process.wait()
                    except:
                        self.browser_process.kill()
                
                self.browser_process = None
                logger.info("浏览器进程已关闭")
                
            except Exception as e:
                logger.warning(f"关闭浏览器进程时出错: {e}")