import React, { useState, useCallback, useEffect } from 'react';
import { Card, Space, Alert, Button, message, Upload, Spin } from 'antd';
import { 
  LockOutlined, 
  ReloadOutlined, 
  SaveOutlined, 
  DownloadOutlined, 
  UploadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import CookieInput from './CookieInput';
import CookieDisplay from './CookieDisplay';
import CookieValidator from './CookieValidator';
import { Cookie, CookieInputFormat, CookieValidationResult } from '../../types/cookie';
import apiClient from '../../services/apiClient';
import './CookieManager.css';

interface CookieManagerProps {
  onCookieUpdate?: (cookies: Cookie[]) => void;
  onCookieSave?: (cookies: Cookie[]) => Promise<void>;
  initialCookies?: Cookie[];
}

const CookieManager: React.FC<CookieManagerProps> = ({
  onCookieUpdate,
  onCookieSave,
  initialCookies = []
}) => {
  const [cookies, setCookies] = useState<Cookie[]>(initialCookies);
  const [rawInput, setRawInput] = useState<string>('');
  const [inputFormat, setInputFormat] = useState<CookieInputFormat>('string');
  const [validationResult, setValidationResult] = useState<CookieValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [cookieStatus, setCookieStatus] = useState<{
    exists: boolean;
    valid: boolean;
    expiresAt?: string;
  } | null>(null);

  // 检查Cookie状态
  const checkCookieStatus = useCallback(async () => {
    try {
      setIsCheckingStatus(true);
      const status = await apiClient.getCookieStatus();
      setCookieStatus(status);
      // 如果有有效的Cookie，更新到组件状态
      if (status.exists && status.cookies) {
        setCookies(status.cookies);
        onCookieUpdate?.(status.cookies);
      }
    } catch (error) {
      console.error('检查Cookie状态失败:', error);
    } finally {
      setIsCheckingStatus(false);
    }
  }, [onCookieUpdate]);

  // 初始化时检查Cookie状态
  useEffect(() => {
    checkCookieStatus();
  }, [checkCookieStatus]);

  const handleInputChange = useCallback((value: string, format: CookieInputFormat) => {
    setRawInput(value);
    setInputFormat(format);
    setValidationResult(null);
  }, []);

  const handleValidation = useCallback(async (result: CookieValidationResult) => {
    setValidationResult(result);
    if (result.isValid && result.parsedCookies.length > 0) {
      // 调用API验证Cookie
      try {
        setIsValidating(true);
        const apiResult = await apiClient.validateCookie(result.parsedCookies);
        if (apiResult.valid) {
          setCookies(result.parsedCookies);
          onCookieUpdate?.(result.parsedCookies);
          message.success('Cookie验证成功');
        } else {
          message.error('Cookie验证失败: ' + (apiResult.message || '无效的Cookie'));
          setValidationResult({
            ...result,
            isValid: false,
            errors: [...result.errors, apiResult.message || '无效的Cookie']
          });
        }
      } catch (error) {
        message.error('Cookie验证失败: ' + (error as Error).message);
      } finally {
        setIsValidating(false);
      }
    }
  }, [onCookieUpdate]);

  const handleSave = useCallback(async () => {
    if (cookies.length === 0) {
      message.warning('没有可保存的Cookie');
      return;
    }

    setIsSaving(true);
    try {
      // 使用API保存Cookie
      const result = await apiClient.saveCookie(cookies);
      if (result.success) {
        message.success('Cookie保存成功');
        // 保存成功后检查状态
        await checkCookieStatus();
        // 如果有外部保存回调，也执行
        if (onCookieSave) {
          await onCookieSave(cookies);
        }
      } else {
        throw new Error(result.message || '保存失败');
      }
    } catch (error) {
      message.error('Cookie保存失败: ' + (error as Error).message);
    } finally {
      setIsSaving(false);
    }
  }, [cookies, onCookieSave]);

  const handleClear = useCallback(async () => {
    try {
      // 调用API清除Cookie
      const result = await apiClient.clearCookie();
      if (result.success) {
        setRawInput('');
        setCookies([]);
        setValidationResult(null);
        setCookieStatus(null);
        onCookieUpdate?.([]);
        message.success('Cookie已清除');
      } else {
        throw new Error(result.message || '清除失败');
      }
    } catch (error) {
      message.error('Cookie清除失败: ' + (error as Error).message);
    }
  }, [onCookieUpdate]);

  // 导出Cookie
  const handleExport = useCallback(async () => {
    try {
      const result = await apiClient.exportCookie();
      if (result.success && result.data) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(result.data, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `cookies_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        message.success('Cookie导出成功');
      } else {
        throw new Error(result.message || '导出失败');
      }
    } catch (error) {
      message.error('Cookie导出失败: ' + (error as Error).message);
    }
  }, []);

  // 导入Cookie处理
  const handleImport = useCallback(async (file: File) => {
    try {
      const text = await file.text();
      const cookieData = JSON.parse(text);
      
      // 调用API导入Cookie
      const result = await apiClient.importCookie(cookieData);
      if (result.success) {
        message.success('Cookie导入成功');
        // 导入成功后更新状态
        await checkCookieStatus();
      } else {
        throw new Error(result.message || '导入失败');
      }
    } catch (error) {
      if (error instanceof SyntaxError) {
        message.error('无效的JSON文件');
      } else {
        message.error('Cookie导入失败: ' + (error as Error).message);
      }
    }
    return false; // 阻止Upload组件的默认行为
  }, [checkCookieStatus]);

  const hasExpiredCookies = cookies.some(cookie => {
    if (!cookie.expires) return false;
    const expiryDate = new Date(cookie.expires);
    return expiryDate < new Date();
  });

  const hasExpiringSoonCookies = cookies.some(cookie => {
    if (!cookie.expires) return false;
    const expiryDate = new Date(cookie.expires);
    const dayFromNow = new Date();
    dayFromNow.setDate(dayFromNow.getDate() + 1);
    return expiryDate > new Date() && expiryDate < dayFromNow;
  });

  return (
    <Card 
      title={
        <Space>
          <LockOutlined />
          <span>Cookie管理</span>
          {isCheckingStatus && <Spin size="small" />}
          {cookieStatus && !isCheckingStatus && (
            cookieStatus.valid ? (
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
            ) : (
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            )
          )}
        </Space>
      }
      className="cookie-manager"
      extra={
        <Space>
          <Upload
            accept=".json"
            showUploadList={false}
            beforeUpload={handleImport}
          >
            <Button 
              icon={<UploadOutlined />} 
              size="small"
            >
              导入
            </Button>
          </Upload>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
            disabled={cookies.length === 0}
            size="small"
          >
            导出
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleClear}
            size="small"
          >
            清除
          </Button>
          <Button 
            type="primary"
            icon={<SaveOutlined />} 
            onClick={handleSave}
            loading={isSaving}
            disabled={cookies.length === 0}
            size="small"
          >
            保存
          </Button>
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Cookie状态提示 */}
        {cookieStatus && !isCheckingStatus && (
          cookieStatus.exists && cookieStatus.valid ? (
            <Alert
              type="success"
              message="Cookie状态正常"
              description={`Cookie有效，${cookieStatus.expiresAt ? `过期时间: ${new Date(cookieStatus.expiresAt).toLocaleString()}` : '无过期时间'}`}
              showIcon
            />
          ) : (
            <Alert
              type="info"
              message="未配置Cookie"
              description="请输入有效的Cookie以开始爬取数据"
              showIcon
            />
          )
        )}
        
        {/* Cookie过期警告 */}
        {hasExpiredCookies && (
          <Alert
            type="error"
            message="存在已过期的Cookie"
            description="部分Cookie已过期，可能导致爬虫无法正常工作，请更新Cookie。"
            showIcon
          />
        )}
        {!hasExpiredCookies && hasExpiringSoonCookies && (
          <Alert
            type="warning"
            message="Cookie即将过期"
            description="部分Cookie将在24小时内过期，建议尽快更新。"
            showIcon
          />
        )}

        {/* Cookie输入组件 */}
        <CookieInput
          value={rawInput}
          format={inputFormat}
          onChange={handleInputChange}
        />

        {/* Cookie验证组件 */}
        <CookieValidator
          input={rawInput}
          format={inputFormat}
          onValidate={handleValidation}
          isValidating={isValidating}
          onValidatingChange={setIsValidating}
        />

        {/* Cookie显示组件 */}
        {cookies.length > 0 && (
          <CookieDisplay
            cookies={cookies}
            validationResult={validationResult}
          />
        )}
      </Space>
    </Card>
  );
};

export default CookieManager;