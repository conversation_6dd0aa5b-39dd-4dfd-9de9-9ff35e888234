/* 主题切换按钮样式 */
.theme-toggle {
  position: relative;
  width: 64px;
  height: 32px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  outline: none;
}

.theme-toggle-track {
  display: block;
  width: 100%;
  height: 100%;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-full);
  border: 2px solid var(--border-primary);
  position: relative;
  transition: all var(--duration-300) var(--ease-out);
  overflow: hidden;
}

.theme-toggle-track::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, #fbbf24 0%, #3b82f6 100%);
  opacity: 0;
  transition: opacity var(--duration-300) var(--ease-out);
}

.theme-toggle:hover .theme-toggle-track::before {
  opacity: 0.1;
}

.theme-toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background-color: var(--bg-card);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-300) var(--ease-spring);
  box-shadow: var(--shadow-sm);
  color: var(--text-primary);
}

/* 深色模式时的位置 */
[data-theme="dark"] .theme-toggle-thumb {
  transform: translateX(32px);
}

/* 动画效果 */
.theme-toggle:active .theme-toggle-thumb {
  width: 28px;
}

[data-theme="dark"] .theme-toggle:active .theme-toggle-thumb {
  transform: translateX(28px);
}

/* 颜色调整 */
[data-theme="light"] .theme-toggle-track {
  background-color: #fef3c7;
  border-color: #fbbf24;
}

[data-theme="dark"] .theme-toggle-track {
  background-color: #1e3a8a;
  border-color: #3b82f6;
}

/* 图标动画 */
.theme-toggle-thumb svg {
  transition: transform var(--duration-300) var(--ease-spring);
}

.theme-toggle:hover .theme-toggle-thumb svg {
  transform: rotate(15deg);
}

[data-theme="dark"] .theme-toggle:hover .theme-toggle-thumb svg {
  transform: rotate(-15deg);
}