.data-table-enhanced {
  background: var(--surface);
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.3s ease;
}

.data-table-enhanced:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 表格头部 */
.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(238, 77, 45, 0.05) 0%, rgba(238, 77, 45, 0.02) 100%);
  border-bottom: 1px solid var(--border);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.data-count {
  padding: 4px 12px;
  background: rgba(238, 77, 45, 0.1);
  color: var(--primary);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 14px;
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--secondary);
  border-color: var(--primary);
  color: var(--primary);
}

/* 表格容器 */
.table-container {
  overflow-x: auto;
}

/* 表头 */
.table-head {
  background: var(--secondary);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-row {
  font-weight: 600;
  color: var(--text);
  font-size: 14px;
}

/* 表格行 */
.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 100px 150px;
  align-items: center;
  padding: 0 24px;
  min-height: 80px;
  border-bottom: 1px solid var(--border);
}

.data-row {
  cursor: pointer;
  transition: all 0.2s ease;
}

.data-row:hover {
  background: rgba(238, 77, 45, 0.03);
}

.data-row:last-child {
  border-bottom: none;
}

/* 表格单元格 */
.table-cell {
  padding: 16px 12px;
}

.table-cell:first-child {
  padding-left: 0;
}

.table-cell:last-child {
  padding-right: 0;
}

/* 商品图片 */
.product-image-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  color: var(--text-secondary);
}

/* 商品信息 */
.product-name {
  font-size: 14px;
  color: var(--text);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-brand {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

/* 价格 */
.product-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary);
}

.coupon-price {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
}

/* 销量 */
.product-sales {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.sales-number {
  font-size: 16px;
  font-weight: 600;
  color: var(--text);
}

.sales-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 店铺信息 */
.shop-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.shop-name {
  font-size: 14px;
  color: var(--text);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shop-type {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 骨架屏 */
.skeleton-row {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.skeleton-image {
  width: 60px;
  height: 60px;
  background: var(--border);
  border-radius: 8px;
}

.skeleton-text {
  height: 16px;
  background: var(--border);
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-price {
  height: 20px;
  width: 80px;
  background: var(--border);
  border-radius: 4px;
}

.skeleton-sales {
  height: 20px;
  width: 60px;
  background: var(--border);
  border-radius: 4px;
}

.skeleton-shop {
  height: 16px;
  width: 100px;
  background: var(--border);
  border-radius: 4px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  color: var(--text-secondary);
  margin-bottom: 24px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: var(--surface);
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.modal-close {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--secondary);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--border);
  color: var(--text);
}

.modal-body {
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

.detail-image-section {
  padding: 24px;
  background: var(--secondary);
  display: flex;
  justify-content: center;
}

.detail-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid var(--border);
}

.detail-info-section {
  padding: 24px;
}

.detail-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.detail-price-section {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 20px;
}

.current-price {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary);
}

.original-price {
  font-size: 16px;
  color: var(--text-secondary);
  text-decoration: line-through;
}

.detail-stats {
  display: flex;
  gap: 24px;
  padding: 16px;
  background: var(--secondary);
  border-radius: 12px;
  margin-bottom: 20px;
}

.detail-stats .stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-stats .stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.detail-stats .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text);
}

.detail-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--text);
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  padding: 4px 12px;
  background: rgba(238, 77, 45, 0.1);
  color: var(--primary);
  border-radius: 16px;
  font-size: 12px;
}

/* 暗色主题适配 */
[data-theme="dark"] .data-table-enhanced {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table-header {
  background: linear-gradient(135deg, rgba(255, 104, 84, 0.1) 0%, rgba(255, 104, 84, 0.05) 100%);
}

[data-theme="dark"] .modal-content {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-row {
    grid-template-columns: 60px 1fr;
    padding: 0 16px;
  }
  
  .table-cell:nth-child(3),
  .table-cell:nth-child(4),
  .table-cell:nth-child(5) {
    display: none;
  }
  
  .product-name {
    font-size: 13px;
  }
  
  .table-header {
    padding: 16px 20px;
  }
  
  .modal-body {
    max-height: calc(90vh - 70px);
  }
  
  .detail-info-grid {
    grid-template-columns: 1fr;
  }
}