"""
User-Agent管理器模块
负责生成和管理真实、一致的User-Agent信息
"""

import random
import json
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from src.utils.helpers import load_config, load_json


class UserAgentManager:
    """User-Agent管理器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化User-Agent管理器"""
        self.config = load_config(config_path)
        self.ua_config = self.config.get("user_agent", {})
        
        # 加载User-Agent数据
        self.user_agents_data = self._load_enhanced_user_agents()
        
        # 使用历史
        self.usage_history = {}
        self.last_rotation_time = time.time()
        
        logger.info("User-Agent管理器初始化完成")
    
    def _load_enhanced_user_agents(self) -> Dict[str, List[Dict]]:
        """加载增强的User-Agent数据"""
        try:
            # 尝试加载增强的UA文件
            enhanced_ua_file = "config/enhanced_user_agents.json"
            return load_json(enhanced_ua_file)
        except:
            # 如果没有增强文件，使用基础文件并增强
            basic_ua = load_json("config/user_agents.json")
            return self._enhance_basic_user_agents(basic_ua)
    
    def _enhance_basic_user_agents(self, basic_ua: Dict) -> Dict[str, List[Dict]]:
        """增强基础User-Agent数据"""
        enhanced_ua = {}
        
        for device_type, ua_list in basic_ua.items():
            enhanced_ua[device_type] = []
            
            for ua_string in ua_list:
                enhanced_info = self._parse_and_enhance_ua(ua_string, device_type)
                enhanced_ua[device_type].append(enhanced_info)
        
        # 保存增强的UA数据
        try:
            with open("config/enhanced_user_agents.json", "w", encoding="utf-8") as f:
                json.dump(enhanced_ua, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"保存增强UA数据失败: {e}")
        
        return enhanced_ua
    
    def _parse_and_enhance_ua(self, ua_string: str, device_type: str) -> Dict:
        """解析并增强User-Agent信息"""
        enhanced_info = {
            "user_agent": ua_string,
            "device_type": device_type,
            "weight": 1.0,  # 使用权重
            "last_used": 0,
            "usage_count": 0
        }
        
        # 解析浏览器信息
        if "Chrome/" in ua_string:
            enhanced_info["browser"] = "Chrome"
            import re
            match = re.search(r"Chrome/(\d+)\.(\d+)\.(\d+)\.(\d+)", ua_string)
            if match:
                enhanced_info["browser_version"] = {
                    "major": int(match.group(1)),
                    "minor": int(match.group(2)),
                    "build": int(match.group(3)),
                    "patch": int(match.group(4))
                }
        elif "Firefox/" in ua_string:
            enhanced_info["browser"] = "Firefox"
            match = re.search(r"Firefox/(\d+)\.(\d+)", ua_string)
            if match:
                enhanced_info["browser_version"] = {
                    "major": int(match.group(1)),
                    "minor": int(match.group(2))
                }
        elif "Safari/" in ua_string and "Chrome" not in ua_string:
            enhanced_info["browser"] = "Safari"
            match = re.search(r"Version/(\d+)\.(\d+)", ua_string)
            if match:
                enhanced_info["browser_version"] = {
                    "major": int(match.group(1)),
                    "minor": int(match.group(2))
                }
        
        # 解析操作系统
        if "Windows NT 10.0" in ua_string:
            enhanced_info["os"] = "Windows"
            enhanced_info["os_version"] = "10"
            enhanced_info["platform"] = "Win32"
        elif "Windows NT 11.0" in ua_string:
            enhanced_info["os"] = "Windows"
            enhanced_info["os_version"] = "11"
            enhanced_info["platform"] = "Win32"
        elif "Macintosh" in ua_string:
            enhanced_info["os"] = "macOS"
            enhanced_info["platform"] = "MacIntel"
            if "Mac OS X 10_15" in ua_string:
                enhanced_info["os_version"] = "10.15"
            elif "Mac OS X 11_" in ua_string:
                enhanced_info["os_version"] = "11.0"
        elif "Linux" in ua_string:
            enhanced_info["os"] = "Linux"
            enhanced_info["platform"] = "Linux x86_64"
            enhanced_info["os_version"] = "5.4"
        elif "iPhone" in ua_string:
            enhanced_info["os"] = "iOS"
            enhanced_info["platform"] = "iPhone"
            match = re.search(r"iPhone OS (\d+)_(\d+)", ua_string)
            if match:
                enhanced_info["os_version"] = f"{match.group(1)}.{match.group(2)}"
        elif "Android" in ua_string:
            enhanced_info["os"] = "Android"
            enhanced_info["platform"] = "Linux armv7l"
            match = re.search(r"Android (\d+)", ua_string)
            if match:
                enhanced_info["os_version"] = match.group(1)
        
        # 生成匹配的屏幕分辨率
        enhanced_info["screen_resolutions"] = self._get_matching_screen_resolutions(
            enhanced_info.get("os", ""), device_type
        )
        
        # 生成匹配的硬件信息
        enhanced_info["hardware_profiles"] = self._get_matching_hardware_profiles(
            enhanced_info.get("os", ""), device_type
        )
        
        return enhanced_info
    
    def _get_matching_screen_resolutions(self, os: str, device_type: str) -> List[Dict]:
        """获取匹配的屏幕分辨率"""
        if device_type == "mobile":
            if os == "iOS":
                return [
                    {"width": 375, "height": 812, "pixel_ratio": 3},  # iPhone X/11/12
                    {"width": 414, "height": 896, "pixel_ratio": 2},  # iPhone XR/11
                    {"width": 390, "height": 844, "pixel_ratio": 3},  # iPhone 12/13
                ]
            elif os == "Android":
                return [
                    {"width": 360, "height": 800, "pixel_ratio": 3},
                    {"width": 412, "height": 915, "pixel_ratio": 2.6},
                    {"width": 393, "height": 851, "pixel_ratio": 2.75},
                ]
        elif device_type == "tablet":
            if os == "iOS":
                return [
                    {"width": 768, "height": 1024, "pixel_ratio": 2},  # iPad
                    {"width": 820, "height": 1180, "pixel_ratio": 2},  # iPad Air
                    {"width": 1024, "height": 1366, "pixel_ratio": 2}, # iPad Pro
                ]
            else:
                return [
                    {"width": 800, "height": 1280, "pixel_ratio": 1.5},
                    {"width": 1200, "height": 1920, "pixel_ratio": 2},
                ]
        else:  # desktop
            if os == "Windows":
                return [
                    {"width": 1920, "height": 1080, "pixel_ratio": 1},
                    {"width": 1366, "height": 768, "pixel_ratio": 1},
                    {"width": 1536, "height": 864, "pixel_ratio": 1.25},
                    {"width": 2560, "height": 1440, "pixel_ratio": 1},
                ]
            elif os == "macOS":
                return [
                    {"width": 1440, "height": 900, "pixel_ratio": 2},  # MacBook Air
                    {"width": 1680, "height": 1050, "pixel_ratio": 2}, # MacBook Pro
                    {"width": 2560, "height": 1600, "pixel_ratio": 2}, # MacBook Pro 16"
                ]
            else:  # Linux
                return [
                    {"width": 1920, "height": 1080, "pixel_ratio": 1},
                    {"width": 1366, "height": 768, "pixel_ratio": 1},
                    {"width": 1440, "height": 900, "pixel_ratio": 1},
                ]
        
        # 默认返回
        return [{"width": 1920, "height": 1080, "pixel_ratio": 1}]
    
    def _get_matching_hardware_profiles(self, os: str, device_type: str) -> List[Dict]:
        """获取匹配的硬件配置"""
        if device_type == "mobile":
            return [
                {"cpu_cores": 6, "memory": 4, "gpu": "Adreno 640"},
                {"cpu_cores": 8, "memory": 6, "gpu": "Mali-G78"},
                {"cpu_cores": 6, "memory": 8, "gpu": "Apple A15"},
            ]
        elif device_type == "tablet":
            return [
                {"cpu_cores": 8, "memory": 8, "gpu": "Apple M1"},
                {"cpu_cores": 8, "memory": 6, "gpu": "Adreno 650"},
            ]
        else:  # desktop
            if os == "Windows":
                return [
                    {"cpu_cores": 8, "memory": 16, "gpu": "NVIDIA GeForce RTX 3060"},
                    {"cpu_cores": 6, "memory": 16, "gpu": "AMD Radeon RX 6600"},
                    {"cpu_cores": 4, "memory": 8, "gpu": "Intel UHD Graphics 630"},
                ]
            elif os == "macOS":
                return [
                    {"cpu_cores": 8, "memory": 16, "gpu": "Apple M1"},
                    {"cpu_cores": 10, "memory": 32, "gpu": "Apple M1 Pro"},
                ]
            else:  # Linux
                return [
                    {"cpu_cores": 4, "memory": 8, "gpu": "Intel HD Graphics"},
                    {"cpu_cores": 6, "memory": 16, "gpu": "AMD Radeon RX 580"},
                ]
        
        return [{"cpu_cores": 4, "memory": 8, "gpu": "Generic GPU"}]
    
    def get_optimal_user_agent(self, target_site: str = "pdd", prefer_device: str = "desktop") -> Dict:
        """
        获取最优的User-Agent
        
        Args:
            target_site: 目标网站类型
            prefer_device: 偏好的设备类型
            
        Returns:
            Dict: 完整的User-Agent信息
        """
        # 根据目标网站调整策略
        if target_site == "pdd":
            # 拼多多偏好移动端，但桌面端也可以
            device_weights = {
                "mobile": 0.6,
                "desktop": 0.35,
                "tablet": 0.05
            }
        else:
            device_weights = {
                "desktop": 0.7,
                "mobile": 0.25,
                "tablet": 0.05
            }
        
        # 如果指定了偏好设备，调整权重
        if prefer_device in device_weights:
            device_weights[prefer_device] *= 2
            # 重新归一化
            total_weight = sum(device_weights.values())
            device_weights = {k: v/total_weight for k, v in device_weights.items()}
        
        # 选择设备类型
        device_type = self._weighted_choice(device_weights)
        
        # 从该设备类型中选择最优UA
        candidates = self.user_agents_data.get(device_type, [])
        if not candidates:
            logger.warning(f"没有找到 {device_type} 类型的User-Agent")
            device_type = "desktop"
            candidates = self.user_agents_data.get(device_type, [])
        
        # 计算每个候选UA的得分
        scored_candidates = []
        current_time = time.time()
        
        for ua_info in candidates:
            score = self._calculate_ua_score(ua_info, current_time)
            scored_candidates.append((score, ua_info))
        
        # 按得分排序并选择
        scored_candidates.sort(key=lambda x: x[0], reverse=True)
        
        # 从前几名中随机选择（避免总是选择同一个）
        top_candidates = scored_candidates[:min(3, len(scored_candidates))]
        selected_ua = random.choice(top_candidates)[1]
        
        # 更新使用记录
        selected_ua["last_used"] = current_time
        selected_ua["usage_count"] += 1
        
        # 生成完整的设备信息
        complete_info = self._generate_complete_device_info(selected_ua)
        
        logger.info(f"选择User-Agent: {selected_ua['browser']} {device_type}")
        return complete_info
    
    def _weighted_choice(self, weights: Dict[str, float]) -> str:
        """根据权重随机选择"""
        items = list(weights.keys())
        weights_list = list(weights.values())
        return random.choices(items, weights=weights_list)[0]
    
    def _calculate_ua_score(self, ua_info: Dict, current_time: float) -> float:
        """计算User-Agent得分"""
        score = ua_info.get("weight", 1.0)
        
        # 时间衰减：最近使用过的降低得分
        last_used = ua_info.get("last_used", 0)
        time_since_last_use = current_time - last_used
        if time_since_last_use < 3600:  # 1小时内使用过
            score *= 0.3
        elif time_since_last_use < 86400:  # 24小时内使用过
            score *= 0.7
        
        # 使用频率：使用次数过多的降低得分
        usage_count = ua_info.get("usage_count", 0)
        if usage_count > 10:
            score *= 0.5
        elif usage_count > 5:
            score *= 0.8
        
        # 浏览器版本新旧程度
        browser_version = ua_info.get("browser_version", {})
        if browser_version:
            major_version = browser_version.get("major", 0)
            if ua_info.get("browser") == "Chrome":
                # Chrome版本太老的降低得分
                if major_version < 110:
                    score *= 0.6
                elif major_version < 115:
                    score *= 0.8
            elif ua_info.get("browser") == "Firefox":
                if major_version < 115:
                    score *= 0.6
        
        return score
    
    def _generate_complete_device_info(self, ua_info: Dict) -> Dict:
        """生成完整的设备信息"""
        # 选择匹配的屏幕分辨率
        screen_options = ua_info.get("screen_resolutions", [])
        screen = random.choice(screen_options) if screen_options else {"width": 1920, "height": 1080, "pixel_ratio": 1}
        
        # 选择匹配的硬件配置
        hardware_options = ua_info.get("hardware_profiles", [])
        hardware = random.choice(hardware_options) if hardware_options else {"cpu_cores": 4, "memory": 8, "gpu": "Generic"}
        
        complete_info = {
            "user_agent": ua_info["user_agent"],
            "device_type": ua_info["device_type"],
            "browser": ua_info.get("browser", "Chrome"),
            "browser_version": ua_info.get("browser_version", {}),
            "os": ua_info.get("os", "Windows"),
            "os_version": ua_info.get("os_version", "10"),
            "platform": ua_info.get("platform", "Win32"),
            "screen": screen,
            "hardware": hardware,
            "selected_at": time.time()
        }
        
        return complete_info
    
    def should_rotate_user_agent(self) -> bool:
        """判断是否应该轮换User-Agent"""
        current_time = time.time()
        rotation_interval = self.ua_config.get("rotation_interval", 3600)  # 默认1小时
        
        return (current_time - self.last_rotation_time) > rotation_interval
    
    def force_rotation(self) -> None:
        """强制轮换User-Agent"""
        self.last_rotation_time = time.time()
        logger.info("强制轮换User-Agent")
    
    def get_usage_statistics(self) -> Dict:
        """获取使用统计"""
        stats = {
            "total_user_agents": sum(len(uas) for uas in self.user_agents_data.values()),
            "device_types": list(self.user_agents_data.keys()),
            "last_rotation": self.last_rotation_time,
            "usage_history": len(self.usage_history)
        }
        
        return stats
    
    def update_user_agent_database(self) -> None:
        """更新User-Agent数据库"""
        # 这里可以实现从在线源更新UA数据库的逻辑
        logger.info("User-Agent数据库更新功能待实现")
        pass