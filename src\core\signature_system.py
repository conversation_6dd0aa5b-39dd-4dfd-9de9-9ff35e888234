"""
智能签名系统 - 基于MediaCrawler的签名生成架构
"""
import time
import json
import hashlib
import random
from typing import Dict, Any, Optional
from loguru import logger
import execjs
from pathlib import Path

class SignatureSystem:
    """智能签名生成系统"""
    
    def __init__(self, browser_manager):
        self.browser_manager = browser_manager
        self.js_runtime = None
        self.signature_cache = {}
        self.js_files_path = Path(__file__).parent / "js"
        self._init_js_runtime()
        
    def _init_js_runtime(self):
        """初始化JavaScript运行时"""
        try:
            # 尝试使用Node.js运行时
            self.js_runtime = execjs.get("Node")
            logger.info("使用Node.js作为JS运行时")
        except:
            # 回退到默认运行时
            self.js_runtime = execjs.get()
            logger.warning("使用默认JS运行时")
            
    async def generate_pdd_signature(self, api_path: str, params: Dict[str, Any]) -> Dict[str, str]:
        """生成拼多多API签名"""
        cache_key = f"{api_path}:{json.dumps(params, sort_keys=True)}"
        
        # 检查缓存
        if cache_key in self.signature_cache:
            cached = self.signature_cache[cache_key]
            if time.time() - cached['timestamp'] < 300:  # 5分钟缓存
                logger.debug("使用缓存的签名")
                return cached['signature']
                
        try:
            # 方法1: 尝试从浏览器上下文执行签名生成
            if self.browser_manager.page:
                signature = await self._generate_signature_from_browser(api_path, params)
                if signature:
                    self._cache_signature(cache_key, signature)
                    return signature
                    
            # 方法2: 使用本地JS运行时
            signature = self._generate_signature_locally(api_path, params)
            if signature:
                self._cache_signature(cache_key, signature)
                return signature
                
            # 方法3: 使用简单的签名算法
            return self._generate_simple_signature(api_path, params)
            
        except Exception as e:
            logger.error(f"签名生成失败: {e}")
            return self._generate_simple_signature(api_path, params)
            
    async def _generate_signature_from_browser(self, api_path: str, params: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """从浏览器上下文生成签名"""
        try:
            # 构建签名生成脚本
            script = """
            (function() {
                // 尝试查找PDD的签名函数
                if (window._pdd_sign || window.signData || window.generateSign) {
                    const signFunc = window._pdd_sign || window.signData || window.generateSign;
                    const timestamp = Date.now();
                    const params = %s;
                    
                    // 调用签名函数
                    const result = signFunc({
                        api: '%s',
                        params: params,
                        timestamp: timestamp
                    });
                    
                    return {
                        sign: result.sign || result,
                        timestamp: timestamp,
                        anti_content: result.anti_content || ''
                    };
                }
                
                // 如果没有找到签名函数，返回null
                return null;
            })();
            """ % (json.dumps(params), api_path)
            
            result = await self.browser_manager.execute_script(script)
            
            if result:
                logger.debug("从浏览器生成签名成功")
                return {
                    'sign': result.get('sign', ''),
                    'timestamp': str(result.get('timestamp', '')),
                    'anti_content': result.get('anti_content', '')
                }
                
        except Exception as e:
            logger.warning(f"浏览器签名生成失败: {e}")
            
        return None
        
    def _generate_signature_locally(self, api_path: str, params: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """使用本地JS运行时生成签名"""
        try:
            # 检查是否有PDD签名JS文件
            pdd_sign_js = self.js_files_path / "pdd_sign.js"
            if not pdd_sign_js.exists():
                return None
                
            # 读取JS代码
            js_code = pdd_sign_js.read_text(encoding='utf-8')
            
            # 编译JS代码
            ctx = self.js_runtime.compile(js_code)
            
            # 执行签名生成
            timestamp = int(time.time() * 1000)
            result = ctx.call('generatePDDSign', {
                'api': api_path,
                'params': params,
                'timestamp': timestamp
            })
            
            if result:
                logger.debug("本地JS签名生成成功")
                return {
                    'sign': result.get('sign', ''),
                    'timestamp': str(timestamp),
                    'anti_content': result.get('anti_content', '')
                }
                
        except Exception as e:
            logger.warning(f"本地签名生成失败: {e}")
            
        return None
        
    def _generate_simple_signature(self, api_path: str, params: Dict[str, Any]) -> Dict[str, str]:
        """生成简单的签名（降级方案）"""
        timestamp = int(time.time() * 1000)
        
        # 构建签名字符串
        sign_str = f"{api_path}"
        
        # 按照键排序参数
        sorted_params = sorted(params.items())
        for key, value in sorted_params:
            sign_str += f"{key}{value}"
            
        sign_str += str(timestamp)
        
        # 添加盐值
        salt = "pdd_mobile_" + str(random.randint(1000, 9999))
        sign_str += salt
        
        # 生成MD5签名
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        
        logger.debug("使用简单签名算法")
        
        return {
            'sign': sign,
            'timestamp': str(timestamp),
            'anti_content': self._generate_anti_content()
        }
        
    def _generate_anti_content(self) -> str:
        """生成反爬虫内容"""
        # 模拟设备指纹
        device_id = hashlib.md5(str(random.random()).encode()).hexdigest()[:16]
        
        anti_content = {
            'device_id': device_id,
            'platform': 'android',
            'app_version': '6.89.0',
            'os_version': '12',
            'channel': 'xiaomi',
            'screen': '1080x2400',
            'timestamp': int(time.time() * 1000)
        }
        
        return json.dumps(anti_content, separators=(',', ':'))
        
    def _cache_signature(self, cache_key: str, signature: Dict[str, str]):
        """缓存签名"""
        self.signature_cache[cache_key] = {
            'signature': signature,
            'timestamp': time.time()
        }
        
        # 清理过期缓存
        if len(self.signature_cache) > 100:
            current_time = time.time()
            expired_keys = [
                k for k, v in self.signature_cache.items()
                if current_time - v['timestamp'] > 300
            ]
            for key in expired_keys:
                del self.signature_cache[key]
                
    async def inject_signature_interceptor(self):
        """注入签名拦截器到浏览器"""
        if not self.browser_manager.page:
            return
            
        try:
            script = """
            // 拦截XMLHttpRequest
            (function() {
                const originalOpen = XMLHttpRequest.prototype.open;
                const originalSend = XMLHttpRequest.prototype.send;
                
                XMLHttpRequest.prototype.open = function(method, url, ...args) {
                    this._url = url;
                    this._method = method;
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                XMLHttpRequest.prototype.send = function(data) {
                    // 记录请求信息
                    if (this._url && this._url.includes('api')) {
                        console.log('API请求:', {
                            url: this._url,
                            method: this._method,
                            data: data,
                            headers: this.getAllResponseHeaders()
                        });
                    }
                    
                    return originalSend.apply(this, [data]);
                };
            })();
            
            // 拦截fetch
            (function() {
                const originalFetch = window.fetch;
                
                window.fetch = function(url, options = {}) {
                    // 记录请求信息
                    if (url.includes('api')) {
                        console.log('Fetch请求:', {
                            url: url,
                            options: options
                        });
                    }
                    
                    return originalFetch.apply(this, [url, options]);
                };
            })();
            """
            
            await self.browser_manager.page.add_init_script(script)
            logger.debug("签名拦截器注入成功")
            
        except Exception as e:
            logger.warning(f"注入签名拦截器失败: {e}")
            
    def update_signature_algorithm(self, js_code: str):
        """更新签名算法"""
        try:
            # 保存新的签名JS代码
            (self.js_files_path / "pdd_sign.js").write_text(js_code, encoding='utf-8')
            
            # 清空缓存
            self.signature_cache.clear()
            
            logger.info("签名算法已更新")
            
        except Exception as e:
            logger.error(f"更新签名算法失败: {e}")