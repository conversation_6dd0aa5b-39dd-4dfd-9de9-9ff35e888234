<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON>管理增强版 - 拼多多爬虫</title>
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="https://unpkg.com/antd@5.20.1/dist/antd.min.js"></script>
  <script src="https://unpkg.com/@ant-design/icons@5.3.7/dist/index.umd.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/antd@5.20.1/dist/reset.css">
  <style>
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 24px;
    }
    .demo-section {
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      margin-bottom: 24px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03), 0 2px 4px rgba(0, 0, 0, 0.04);
    }
    .api-status {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    .cookie-manager-demo {
      margin-top: 24px;
    }
  </style>
</head>
<body>
  <div id="root"></div>

  <script type="text/babel">
    const { useState, useEffect, useCallback } = React;
    const { 
      Layout, Typography, Space, Card, Button, Alert, message, Spin, Upload, Divider, Tag
    } = antd;
    const { 
      LockOutlined, ReloadOutlined, SaveOutlined, DownloadOutlined, 
      UploadOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
      ApiOutlined
    } = icons;
    
    const { Header, Content } = Layout;
    const { Title, Paragraph, Text } = Typography;

    // 模拟API客户端
    const mockApiClient = {
      validateCookie: async (cookies) => {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        // 模拟验证逻辑
        const isValid = cookies.length > 0 && cookies.some(c => c.name && c.value);
        return {
          valid: isValid,
          message: isValid ? '验证成功' : '无效的Cookie格式'
        };
      },
      
      saveCookie: async (cookies) => {
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: 'Cookie保存成功'
        };
      },
      
      getCookieStatus: async () => {
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          exists: Math.random() > 0.3,
          valid: Math.random() > 0.5,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          cookies: []
        };
      },
      
      clearCookie: async () => {
        await new Promise(resolve => setTimeout(resolve, 600));
        return {
          success: true,
          message: 'Cookie已清除'
        };
      },
      
      exportCookie: async () => {
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          data: {
            cookies: [
              {
                name: 'PDDAccessToken',
                value: 'example_token_value',
                domain: '.pinduoduo.com',
                path: '/',
                expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
              }
            ],
            exportTime: new Date().toISOString()
          }
        };
      },
      
      importCookie: async (data) => {
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: '导入成功'
        };
      }
    };

    // API状态组件
    const ApiStatus = () => {
      const [status, setStatus] = useState('checking');
      
      useEffect(() => {
        const checkStatus = async () => {
          try {
            await mockApiClient.getCookieStatus();
            setStatus('online');
          } catch (error) {
            setStatus('offline');
          }
        };
        
        checkStatus();
        const interval = setInterval(checkStatus, 30000);
        return () => clearInterval(interval);
      }, []);
      
      return (
        <div className="api-status">
          <Tag 
            icon={<ApiOutlined />} 
            color={status === 'online' ? 'success' : status === 'offline' ? 'error' : 'processing'}
          >
            API状态: {status === 'online' ? '在线' : status === 'offline' ? '离线' : '检查中...'}
          </Tag>
        </div>
      );
    };

    // 增强版Cookie管理演示
    const CookieManagerEnhancedDemo = () => {
      const [cookies, setCookies] = useState([]);
      const [isValidating, setIsValidating] = useState(false);
      const [isSaving, setIsSaving] = useState(false);
      const [isCheckingStatus, setIsCheckingStatus] = useState(false);
      const [cookieStatus, setCookieStatus] = useState(null);
      const [cookieInput, setCookieInput] = useState('');

      // 检查Cookie状态
      const checkCookieStatus = useCallback(async () => {
        try {
          setIsCheckingStatus(true);
          const status = await mockApiClient.getCookieStatus();
          setCookieStatus(status);
          message.info(`Cookie状态: ${status.exists ? '存在' : '不存在'}, ${status.valid ? '有效' : '无效'}`);
        } catch (error) {
          message.error('检查状态失败');
        } finally {
          setIsCheckingStatus(false);
        }
      }, []);

      // 验证Cookie
      const handleValidate = async () => {
        if (!cookieInput.trim()) {
          message.warning('请输入Cookie');
          return;
        }

        try {
          setIsValidating(true);
          // 简单解析Cookie
          const parsedCookies = cookieInput.split(';').map(c => {
            const [name, value] = c.trim().split('=');
            return { name, value };
          }).filter(c => c.name && c.value);
          
          const result = await mockApiClient.validateCookie(parsedCookies);
          if (result.valid) {
            setCookies(parsedCookies);
            message.success('Cookie验证成功');
          } else {
            message.error(result.message);
          }
        } catch (error) {
          message.error('验证失败: ' + error.message);
        } finally {
          setIsValidating(false);
        }
      };

      // 保存Cookie
      const handleSave = async () => {
        if (cookies.length === 0) {
          message.warning('没有可保存的Cookie');
          return;
        }

        try {
          setIsSaving(true);
          const result = await mockApiClient.saveCookie(cookies);
          if (result.success) {
            message.success(result.message);
            await checkCookieStatus();
          }
        } catch (error) {
          message.error('保存失败: ' + error.message);
        } finally {
          setIsSaving(false);
        }
      };

      // 清除Cookie
      const handleClear = async () => {
        try {
          const result = await mockApiClient.clearCookie();
          if (result.success) {
            setCookies([]);
            setCookieInput('');
            setCookieStatus(null);
            message.success(result.message);
          }
        } catch (error) {
          message.error('清除失败: ' + error.message);
        }
      };

      // 导出Cookie
      const handleExport = async () => {
        try {
          const result = await mockApiClient.exportCookie();
          if (result.success && result.data) {
            const blob = new Blob([JSON.stringify(result.data, null, 2)], {
              type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `cookies_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            message.success('导出成功');
          }
        } catch (error) {
          message.error('导出失败: ' + error.message);
        }
      };

      // 导入Cookie
      const handleImport = async (file) => {
        try {
          const text = await file.text();
          const data = JSON.parse(text);
          const result = await mockApiClient.importCookie(data);
          if (result.success) {
            message.success(result.message);
            await checkCookieStatus();
          }
        } catch (error) {
          message.error('导入失败: ' + error.message);
        }
        return false;
      };

      useEffect(() => {
        checkCookieStatus();
      }, []);

      return (
        <Card 
          title={
            <Space>
              <LockOutlined />
              <span>Cookie管理（API集成版）</span>
              {isCheckingStatus && <Spin size="small" />}
              {cookieStatus && !isCheckingStatus && (
                cookieStatus.valid ? (
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                ) : (
                  <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                )
              )}
            </Space>
          }
          extra={
            <Space>
              <Upload
                accept=".json"
                showUploadList={false}
                beforeUpload={handleImport}
              >
                <Button icon={<UploadOutlined />} size="small">
                  导入
                </Button>
              </Upload>
              <Button 
                icon={<DownloadOutlined />} 
                onClick={handleExport}
                size="small"
              >
                导出
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleClear}
                size="small"
              >
                清除
              </Button>
              <Button 
                type="primary"
                icon={<SaveOutlined />} 
                onClick={handleSave}
                loading={isSaving}
                disabled={cookies.length === 0}
                size="small"
              >
                保存
              </Button>
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* Cookie状态提示 */}
            {cookieStatus && (
              cookieStatus.exists && cookieStatus.valid ? (
                <Alert
                  type="success"
                  message="Cookie状态正常"
                  description={`Cookie有效，过期时间: ${new Date(cookieStatus.expiresAt).toLocaleString()}`}
                  showIcon
                />
              ) : (
                <Alert
                  type="info"
                  message="未配置Cookie"
                  description="请输入有效的Cookie以开始爬取数据"
                  showIcon
                />
              )
            )}

            {/* Cookie输入区域 */}
            <div>
              <Text strong>输入Cookie：</Text>
              <textarea
                value={cookieInput}
                onChange={(e) => setCookieInput(e.target.value)}
                placeholder="请输入Cookie字符串，格式如：name1=value1; name2=value2"
                style={{
                  width: '100%',
                  minHeight: '100px',
                  marginTop: '8px',
                  padding: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px'
                }}
              />
              <Button 
                type="primary" 
                onClick={handleValidate}
                loading={isValidating}
                style={{ marginTop: '8px' }}
              >
                验证Cookie
              </Button>
            </div>

            {/* 已验证的Cookie显示 */}
            {cookies.length > 0 && (
              <div>
                <Text strong>已验证的Cookie ({cookies.length}个)：</Text>
                <div style={{ marginTop: '8px', padding: '12px', background: '#f5f5f5', borderRadius: '4px' }}>
                  {cookies.map((cookie, index) => (
                    <div key={index} style={{ marginBottom: '4px' }}>
                      <Text code>{cookie.name}={cookie.value}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Space>
        </Card>
      );
    };

    // 主应用组件
    const App = () => {
      return (
        <Layout style={{ minHeight: '100vh' }}>
          <ApiStatus />
          <Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}>
            <Title level={3} style={{ margin: '16px 0' }}>拼多多爬虫 - Cookie管理增强版</Title>
          </Header>
          <Content style={{ padding: '24px' }}>
            <div className="container">
              <div className="demo-section">
                <Title level={4}>功能特性</Title>
                <Paragraph>
                  本示例展示了增强版的Cookie管理功能，包含完整的API集成：
                </Paragraph>
                <ul>
                  <li>✅ Cookie验证API调用 - 实时验证Cookie有效性</li>
                  <li>✅ Cookie保存API调用 - 持久化存储Cookie到后端</li>
                  <li>✅ Cookie状态检查 - 自动检查Cookie状态并显示</li>
                  <li>✅ Cookie清除功能 - 一键清除所有Cookie</li>
                  <li>✅ Cookie导入/导出功能 - 支持JSON格式的导入导出</li>
                  <li>📊 Loading状态提示 - 所有操作都有加载状态</li>
                  <li>🔒 错误处理 - 完善的错误提示和处理机制</li>
                </ul>
              </div>

              <div className="cookie-manager-demo">
                <CookieManagerEnhancedDemo />
              </div>

              <div className="demo-section" style={{ marginTop: '24px' }}>
                <Title level={4}>API集成说明</Title>
                <Space direction="vertical">
                  <div>
                    <Text strong>已集成的API端点：</Text>
                    <ul>
                      <li><Text code>POST /api/cookie/validate</Text> - 验证Cookie有效性</li>
                      <li><Text code>POST /api/cookie/save</Text> - 保存Cookie到服务器</li>
                      <li><Text code>GET /api/cookie/status</Text> - 获取Cookie状态</li>
                      <li><Text code>DELETE /api/cookie/clear</Text> - 清除服务器上的Cookie</li>
                      <li><Text code>GET /api/cookie/export</Text> - 导出Cookie数据</li>
                      <li><Text code>POST /api/cookie/import</Text> - 导入Cookie数据</li>
                    </ul>
                  </div>
                  <Alert
                    message="提示"
                    description="本演示使用模拟API，实际使用时请确保后端服务正常运行。"
                    type="info"
                    showIcon
                  />
                </Space>
              </div>
            </div>
          </Content>
        </Layout>
      );
    };

    // 渲染应用
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(<App />);
  </script>
</body>
</html>