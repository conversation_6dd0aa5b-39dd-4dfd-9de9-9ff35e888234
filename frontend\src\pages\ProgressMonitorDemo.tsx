import React, { useState, useEffect } from 'react';
import { ProgressMonitor } from '../components/ProgressMonitor';
import WebSocketManager from '../services/websocketManager';
import ProgressBuffer from '../services/progressBuffer';
import apiClient from '../services/apiClient';
import './ProgressMonitorDemo.css';

// 模拟数据生成
const generateMockProgress = (current: any) => {
  const itemsIncrement = Math.floor(Math.random() * 10) + 5;
  const pageIncrement = Math.random() > 0.8 ? 1 : 0;
  
  return {
    itemsCollected: Math.min(current.itemsCollected + itemsIncrement, current.targetItems),
    currentPage: Math.min(current.currentPage + pageIncrement, current.totalPages),
    totalPages: current.totalPages,
    targetItems: current.targetItems,
    currentKeyword: current.currentKeyword,
    keywordIndex: current.keywordIndex,
    totalKeywords: current.totalKeywords,
    startTime: current.startTime,
    errors: Math.random() > 0.95 ? [...(current.errors || []), {
      timestamp: Date.now(),
      message: '网络请求超时，正在重试...',
      type: Math.random() > 0.5 ? 'warning' : 'error' as const
    }] : current.errors
  };
};

const ProgressMonitorDemo: React.FC = () => {
  const [status, setStatus] = useState<'idle' | 'running' | 'paused' | 'completed' | 'error'>('idle');
  const [progress, setProgress] = useState({
    itemsCollected: 0,
    targetItems: 1000,
    currentPage: 0,
    totalPages: 50,
    currentKeyword: '女装',
    keywordIndex: 1,
    totalKeywords: 3,
    startTime: Date.now(),
    errors: [] as any[]
  });

  const [wsManager, setWsManager] = useState<WebSocketManager | null>(null);
  const [progressBuffer] = useState(() => new ProgressBuffer({
    updateInterval: 100,
    smoothingFactor: 0.3
  }));

  const [demoMode, setDemoMode] = useState(true);
  const [mockInterval, setMockInterval] = useState<number | null>(null);

  // 设置进度缓冲监听
  useEffect(() => {
    const unsubscribe = progressBuffer.subscribe((update) => {
      setProgress(prev => ({
        ...prev,
        itemsCollected: update.itemsCollected,
        currentPage: update.currentPage,
        totalPages: update.totalPages,
        currentKeyword: update.currentKeyword || prev.currentKeyword,
        keywordIndex: update.keywordIndex || prev.keywordIndex,
        totalKeywords: update.totalKeywords || prev.totalKeywords
      }));
    });

    return () => {
      unsubscribe();
      progressBuffer.destroy();
    };
  }, [progressBuffer]);

  // 模拟进度更新
  const startMockProgress = () => {
    if (mockInterval) return;

    const interval = window.setInterval(() => {
      setProgress(prev => {
        const newProgress = generateMockProgress(prev);
        
        // 检查是否完成
        if (newProgress.itemsCollected >= newProgress.targetItems || 
            newProgress.currentPage >= newProgress.totalPages) {
          stopMockProgress();
          setStatus('completed');
          return prev;
        }

        // 添加到缓冲区
        progressBuffer.addProgress({
          itemsCollected: newProgress.itemsCollected,
          currentPage: newProgress.currentPage,
          totalPages: newProgress.totalPages,
          currentKeyword: newProgress.currentKeyword,
          keywordIndex: newProgress.keywordIndex,
          totalKeywords: newProgress.totalKeywords
        });

        return newProgress;
      });
    }, 500);

    setMockInterval(interval);
  };

  const stopMockProgress = () => {
    if (mockInterval) {
      clearInterval(mockInterval);
      setMockInterval(null);
    }
  };

  // 连接真实WebSocket
  const connectWebSocket = async (taskId: string) => {
    const manager = new WebSocketManager({
      url: 'ws://localhost:8000/ws/crawl',
      taskId,
      callbacks: {
        onOpen: () => {
          console.log('WebSocket connected');
        },
        onProgress: (data) => {
          progressBuffer.addProgress(data);
        },
        onError: (error) => {
          setProgress(prev => ({
            ...prev,
            errors: [...(prev.errors || []), {
              timestamp: Date.now(),
              message: error.message || '未知错误',
              type: 'error'
            }]
          }));
        },
        onCompleted: () => {
          setStatus('completed');
        }
      }
    });

    manager.connect();
    setWsManager(manager);
  };

  // 开始爬取
  const handleStart = async () => {
    setStatus('running');
    setProgress(prev => ({
      ...prev,
      itemsCollected: 0,
      currentPage: 1,
      startTime: Date.now(),
      errors: []
    }));

    if (demoMode) {
      startMockProgress();
    } else {
      try {
        const result = await apiClient.startCrawl({
          keyword: '女装',
          targetCount: 1000,
          maxPages: 50,
          sortType: 'default'
        });

        if (result.taskId) {
          await connectWebSocket(result.taskId);
        }
      } catch (error: any) {
        setStatus('error');
        setProgress(prev => ({
          ...prev,
          errors: [{
            timestamp: Date.now(),
            message: error.message || '启动失败',
            type: 'error'
          }]
        }));
      }
    }
  };

  // 暂停爬取
  const handlePause = () => {
    setStatus('paused');
    if (demoMode) {
      stopMockProgress();
    } else if (wsManager) {
      apiClient.pauseCrawl();
    }
  };

  // 恢复爬取
  const handleResume = () => {
    setStatus('running');
    if (demoMode) {
      startMockProgress();
    } else if (wsManager) {
      apiClient.resumeCrawl();
    }
  };

  // 停止爬取
  const handleStop = () => {
    setStatus('idle');
    if (demoMode) {
      stopMockProgress();
      setProgress(prev => ({
        ...prev,
        itemsCollected: 0,
        currentPage: 0
      }));
    } else {
      wsManager?.close();
      apiClient.stopCrawl();
    }
  };

  // 重试
  const handleRetry = () => {
    handleStart();
  };

  // 清理
  useEffect(() => {
    return () => {
      stopMockProgress();
      wsManager?.close();
    };
  }, [wsManager]);

  return (
    <div className="progress-monitor-demo">
      <div className="demo-header">
        <h1>进度监控组件演示</h1>
        <div className="demo-controls">
          <label className="demo-mode-switch">
            <input
              type="checkbox"
              checked={demoMode}
              onChange={(e) => setDemoMode(e.target.checked)}
            />
            <span>演示模式</span>
          </label>
        </div>
      </div>

      <div className="demo-content">
        {/* 控制按钮 */}
        <div className="control-panel">
          <button
            className="control-button start"
            onClick={handleStart}
            disabled={status === 'running' || status === 'paused'}
          >
            开始爬取
          </button>
          <button
            className="control-button pause"
            onClick={handlePause}
            disabled={status !== 'running'}
          >
            暂停
          </button>
          <button
            className="control-button resume"
            onClick={handleResume}
            disabled={status !== 'paused'}
          >
            恢复
          </button>
          <button
            className="control-button stop"
            onClick={handleStop}
            disabled={status === 'idle' || status === 'completed'}
          >
            停止
          </button>
        </div>

        {/* 进度监控组件 */}
        <ProgressMonitor
          status={status}
          progress={progress}
          onRetry={handleRetry}
          onStop={handleStop}
        />

        {/* 统计信息 */}
        {status !== 'idle' && (
          <div className="demo-stats">
            <h3>实时统计</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">WebSocket状态:</span>
                <span className="stat-value">
                  {demoMode ? '模拟模式' : (wsManager?.getState().connected ? '已连接' : '未连接')}
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">缓冲区消息:</span>
                <span className="stat-value">
                  {progressBuffer.getStatistics().totalUpdates} 条
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">更新频率:</span>
                <span className="stat-value">
                  {progressBuffer.getStatistics().updatesPerSecond.toFixed(1)} 次/秒
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">平均速度:</span>
                <span className="stat-value">
                  {progressBuffer.getStatistics().averageItemsPerUpdate.toFixed(1)} 商品/更新
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressMonitorDemo;