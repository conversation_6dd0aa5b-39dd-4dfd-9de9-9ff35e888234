#!/usr/bin/env python3
"""
精确商品筛选器

提供基于关键词的精确商品匹配和筛选功能，支持多维度匹配和评分机制。
"""

import re
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class MatchResult:
    """增强型匹配结果"""
    score: float
    brand_score: float
    model_score: float  # 保持兼容性
    spec_score: float
    keyword_score: float
    product_score: float = 0.0  # 新增产品类型得分
    details: Dict[str, any] = None


class ProductFilter:
    """
    增强型精确商品筛选器
    
    功能：
    1. 基于搜索关键词对商品进行精确匹配
    2. 处理分散信息：品牌、型号、规格可能分布在标题不同位置
    3. 复用现有品牌识别系统的优势技术
    4. 支持RapidFuzz模糊匹配和多层次匹配
    """
    
    def __init__(self, config: Dict, brand_processor=None):
        """
        初始化增强型筛选器
        
        Args:
            config: 筛选器配置字典
            brand_processor: DataProcessor实例，用于复用品牌识别逻辑
        """
        self.enabled = config.get('enabled', False)
        self.match_threshold = config.get('match_threshold', 0.7)
        self.strict_mode = config.get('strict_mode', False)
        
        # 权重配置
        weights = config.get('weights', {})
        self.brand_weight = weights.get('brand', 0.40)  # 提高品牌权重
        self.product_weight = weights.get('product_type', 0.25)  # 新增产品类型权重
        self.spec_weight = weights.get('specification', 0.25)
        self.keyword_weight = weights.get('keyword', 0.10)  # 降低通用关键词权重
        
        # 算法配置
        algorithm = config.get('algorithm', {})
        self.fuzzy_threshold = algorithm.get('fuzzy_threshold', 0.8)
        self.number_tolerance = algorithm.get('number_tolerance', 0.05)
        self.unit_normalization = algorithm.get('unit_normalization', True)
        self.case_sensitive = algorithm.get('case_sensitive', False)
        
        # 调试配置
        debug = config.get('debug', {})
        self.debug_enabled = debug.get('enabled', False)
        self.log_scores = debug.get('log_scores', False)
        self.save_filtered = debug.get('save_filtered', False)
        
        # 品牌处理器（复用现有逻辑）
        self.brand_processor = brand_processor
        self.rapidfuzz_available = self._check_rapidfuzz_availability()
        
        # 编译常用正则表达式
        self._compile_patterns()
        
        logger.info(f"Enhanced ProductFilter initialized: enabled={self.enabled}, "
                   f"threshold={self.match_threshold}, rapidfuzz={self.rapidfuzz_available}")
    
    def _check_rapidfuzz_availability(self):
        """检查RapidFuzz可用性"""
        try:
            if self.brand_processor and hasattr(self.brand_processor, '_extract_brand_with_rapidfuzz'):
                import rapidfuzz
                return True
        except ImportError:
            pass
        return False
    
    def _compile_patterns(self):
        """编译常用的正则表达式模式"""
        # 产品类型模式 - 增强版，添加更多产品类型和排除词
        self.product_patterns = {
            '冰箱': re.compile(r'(?:冰箱|电冰箱|refrigerator)(?!贴|罩|垫|架|锁|膜)', re.IGNORECASE),
            '洗衣机': re.compile(r'(?:洗衣机|washing machine)(?!罩|架|底座|脚垫)', re.IGNORECASE),
            '空调': re.compile(r'(?:空调|air conditioner|AC)(?!罩|遥控|清洗)', re.IGNORECASE),
            '电视': re.compile(r'(?:电视|电视机|TV|television)(?!柜|架|罩|支架)', re.IGNORECASE),
            '微波炉': re.compile(r'(?:微波炉|microwave)(?!架|罩|手套)', re.IGNORECASE),
            '热水器': re.compile(r'(?:热水器|water heater)(?!配件|管|阀)', re.IGNORECASE),
            '油烟机': re.compile(r'(?:油烟机|抽油烟机|range hood)(?!清洗|配件)', re.IGNORECASE),
            '燃气灶': re.compile(r'(?:燃气灶|煤气灶|灶具)(?!架|配件)', re.IGNORECASE),
            '消毒柜': re.compile(r'消毒柜(?!配件)', re.IGNORECASE),
            '烤箱': re.compile(r'(?:烤箱|电烤箱)(?!手套|烤盘)', re.IGNORECASE),
            '血糖仪': re.compile(r'血糖仪|血糖测试', re.IGNORECASE),
            '血压计': re.compile(r'血压计|血压测量', re.IGNORECASE),
        }
        
        # 产品类型排除词（配件、周边产品）
        self.accessory_keywords = [
            '配件', '零件', '滤芯', '滤网', '遥控', '支架', '底座', 
            '罩', '套', '垫', '贴', '膜', '架', '清洗剂', '清洁剂'
        ]
        
        # 规格模式（复用并增强）
        self.spec_patterns = {
            'capacity': re.compile(r'(\d+(?:\.\d+)?)\s*[升LlL]', re.IGNORECASE),
            'weight': re.compile(r'(\d+(?:\.\d+)?)\s*[公斤kgKG]', re.IGNORECASE),
            'size': re.compile(r'(\d+)\s*[寸英寸inch]', re.IGNORECASE),
            'power': re.compile(r'(\d+(?:\.\d+)?)\s*[匹HP]', re.IGNORECASE),
            'volume': re.compile(r'(\d+(?:\.\d+)?)\s*[立方米m³]', re.IGNORECASE),
        }
        
        # 型号模式（增强版）
        self.model_patterns = [
            re.compile(r'([A-Z]+\d+[A-Z]*)', re.IGNORECASE),  # 字母+数字+字母
            re.compile(r'(\d+[A-Z]+)', re.IGNORECASE),        # 数字+字母
            re.compile(r'([A-Z]+\-\d+)', re.IGNORECASE),      # 字母-数字
            re.compile(r'(\d{3,4}[A-Z]*)', re.IGNORECASE),    # 3-4位数字+字母
        ]
        
        # 营销标签清理模式（复用DataProcessor逻辑）
        self.marketing_patterns = [
            re.compile(r'【[^】]*】'),  # 【百亿补贴】等
            re.compile(r'\[[^\]]*\]'),  # [限时特价]等
            re.compile(r'[⭐⭐⭐]+'),    # 星号
            re.compile(r'[❤💖]+'),    # 爱心
            re.compile(r'[🔥💥⚡]+'),   # 火焰等emoji
            re.compile(r'限时(?![^，。]*[美海格小华云石追戴])'),  # 限时但不包含品牌
            re.compile(r'特价(?![^，。]*[美海格小华云石追戴])'),  # 特价但不包含品牌
            re.compile(r'包邮(?![^，。]*[美海格小华云石追戴])'),  # 包邮但不包含品牌
        ]
    
    def filter_products(self, products: List[Dict], search_keyword: str) -> List[Dict]:
        """
        增强型商品筛选
        
        Args:
            products: 商品列表
            search_keyword: 搜索关键词
            
        Returns:
            筛选后的商品列表
        """
        if not self.enabled:
            if self.debug_enabled:
                logger.debug("Enhanced ProductFilter is disabled, returning all products")
            return products
        
        if not search_keyword or not products:
            return products
        
        # 解析搜索关键词的组成部分
        keyword_components = self._parse_keyword_components(search_keyword)
        
        if self.debug_enabled:
            logger.debug(f"Parsed keyword components: {keyword_components}")
        
        filtered_products = []
        filtered_out = []
        
        for product in products:
            # 提取商品信息组成部分
            product_components = self._extract_product_components(product)
            
            # 计算分散信息匹配度
            match_result = self._calculate_distributed_match(keyword_components, product_components)
            
            if match_result.score >= self.match_threshold:
                # 添加增强匹配信息
                product['enhanced_match_score'] = match_result.score
                product['match_components'] = {
                    'brand_score': match_result.brand_score,
                    'product_score': match_result.product_score,
                    'spec_score': match_result.spec_score,
                    'keyword_score': match_result.keyword_score,
                    'keyword_components': keyword_components,
                    'product_components': product_components
                }
                filtered_products.append(product)
                
                if self.log_scores:
                    logger.info(f"ENHANCED MATCH: {product.get('goods_name', '')[:50]}... "
                              f"Score: {match_result.score:.3f} "
                              f"(B:{match_result.brand_score:.2f} "
                              f"P:{match_result.product_score:.2f} "
                              f"S:{match_result.spec_score:.2f} "
                              f"K:{match_result.keyword_score:.2f})")
            else:
                filtered_out.append(product)
                
                if self.log_scores:
                    logger.debug(f"ENHANCED FILTER: {product.get('goods_name', '')[:50]}... "
                               f"Score: {match_result.score:.3f}")
        
        # 记录筛选统计
        total_count = len(products)
        filtered_count = len(filtered_products)
        filter_rate = (total_count - filtered_count) / total_count if total_count > 0 else 0
        
        logger.info(f"Enhanced ProductFilter: {filtered_count}/{total_count} products matched "
                   f"(filtered {filter_rate:.1%}) for keyword: {search_keyword}")
        
        return filtered_products
    
    def _parse_keyword_components(self, keyword: str) -> Dict:
        """
        解析搜索关键词的各个组成部分
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            解析后的组件字典
        """
        components = {
            'original': keyword,
            'brand': None,
            'product_type': None,
            'specifications': {},
            'models': [],
            'keywords': [],
            'cleaned_text': keyword
        }
        
        # 清理营销标签
        cleaned_keyword = self._clean_marketing_tags(keyword)
        components['cleaned_text'] = cleaned_keyword
        
        # 提取品牌（使用现有品牌识别逻辑）
        if self.brand_processor:
            try:
                # 使用DataProcessor的品牌识别能力
                brand = self.brand_processor._extract_brand_name_from_goods_name(cleaned_keyword, "")
                if brand:
                    components['brand'] = brand
            except Exception as e:
                logger.warning(f"Brand extraction failed: {e}")
        
        # 先提取规格信息（在产品类型识别之前）
        for spec_type, pattern in self.spec_patterns.items():
            matches = pattern.findall(cleaned_keyword)
            if matches:
                components['specifications'][spec_type] = [float(m) for m in matches]
        
        # 提取产品类型（增强版：排除配件）
        # 先检查是否为配件
        is_accessory = any(keyword in cleaned_keyword for keyword in self.accessory_keywords)
        
        if not is_accessory:
            for product_type, pattern in self.product_patterns.items():
                if pattern.search(cleaned_keyword):
                    components['product_type'] = product_type
                    break

        # 智能产品类型推断（基于规格信息和型号）
        if not components['product_type']:
            # 1. 根据规格推断产品类型
            if 'capacity' in components['specifications']:
                capacity_values = components['specifications']['capacity']
                if capacity_values:
                    max_capacity = max(capacity_values)
                    # 根据容量范围推断产品类型
                    if 100 <= max_capacity <= 800:  # 100-800升通常是冰箱
                        components['product_type'] = '冰箱'
                        if self.debug_enabled:
                            logger.debug(f"根据容量{max_capacity}L推断产品类型为冰箱")
                    elif 5 <= max_capacity <= 20:   # 5-20升通常是洗衣机
                        components['product_type'] = '洗衣机'
                        if self.debug_enabled:
                            logger.debug(f"根据容量{max_capacity}L推断产品类型为洗衣机")
            
            # 2. 根据型号推断产品类型（如475通常是冰箱容量）
            if not components['product_type'] and components['models']:
                for model in components['models']:
                    # 如果型号是纯数字且在冰箱容量范围内
                    if model.isdigit():
                        capacity = int(model)
                        if 100 <= capacity <= 800:  # 冰箱容量范围
                            components['product_type'] = '冰箱'
                            if self.debug_enabled:
                                logger.debug(f"根据型号{model}推断产品类型为冰箱")
                            break
        

        
        # 提取型号
        for pattern in self.model_patterns:
            models = pattern.findall(cleaned_keyword)
            components['models'].extend(models)
        
        # 提取关键词
        words = re.findall(r'[\u4e00-\u9fa5]+|[A-Za-z]+', cleaned_keyword)
        components['keywords'] = [w for w in words if len(w) >= 2]
        
        return components
    
    def _extract_product_components(self, product: Dict) -> Dict:
        """
        提取商品的各个组成部分
        
        Args:
            product: 商品信息字典
            
        Returns:
            商品组件字典
        """
        goods_name = product.get('goods_name', '')
        brand_name = product.get('brand_name', '')
        
        components = {
            'brand': brand_name,
            'product_type': None,
            'specifications': {},
            'models': [],
            'keywords': [],
            'cleaned_text': goods_name
        }
        
        # 使用DataProcessor的文本清理逻辑
        if self.brand_processor and hasattr(self.brand_processor, '_clean_goods_name_for_brand_extraction'):
            try:
                cleaned_name = self.brand_processor._clean_goods_name_for_brand_extraction(goods_name)
                components['cleaned_text'] = cleaned_name
            except Exception as e:
                logger.warning(f"Text cleaning failed: {e}")
                cleaned_name = self._clean_marketing_tags(goods_name)
                components['cleaned_text'] = cleaned_name
        else:
            cleaned_name = self._clean_marketing_tags(goods_name)
            components['cleaned_text'] = cleaned_name
        
        # 如果没有品牌信息，尝试从商品名称中提取
        if not components['brand'] and self.brand_processor:
            try:
                extracted_brand = self.brand_processor._extract_brand_with_rapidfuzz(cleaned_name, "")
                if extracted_brand:
                    components['brand'] = extracted_brand
            except Exception as e:
                logger.warning(f"Brand extraction from goods name failed: {e}")
        
        # 提取产品类型（增强版：排除配件）
        # 先检查是否为配件
        is_accessory = any(keyword in cleaned_name for keyword in self.accessory_keywords)
        
        if not is_accessory:
            for product_type, pattern in self.product_patterns.items():
                if pattern.search(cleaned_name):
                    components['product_type'] = product_type
                    break
        
        # 提取规格信息
        for spec_type, pattern in self.spec_patterns.items():
            matches = pattern.findall(cleaned_name)
            if matches:
                components['specifications'][spec_type] = [float(m) for m in matches]
        
        # 提取型号
        for pattern in self.model_patterns:
            models = pattern.findall(cleaned_name)
            components['models'].extend(models)
        
        # 提取关键词
        words = re.findall(r'[\u4e00-\u9fa5]+|[A-Za-z]+', cleaned_name)
        components['keywords'] = [w for w in words if len(w) >= 2]
        
        return components
    
    def _clean_marketing_tags(self, text: str) -> str:
        """清理营销标签"""
        cleaned_text = text
        for pattern in self.marketing_patterns:
            cleaned_text = pattern.sub(' ', cleaned_text)
        
        # 移除多余空格
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        return cleaned_text
    
    def _calculate_distributed_match(self, keyword_components: Dict, product_components: Dict) -> MatchResult:
        """
        计算分散信息的匹配度
        
        Args:
            keyword_components: 关键词组件
            product_components: 商品组件
            
        Returns:
            匹配结果对象
        """
        # 品牌匹配得分
        brand_score = self._calculate_brand_match_score(
            keyword_components.get('brand'), 
            product_components.get('brand')
        )
        
        # 产品类型匹配得分
        product_score = self._calculate_product_type_score(
            keyword_components.get('product_type'),
            product_components.get('product_type')
        )
        
        # 规格匹配得分
        spec_score = self._calculate_specification_match_score(
            keyword_components.get('specifications', {}),
            product_components.get('specifications', {})
        )
        
        # 关键词匹配得分
        keyword_score = self._calculate_keyword_match_score(
            keyword_components.get('keywords', []),
            product_components.get('keywords', [])
        )
        
        # 加权计算总分
        total_score = (
            brand_score * self.brand_weight +
            product_score * self.product_weight +
            spec_score * self.spec_weight +
            keyword_score * self.keyword_weight
        )
        
        # 严格模式检查和产品类型验证（改进版：更灵活的处理）
        if self.strict_mode:
            required_components = ['brand', 'product_type']
            for component in required_components:
                if keyword_components.get(component) and not product_components.get(component):
                    total_score *= 0.5  # 缺少必需组件时降低分数

        # 产品类型不匹配的改进处理
        keyword_product_type = keyword_components.get('product_type')
        product_product_type = product_components.get('product_type')

        # 如果关键词推断出了产品类型（如从475推断出冰箱）
        # 而商品的产品类型不匹配，应该降低分数但不完全拒绝
        if keyword_product_type and product_product_type and keyword_product_type != product_product_type:
            # 产品类型明确不匹配时，降低分数但不完全拒绝
            # 考虑到可能存在分类错误或边界情况
            total_score *= 0.2  # 降低到20%，而不是0
            if self.debug_enabled:
                logger.debug(f"产品类型不匹配: 关键词={keyword_product_type}, 商品={product_product_type}, 降低分数至{total_score:.3f}")
        
        # 额外检查：如果关键词有产品类型，但得分仍然较高，需要再次确认
        elif keyword_product_type and total_score >= self.match_threshold:
            # 确保产品类型得分占有足够权重
            if product_score < 0.3:  # 产品类型得分低于0.3说明匹配度低
                total_score *= 0.6  # 适度降低总分，避免误匹配
                if self.debug_enabled:
                    logger.debug(f"产品类型得分过低，适度降低总分: {total_score:.3f}")
        
        return MatchResult(
            score=total_score,
            brand_score=brand_score,
            model_score=0.0,  # 保持兼容性
            spec_score=spec_score,
            keyword_score=keyword_score,
            details={
                'product_score': product_score,
                'keyword_components': keyword_components,
                'product_components': product_components
            }
        )
    
    def _calculate_brand_match_score(self, keyword_brand: str, product_brand: str) -> float:
        """计算品牌匹配得分 - 改进版，支持主品牌与子品牌的合理匹配"""
        if not keyword_brand or not product_brand:
            return 0.5 if not keyword_brand else 0.0  # 没有品牌要求时给中等分
        
        # 精确匹配
        if keyword_brand == product_brand:
            return 1.0
        
        # 特殊处理：子品牌与主品牌的关系
        # 定义已知的主品牌-子品牌映射
        sub_brand_mapping = {
            '海尔': ['统帅', '卡萨帝', 'Leader', 'Casarte'],
            '美的': ['小天鹅', 'COLMO', '华凌', '布谷'],
            '格力': ['大松', '晶弘'],
            'TCL': ['雷鸟'],
            '海信': ['科龙', '容声', 'VIDAA'],
            '长虹': ['CHiQ'],
            '创维': ['酷开']
        }
        
        # 反向映射：子品牌到主品牌
        reverse_mapping = {}
        for main_brand, sub_brands in sub_brand_mapping.items():
            for sub_brand in sub_brands:
                reverse_mapping[sub_brand] = main_brand
        
        # 改进的匹配逻辑
        # 情况1：搜索主品牌，商品是该主品牌的子品牌 -> 给予较高分数（而不是拒绝）
        if keyword_brand in sub_brand_mapping:
            for sub_brand in sub_brand_mapping[keyword_brand]:
                if product_brand == sub_brand or sub_brand == product_brand:
                    if self.debug_enabled:
                        logger.debug(f"主品牌搜索匹配子品牌: 搜索{keyword_brand}, 商品品牌是{product_brand} -> 给予0.8分")
                    return 0.8  # 给予较高分数，因为子品牌属于主品牌
        
        # 情况2：搜索子品牌，商品是对应的主品牌 -> 给予中等分数
        if keyword_brand in reverse_mapping and product_brand == reverse_mapping[keyword_brand]:
            if self.debug_enabled:
                logger.debug(f"子品牌搜索匹配主品牌: 搜索{keyword_brand}, 商品品牌是{product_brand} -> 给予0.6分")
            return 0.6  # 给予中等分数
        
        # 使用RapidFuzz模糊匹配
        if self.rapidfuzz_available:
            try:
                from rapidfuzz import fuzz
                ratio = fuzz.ratio(keyword_brand, product_brand) / 100.0
                if ratio >= self.fuzzy_threshold:
                    return ratio
            except Exception as e:
                logger.warning(f"RapidFuzz matching failed: {e}")
        
        # 改进的包含匹配逻辑
        keyword_lower = keyword_brand.lower()
        product_lower = product_brand.lower()
        
        # 只有当关键词完整出现在产品品牌中，且没有额外的品牌信息时才给分
        if keyword_lower in product_lower:
            # 检查是否有额外的品牌信息（如"海尔统帅"中的"统帅"）
            extra_text = product_lower.replace(keyword_lower, '').strip()
            if extra_text and len(extra_text) >= 2:  # 有额外的品牌信息
                return 0.3  # 给低分，表示部分匹配
            else:
                return 0.8  # 完整包含，给高分
        
        return 0.0
    
    def _calculate_product_type_score(self, keyword_type: str, product_type: str) -> float:
        """计算产品类型匹配得分 - 改进版，更灵活的匹配"""
        # 如果关键词没有明确产品类型，但商品有产品类型
        if not keyword_type and product_type:
            # 给予中等分数，因为关键词可能是通用搜索
            return 0.5
        
        # 如果关键词有产品类型，但商品没有
        if keyword_type and not product_type:
            # 给予较低分数，但不完全拒绝（可能是分类标注问题）
            return 0.2

        # 如果都没有产品类型
        if not keyword_type and not product_type:
            return 0.5

        # 严格匹配：产品类型必须完全一致
        if keyword_type == product_type:
            return 1.0

        # 不匹配的情况：给予较低分数而不是0
        # 考虑到可能存在分类错误或边界情况
        if self.debug_enabled:
            logger.debug(f"产品类型不完全匹配: 关键词类型={keyword_type}, 商品类型={product_type}")
        return 0.1  # 给予很低的分数，但不是0
    
    def _calculate_specification_match_score(self, keyword_specs: Dict, product_specs: Dict) -> float:
        """计算规格匹配得分"""
        if not keyword_specs:
            return 0.5  # 没有规格要求时给中等分
        
        total_score = 0.0
        spec_count = 0
        
        for spec_type, keyword_values in keyword_specs.items():
            if spec_type in product_specs:
                product_values = product_specs[spec_type]
                
                # 计算最佳匹配
                best_match = 0.0
                for keyword_val in keyword_values:
                    for product_val in product_values:
                        # 计算相对误差
                        error = abs(keyword_val - product_val) / max(keyword_val, product_val)
                        if error <= self.number_tolerance:
                            best_match = max(best_match, 1.0)
                        elif error <= self.number_tolerance * 2:
                            best_match = max(best_match, 0.8)
                        elif error <= self.number_tolerance * 5:
                            best_match = max(best_match, 0.5)
                
                total_score += best_match
                spec_count += 1
            else:
                spec_count += 1  # 计入总数但得分为0
        
        return total_score / spec_count if spec_count > 0 else 0.5
    
    def _calculate_keyword_match_score(self, keyword_words: List[str], product_words: List[str]) -> float:
        """计算关键词匹配得分"""
        if not keyword_words:
            return 0.5
        
        if not product_words:
            return 0.0
        
        # 转换为小写进行匹配
        keyword_words_lower = [w.lower() for w in keyword_words]
        product_words_lower = [w.lower() for w in product_words]
        
        matched_count = 0
        for keyword_word in keyword_words_lower:
            if any(keyword_word in product_word or product_word in keyword_word 
                   for product_word in product_words_lower):
                matched_count += 1
        
        return matched_count / len(keyword_words) if keyword_words else 0.0


def create_product_filter(config: Dict, brand_processor=None) -> ProductFilter:
    """
    创建增强型商品筛选器实例
    
    Args:
        config: 配置字典
        brand_processor: DataProcessor实例，用于复用品牌识别逻辑
        
    Returns:
        ProductFilter实例
    """
    return ProductFilter(config, brand_processor)