/* 帮助弹窗样式 */
.help-modal {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-xl);
  animation: scaleIn var(--duration-300) var(--ease-spring);
  border: 1px solid var(--border-primary);
}

.help-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--border-primary);
}

.help-modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.help-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-6);
}

.shortcut-category {
  margin-bottom: var(--spacing-8);
}

.shortcut-category:last-child {
  margin-bottom: 0;
}

.shortcut-category h3 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.shortcut-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  transition: all var(--duration-200) var(--ease-out);
}

.shortcut-item:hover {
  background-color: var(--bg-tertiary);
  transform: translateX(4px);
}

.shortcut-description {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.shortcut-keys {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--bg-card);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-base);
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.help-modal-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.help-modal-footer p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* 深色模式调整 */
[data-theme="dark"] .shortcut-keys {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-600);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 响应式 */
@media (max-width: 640px) {
  .help-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .help-modal-header,
  .help-modal-content {
    padding: var(--spacing-4);
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
}