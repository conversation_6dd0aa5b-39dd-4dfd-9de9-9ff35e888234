{"fix_timestamp": "1753861701.0940373", "conflicts_detected": {"duplicate_brand_ids": {"奥克斯": {"ids": ["14", "848920"], "count": 2, "main_id": "14"}, "长虹": {"ids": ["228", "99613", "884800"], "count": 3, "main_id": "228"}, "海尔": {"ids": ["234", "17168", "1617760", "10123633"], "count": 4, "main_id": "234"}, "小米": {"ids": ["439", "15143", "15753", "98893", "11746746", "12285127"], "count": 6, "main_id": "439"}, "志高": {"ids": ["465", "1342760"], "count": 2, "main_id": "465"}, "新飞": {"ids": ["3840", "1109279"], "count": 2, "main_id": "3840"}, "扬子": {"ids": ["4875", "81944", "371241"], "count": 3, "main_id": "4875"}, "荣事达": {"ids": ["8579", "306507"], "count": 2, "main_id": "8579"}, "Candara": {"ids": ["1049165", "11381594"], "count": 2, "main_id": "1049165"}}, "invalid_brand_names": {"89735": "风机盘管", "471922": "骆驼冷暖两用", "521032": "无线迷你内衣", "632291": "小神童双开门冰箱", "753345": "巨无霸老式半自动", "1120512": "樱花雪电热热水器", "1142792": "热水器电家用洗澡", "1413330": "凡妻迷你", "1459962": "冷风机省电", "1659489": "品牌空调挂机冷暖", "1665584": "变频空调挂机大", "1710499": "挂式", "11912743": "冷暖两用智能移动"}, "sub_brand_conflicts": {}, "statistics": {"total_mappings": 67, "duplicate_brands": 9, "invalid_names": 13, "sub_brand_conflicts": 0, "affected_ids": 27}}, "changes_applied": {"removed_mappings": [{"brand_id": "89735", "brand_name": "风机盘管", "reason": "invalid_brand_name"}, {"brand_id": "471922", "brand_name": "骆驼冷暖两用", "reason": "invalid_brand_name"}, {"brand_id": "521032", "brand_name": "无线迷你内衣", "reason": "invalid_brand_name"}, {"brand_id": "632291", "brand_name": "小神童双开门冰箱", "reason": "invalid_brand_name"}, {"brand_id": "753345", "brand_name": "巨无霸老式半自动", "reason": "invalid_brand_name"}, {"brand_id": "1120512", "brand_name": "樱花雪电热热水器", "reason": "invalid_brand_name"}, {"brand_id": "1142792", "brand_name": "热水器电家用洗澡", "reason": "invalid_brand_name"}, {"brand_id": "1413330", "brand_name": "凡妻迷你", "reason": "invalid_brand_name"}, {"brand_id": "1459962", "brand_name": "冷风机省电", "reason": "invalid_brand_name"}, {"brand_id": "1659489", "brand_name": "品牌空调挂机冷暖", "reason": "invalid_brand_name"}, {"brand_id": "1665584", "brand_name": "变频空调挂机大", "reason": "invalid_brand_name"}, {"brand_id": "1710499", "brand_name": "挂式", "reason": "invalid_brand_name"}, {"brand_id": "11912743", "brand_name": "冷暖两用智能移动", "reason": "invalid_brand_name"}], "redirected_mappings": [{"from_id": "15143", "to_id": "439", "brand_name": "小米"}, {"from_id": "15753", "to_id": "439", "brand_name": "小米"}, {"from_id": "17168", "to_id": "234", "brand_name": "海尔"}, {"from_id": "81944", "to_id": "4875", "brand_name": "扬子"}, {"from_id": "98893", "to_id": "439", "brand_name": "小米"}, {"from_id": "99613", "to_id": "228", "brand_name": "长虹"}, {"from_id": "306507", "to_id": "8579", "brand_name": "荣事达"}, {"from_id": "371241", "to_id": "4875", "brand_name": "扬子"}, {"from_id": "848920", "to_id": "14", "brand_name": "奥克斯"}, {"from_id": "884800", "to_id": "228", "brand_name": "长虹"}, {"from_id": "1109279", "to_id": "3840", "brand_name": "新飞"}, {"from_id": "1342760", "to_id": "465", "brand_name": "志高"}, {"from_id": "1617760", "to_id": "234", "brand_name": "海尔"}, {"from_id": "10123633", "to_id": "234", "brand_name": "海尔"}, {"from_id": "11746746", "to_id": "439", "brand_name": "小米"}, {"from_id": "12285127", "to_id": "439", "brand_name": "小米"}], "corrected_mappings": []}, "summary": {"before_count": 67, "after_count": 38, "removed_count": 29, "conflicts_resolved": 9}}