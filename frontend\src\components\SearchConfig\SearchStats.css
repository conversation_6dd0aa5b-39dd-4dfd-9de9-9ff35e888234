.search-stats {
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(238, 77, 45, 0.03) 0%, rgba(238, 77, 45, 0.01) 100%);
  border-radius: 12px;
  border: 1px solid rgba(238, 77, 45, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--surface);
  border-radius: 10px;
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.blue {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.stat-icon.green {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.stat-icon.purple {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}

.stat-icon.orange {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stats-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
  border-radius: 8px;
  color: #ff9800;
  font-size: 13px;
}

.stats-warning svg {
  flex-shrink: 0;
}

/* 暗色主题适配 */
[data-theme="dark"] .search-stats {
  background: linear-gradient(135deg, rgba(255, 104, 84, 0.08) 0%, rgba(255, 104, 84, 0.03) 100%);
}

[data-theme="dark"] .stat-card {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stats-warning {
  background: rgba(255, 152, 0, 0.15);
  border-color: rgba(255, 152, 0, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .stat-card {
    padding: 10px;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
  }
  
  .stat-value {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}