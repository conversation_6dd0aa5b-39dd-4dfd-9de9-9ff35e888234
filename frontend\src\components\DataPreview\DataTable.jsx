import React, { useState, useMemo, useCallback, useRef } from 'react'
import { FixedSizeList as List } from 'react-window'
import InfiniteLoader from 'react-window-infinite-loader'
import clsx from 'clsx'
import './DataTable.css'

const ITEM_HEIGHT = 80
const OVERSCAN_COUNT = 5

export default function DataTable({ 
  data = [], 
  loading = false,
  hasMore = false,
  onLoadMore = () => {},
  onItemClick = () => {},
  selectedItems = [],
  onSelectionChange = () => {},
  sortConfig = { key: null, direction: 'asc' },
  onSort = () => {},
  filters = {},
  onFilter = () => {}
}) {
  const listRef = useRef()
  const [hoveredIndex, setHoveredIndex] = useState(null)

  // 格式化价格
  const formatPrice = useCallback((price) => {
    return `¥${parseFloat(price).toFixed(2)}`
  }, [])

  // 格式化销量
  const formatSales = useCallback((sales) => {
    if (sales >= 10000) {
      return `${(sales / 10000).toFixed(1)}万+`
    }
    if (sales >= 1000) {
      return `${(sales / 1000).toFixed(1)}k+`
    }
    return sales.toString()
  }, [])

  // 处理排序
  const handleSort = useCallback((key) => {
    const direction = sortConfig.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc'
    onSort({ key, direction })
  }, [sortConfig, onSort])

  // 处理选择
  const handleSelect = useCallback((item, e) => {
    e.stopPropagation()
    const isSelected = selectedItems.some(selected => selected.id === item.id)
    if (isSelected) {
      onSelectionChange(selectedItems.filter(selected => selected.id !== item.id))
    } else {
      onSelectionChange([...selectedItems, item])
    }
  }, [selectedItems, onSelectionChange])

  // 全选/取消全选
  const handleSelectAll = useCallback(() => {
    if (selectedItems.length === data.length) {
      onSelectionChange([])
    } else {
      onSelectionChange([...data])
    }
  }, [data, selectedItems, onSelectionChange])

  // 排序后的数据
  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1
      }
      return aValue < bValue ? 1 : -1
    })
  }, [data, sortConfig])

  // 是否加载更多
  const isItemLoaded = useCallback((index) => {
    return !hasMore || index < sortedData.length
  }, [hasMore, sortedData.length])

  // 加载更多数据
  const loadMoreItems = useCallback(() => {
    if (!loading && hasMore) {
      return onLoadMore()
    }
    return Promise.resolve()
  }, [loading, hasMore, onLoadMore])

  // 渲染行
  const Row = useCallback(({ index, style }) => {
    if (!isItemLoaded(index)) {
      return (
        <div style={style} className="table-row loading-row">
          <div className="loading-cell">加载中...</div>
        </div>
      )
    }

    const item = sortedData[index]
    const isSelected = selectedItems.some(selected => selected.id === item.id)
    const isHovered = hoveredIndex === index

    return (
      <div
        style={style}
        className={clsx('table-row', {
          'selected': isSelected,
          'hovered': isHovered
        })}
        onClick={() => onItemClick(item)}
        onMouseEnter={() => setHoveredIndex(index)}
        onMouseLeave={() => setHoveredIndex(null)}
      >
        <div className="table-cell checkbox-cell">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => handleSelect(item, e)}
            onClick={(e) => e.stopPropagation()}
          />
        </div>
        <div className="table-cell image-cell">
          <img 
            src={item.image_url || item.thumb_url} 
            alt={item.goods_name}
            loading="lazy"
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/60x60'
            }}
          />
        </div>
        <div className="table-cell name-cell">
          <div className="product-name">{item.goods_name}</div>
          {item.brand_name && (
            <div className="product-brand">{item.brand_name}</div>
          )}
        </div>
        <div className="table-cell price-cell">
          <div className="product-price">{formatPrice(item.price)}</div>
          {item.coupon_price && item.coupon_price < item.price && (
            <div className="coupon-price">券后 {formatPrice(item.coupon_price)}</div>
          )}
        </div>
        <div className="table-cell sales-cell">
          <div className="product-sales">
            <span className="sales-number">{formatSales(item.sales)}</span>
            <span className="sales-label">已售</span>
          </div>
        </div>
        <div className="table-cell shop-cell">
          <div className="shop-name">{item.shop_name}</div>
          {item.merchant_type_name && (
            <div className="shop-type">{item.merchant_type_name}</div>
          )}
        </div>
      </div>
    )
  }, [sortedData, selectedItems, hoveredIndex, isItemLoaded, formatPrice, formatSales, handleSelect, onItemClick])

  // 表头渲染
  const renderHeader = () => (
    <div className="table-header-row">
      <div className="table-header-cell checkbox-cell">
        <input
          type="checkbox"
          checked={selectedItems.length === data.length && data.length > 0}
          indeterminate={selectedItems.length > 0 && selectedItems.length < data.length}
          onChange={handleSelectAll}
        />
      </div>
      <div className="table-header-cell image-cell">图片</div>
      <div 
        className={clsx('table-header-cell name-cell sortable', {
          'sorted': sortConfig.key === 'goods_name'
        })}
        onClick={() => handleSort('goods_name')}
      >
        商品名称
        {sortConfig.key === 'goods_name' && (
          <span className={`sort-icon ${sortConfig.direction}`}>
            {sortConfig.direction === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
      <div 
        className={clsx('table-header-cell price-cell sortable', {
          'sorted': sortConfig.key === 'price'
        })}
        onClick={() => handleSort('price')}
      >
        价格
        {sortConfig.key === 'price' && (
          <span className={`sort-icon ${sortConfig.direction}`}>
            {sortConfig.direction === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
      <div 
        className={clsx('table-header-cell sales-cell sortable', {
          'sorted': sortConfig.key === 'sales'
        })}
        onClick={() => handleSort('sales')}
      >
        销量
        {sortConfig.key === 'sales' && (
          <span className={`sort-icon ${sortConfig.direction}`}>
            {sortConfig.direction === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
      <div className="table-header-cell shop-cell">店铺</div>
    </div>
  )

  // 空状态
  if (!loading && data.length === 0) {
    return (
      <div className="data-table-container">
        <div className="empty-state">
          <div className="empty-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
              <circle cx="32" cy="32" r="30" stroke="currentColor" strokeWidth="2" strokeDasharray="4 4"/>
              <rect x="20" y="20" width="24" height="24" rx="4" stroke="currentColor" strokeWidth="2"/>
              <path d="M28 36l4-4 4 4m-8-8l2-2m6 2l4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <h3 className="empty-title">暂无数据</h3>
          <p className="empty-description">开始爬取后，商品数据将实时显示在这里</p>
        </div>
      </div>
    )
  }

  const itemCount = hasMore ? sortedData.length + 1 : sortedData.length

  return (
    <div className="data-table-container">
      {renderHeader()}
      <div className="table-body">
        <InfiniteLoader
          isItemLoaded={isItemLoaded}
          itemCount={itemCount}
          loadMoreItems={loadMoreItems}
        >
          {({ onItemsRendered, ref }) => (
            <List
              ref={(list) => {
                ref(list)
                listRef.current = list
              }}
              height={600}
              itemCount={itemCount}
              itemSize={ITEM_HEIGHT}
              onItemsRendered={onItemsRendered}
              overscanCount={OVERSCAN_COUNT}
              width="100%"
            >
              {Row}
            </List>
          )}
        </InfiniteLoader>
      </div>
    </div>
  )
}