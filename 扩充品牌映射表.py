# 扩充后的品牌映射表 - 2025年最新版本
# 包含电器类品牌和骑行服装品牌的完整映射关系

def get_extended_brand_mapping():
    """
    获取扩充后的品牌映射表
    
    分类：
    1. 电器类品牌（家用电器 + 数码电器）
    2. 骑行服装品牌
    
    格式：英文名/变体 → 标准中文名
    """
    
    extended_brand_mapping = {
        
        # ==================== 电器类品牌 ====================
        
        # === 大家电品牌（冰箱、洗衣机、空调、电视） ===
        
        # 海尔集团（中国）- 母品牌
        "haier": "海尔", "海尔": "海尔", "HAIER": "海尔", "Haier": "海尔",
        
        # 海尔子品牌
        "casarte": "卡萨帝", "卡萨帝": "卡萨帝", "CASARTE": "卡萨帝", "Casarte": "卡萨帝",
        "leader": "统帅", "统帅": "统帅", "LEADER": "统帅", "Leader": "统帅",
        "aqua": "AQUA", "AQUA": "AQUA", "Aqua": "AQUA",  # 海尔收购的日本品牌
        
        # 美的集团（中国）- 母品牌
        "midea": "美的", "美的": "美的", "MIDEA": "美的", "Midea": "美的",
        
        # 美的子品牌
        "littleswan": "小天鹅", "小天鹅": "小天鹅", "LITTLESWAN": "小天鹅", "LittleSwan": "小天鹅",
        "colmo": "COLMO", "COLMO": "COLMO", "Colmo": "COLMO",  # 美的高端品牌
        "华凌": "华凌", "hualing": "华凌", "HUALING": "华凌", "Hualing": "华凌",
        "布谷": "布谷", "bugu": "布谷", "BUGU": "布谷", "Bugu": "布谷",
        "toshiba": "东芝", "东芝": "东芝", "TOSHIBA": "东芝", "Toshiba": "东芝",  # 美的收购
        
        # 格力集团（中国）- 母品牌
        "gree": "格力", "格力": "格力", "GREE": "格力", "Gree": "格力",
        
        # 格力子品牌
        "tosot": "大松", "大松": "大松", "TOSOT": "大松", "Tosot": "大松",
        "晶弘": "晶弘", "kinghome": "晶弘", "KINGHOME": "晶弘", "Kinghome": "晶弘",
        
        # TCL集团（中国）
        "tcl": "TCL", "TCL": "TCL", "Tcl": "TCL",
        "雷鸟": "雷鸟", "thunderbird": "雷鸟", "THUNDERBIRD": "雷鸟", "Thunderbird": "雷鸟",
        
        # 海信集团（中国）- 母品牌
        "hisense": "海信", "海信": "海信", "HISENSE": "海信", "Hisense": "海信",
        
        # 海信子品牌
        "kelon": "科龙", "科龙": "科龙", "KELON": "科龙", "Kelon": "科龙",
        "ronshen": "容声", "容声": "容声", "RONSHEN": "容声", "Ronshen": "容声",
        "vidaa": "VIDAA", "VIDAA": "VIDAA", "Vidaa": "VIDAA",  # 海信互联网电视品牌
        
        # 长虹集团（中国）
        "changhong": "长虹", "长虹": "长虹", "CHANGHONG": "长虹", "Changhong": "长虹",
        "chiq": "长虹", "CHiQ": "长虹", "CHIQ": "长虹", "ChiQ": "长虹",
        
        # 康佳集团（中国）
        "konka": "康佳", "康佳": "康佳", "KONKA": "康佳", "Konka": "康佳",
        
        # 创维集团（中国）
        "skyworth": "创维", "创维": "创维", "SKYWORTH": "创维", "Skyworth": "创维",
        "coocaa": "酷开", "酷开": "酷开", "COOCAA": "酷开", "Coocaa": "酷开",
        
        # 奥克斯集团（中国）
        "aux": "奥克斯", "奥克斯": "奥克斯", "AUX": "奥克斯", "Aux": "奥克斯",
        
        # 志高集团（中国）
        "chigo": "志高", "志高": "志高", "CHIGO": "志高", "Chigo": "志高",
        
        # 扬子集团（中国）
        "扬子": "扬子", "yangzi": "扬子", "YANGZI": "扬子", "Yangzi": "扬子",
        "扬子佳": "扬子", "yangzijia": "手子", "YANGZIJIA": "扬子",
        
        # 新飞集团（中国）
        "新飞": "新飞", "xinfei": "新飞", "XINFEI": "新飞", "Xinfei": "新飞",
        
        # 国际大家电品牌
        "siemens": "西门子", "西门子": "西门子", "SIEMENS": "西门子", "Siemens": "西门子",
        "bosch": "博世", "博世": "博世", "BOSCH": "博世", "Bosch": "博世",
        "panasonic": "松下", "松下": "松下", "PANASONIC": "松下", "Panasonic": "松下",
        "samsung": "三星", "三星": "三星", "SAMSUNG": "三星", "Samsung": "三星",
        "lg": "LG", "LG": "LG", "Lg": "LG", "lG": "LG",
        "whirlpool": "惠而浦", "惠而浦": "惠而浦", "WHIRLPOOL": "惠而浦", "Whirlpool": "惠而浦",
        "electrolux": "伊莱克斯", "伊莱克斯": "伊莱克斯", "ELECTROLUX": "伊莱克斯", "Electrolux": "伊莱克斯",
        "liebherr": "利勃海尔", "利勃海尔": "利勃海尔", "LIEBHERR": "利勃海尔", "Liebherr": "利勃海尔",
        
        # === 小家电品牌 ===
        
        # 九阳集团（中国）
        "joyoung": "九阳", "九阳": "九阳", "JOYOUNG": "九阳", "Joyoung": "九阳",
        
        # 苏泊尔集团（中国）
        "supor": "苏泊尔", "苏泊尔": "苏泊尔", "SUPOR": "苏泊尔", "Supor": "苏泊尔",
        
        # 小熊电器（中国）
        "bear": "小熊", "小熊": "小熊", "BEAR": "小熊", "Bear": "小熊",
        "小熊电器": "小熊", "bearelectric": "小熊", "BEARELECTRIC": "小熊",
        
        # 格兰仕集团（中国）
        "galanz": "格兰仕", "格兰仕": "格兰仕", "GALANZ": "格兰仕", "Galanz": "格兰仕",
        
        # 荣事达集团（中国）
        "royalstar": "荣事达", "荣事达": "荣事达", "ROYALSTAR": "荣事达", "Royalstar": "荣事达",
        
        # 利仁集团（中国）
        "liven": "利仁", "利仁": "利仁", "LIVEN": "利仁", "Liven": "利仁",
        
        # 德尔玛集团（中国）
        "deerma": "德尔玛", "德尔玛": "德尔玛", "DEERMA": "德尔玛", "Deerma": "德尔玛",
        
        # 摩飞电器（英国）
        "morphyrichards": "摩飞", "摩飞": "摩飞", "MORPHYRICHARDS": "摩飞", "MorphyRichards": "摩飞",
        "morphy": "摩飞", "MORPHY": "摩飞", "Morphy": "摩飞",
        
        # 北鼎集团（中国）
        "buydeem": "北鼎", "北鼎": "北鼎", "BUYDEEM": "北鼎", "Buydeem": "北鼎",
        
        # 小米生态链小家电
        "xiaomi": "小米", "小米": "小米", "XIAOMI": "小米", "Xiaomi": "小米",
        "mi": "小米", "MI": "小米", "Mi": "小米", "mI": "小米",
        "米家": "小米", "mijia": "小米", "MIJIA": "小米", "Mijia": "小米",
        "素士": "素士", "soocas": "素士", "SOOCAS": "素士", "Soocas": "素士",
        "云米": "云米", "viomi": "云米", "VIOMI": "云米", "Viomi": "云米",
        "追觅": "追觅", "dreame": "追觅", "DREAME": "追觅", "Dreame": "追觅",
        
        # 国际小家电品牌
        "philips": "飞利浦", "飞利浦": "飞利浦", "PHILIPS": "飞利浦", "Philips": "飞利浦",
        "dyson": "戴森", "戴森": "戴森", "DYSON": "戴森", "Dyson": "戴森",
        "braun": "博朗", "博朗": "博朗", "BRAUN": "博朗", "Braun": "博朗",
        "tefal": "特福", "特福": "特福", "TEFAL": "特福", "Tefal": "特福",
        "delonghi": "德龙", "德龙": "德龙", "DELONGHI": "德龙", "DeLonghi": "德龙",
        "zojirushi": "象印", "象印": "象印", "ZOJIRUSHI": "象印", "Zojirushi": "象印",
        "tiger": "虎牌", "虎牌": "虎牌", "TIGER": "虎牌", "Tiger": "虎牌",
        
        # === 数码电器品牌 ===
        
        # 手机品牌（中国）
        "huawei": "华为", "华为": "华为", "HUAWEI": "华为", "Huawei": "华为",
        "honor": "荣耀", "荣耀": "荣耀", "HONOR": "荣耀", "Honor": "荣耀",
        "oppo": "OPPO", "OPPO": "OPPO", "Oppo": "OPPO", "opPO": "OPPO",
        "vivo": "vivo", "vivo": "vivo", "VIVO": "vivo", "Vivo": "vivo",
        "oneplus": "一加", "一加": "一加", "ONEPLUS": "一加", "OnePlus": "一加",
        "realme": "真我", "真我": "真我", "REALME": "真我", "Realme": "真我",
        "meizu": "魅族", "魅族": "魅族", "MEIZU": "魅族", "Meizu": "魅族",
        "zte": "中兴", "中兴": "中兴", "ZTE": "中兴", "Zte": "中兴",
        "nubia": "努比亚", "努比亚": "努比亚", "NUBIA": "努比亚", "Nubia": "努比亚",
        
        # 国际手机品牌
        "apple": "苹果", "苹果": "苹果", "APPLE": "苹果", "Apple": "苹果",
        "iphone": "苹果", "iPhone": "苹果", "IPHONE": "苹果", "IPhone": "苹果",
        
        # 电脑品牌（中国）
        "lenovo": "联想", "联想": "联想", "LENOVO": "联想", "Lenovo": "联想",
        "thinkpad": "联想", "ThinkPad": "联想", "THINKPAD": "联想", "Thinkpad": "联想",
        "hasee": "神舟", "神舟": "神舟", "HASEE": "神舟", "Hasee": "神舟",
        "tongfang": "同方", "同方": "同方", "TONGFANG": "同方", "Tongfang": "同方",
        
        # 国际电脑品牌
        "dell": "戴尔", "戴尔": "戴尔", "DELL": "戴尔", "Dell": "戴尔",
        "hp": "惠普", "惠普": "惠普", "HP": "惠普", "Hp": "惠普",
        "asus": "华硕", "华硕": "华硕", "ASUS": "华硕", "Asus": "华硕",
        "acer": "宏碁", "宏碁": "宏碁", "ACER": "宏碁", "Acer": "宏碁",
        "msi": "微星", "微星": "微星", "MSI": "微星", "Msi": "微星",
        
        # ==================== 骑行服装品牌 ====================
        
        # === 中国骑行服装品牌（占比70%+） ===
        
        # 森地客（中国骑行装备领军品牌）
        "santic": "森地客", "森地客": "森地客", "SANTIC": "森地客", "Santic": "森地客",
        
        # 思帕客（中国）
        "spakct": "思帕客", "思帕客": "思帕客", "SPAKCT": "思帕客", "Spakct": "思帕客",
        
        # 迈森兰（中国）
        "mysenlan": "迈森兰", "迈森兰": "迈森兰", "MYSENLAN": "迈森兰", "Mysenlan": "迈森兰",
        "msl": "迈森兰", "MSL": "迈森兰", "Msl": "迈森兰",
        
        # GRC（中国）
        "grc": "GRC", "GRC": "GRC", "Grc": "GRC", "gRC": "GRC",
        
        # 捷酷（中国）
        "jakroo": "捷酷", "捷酷": "捷酷", "JAKROO": "捷酷", "Jakroo": "捷酷",
        
        # CCN（中国）
        "ccn": "CCN", "CCN": "CCN", "Ccn": "CCN", "cCN": "CCN",
        
        # 速盟（中国）
        "sobike": "速盟", "速盟": "速盟", "SOBIKE": "速盟", "Sobike": "速盟",
        
        # 闪电（中国）
        "lightning": "闪电", "闪电": "闪电", "LIGHTNING": "闪电", "Lightning": "闪电",
        
        # 骑记（中国）
        "qiji": "骑记", "骑记": "骑记", "QIJI": "骑记", "Qiji": "骑记",
        
        # 洛克兄弟（中国）
        "rockbros": "洛克兄弟", "洛克兄弟": "洛克兄弟", "ROCKBROS": "洛克兄弟", "RockBros": "洛克兄弟",
        
        # 永久（中国）
        "forever": "永久", "永久": "永久", "FOREVER": "永久", "Forever": "永久",
        
        # 凤凰（中国）
        "phoenix": "凤凰", "凤凰": "凤凰", "PHOENIX": "凤凰", "Phoenix": "凤凰",
        
        # 飞鸽（中国）
        "pigeon": "飞鸽", "飞鸽": "飞鸽", "PIGEON": "飞鸽", "Pigeon": "飞鸽",
        
        # === 国际知名骑行服装品牌 ===
        
        # 迪卡侬（法国）
        "decathlon": "迪卡侬", "迪卡侬": "迪卡侬", "DECATHLON": "迪卡侬", "Decathlon": "迪卡侬",
        "btwin": "迪卡侬", "BTWIN": "迪卡侬", "Btwin": "迪卡侬",  # 迪卡侬自行车品牌
        
        # Rapha（英国）
        "rapha": "Rapha", "Rapha": "Rapha", "RAPHA": "Rapha", "rAPHA": "Rapha",
        
        # Castelli（意大利）
        "castelli": "Castelli", "Castelli": "Castelli", "CASTELLI": "Castelli", "cASTELLI": "Castelli",
        
        # Assos（瑞士）
        "assos": "Assos", "Assos": "Assos", "ASSOS": "Assos", "aSsos": "Assos",
        
        # Pearl Izumi（日本）
        "pearlizumi": "Pearl Izumi", "Pearl Izumi": "Pearl Izumi", "PEARLIZUMI": "Pearl Izumi",
        "pearl": "Pearl Izumi", "PEARL": "Pearl Izumi", "Pearl": "Pearl Izumi",
        
        # Craft（瑞典）
        "craft": "Craft", "Craft": "Craft", "CRAFT": "Craft", "cRAFT": "Craft",
        
        # Endura（英国）
        "endura": "Endura", "Endura": "Endura", "ENDURA": "Endura", "eNDURA": "Endura",
        
        # Gore（德国）
        "gore": "Gore", "Gore": "Gore", "GORE": "Gore", "gORE": "Gore",
        "goretex": "Gore", "GORETEX": "Gore", "GoreTex": "Gore",
        
        # Specialized（美国）
        "specialized": "闪电", "闪电": "闪电", "SPECIALIZED": "闪电", "Specialized": "闪电",
        
        # Trek（美国）
        "trek": "Trek", "Trek": "Trek", "TREK": "Trek", "tREK": "Trek",
        
        # Giant（台湾）
        "giant": "捷安特", "捷安特": "捷安特", "GIANT": "捷安特", "Giant": "捷安特",
        
        # Merida（台湾）
        "merida": "美利达", "美利达": "美利达", "MERIDA": "美利达", "Merida": "美利达",
        
        # === 小众品牌和新兴品牌 ===
        
        # 七星（中国小众品牌）
        "sevenstars": "七星", "seven stars": "七星", "七星": "七星",
        "SEVENSTARS": "七星", "Sevenstars": "七星", "SevenStars": "七星",
        
        # 町渥（中国小众品牌）
        "twinwash": "町渥", "町渥": "町渥", "TWINWASH": "町渥", "Twinwash": "町渥",
        
        # Candara（小众品牌）
        "candara": "Candara", "Candara": "Candara", "CANDARA": "Candara", "cANDARA": "Candara",
        
        # 菱木（中国小众品牌）
        "lingmu": "菱木", "菱木": "菱木", "LINGMU": "菱木", "Lingmu": "菱木",
        
        # 美凌（中国小众品牌）
        "meiling": "美凌", "美凌": "美凌", "MEILING": "美凌", "MeiLing": "美凌",
        
        # Haer（可能是海尔变体）
        "haer": "海尔", "HAER": "海尔", "Haer": "海尔", "hAER": "海尔",
        
        # TRONSSRA（小众品牌）
        "tronssra": "TRONSSRA", "TRONSSRA": "TRONSSRA", "Tronssra": "TRONSSRA", "tRONSSRA": "TRONSSRA",
        
        # 海日（可能的中文品牌）
        "hairi": "海日", "海日": "海日", "HAIRI": "海日", "Hairi": "海日",
    }
    
    return extended_brand_mapping


# 品牌关系说明
BRAND_RELATIONSHIPS = {
    "母子品牌关系": {
        "海尔集团": ["海尔", "卡萨帝", "统帅", "AQUA"],
        "美的集团": ["美的", "小天鹅", "COLMO", "华凌", "布谷", "东芝"],
        "格力集团": ["格力", "大松", "晶弘"],
        "海信集团": ["海信", "科龙", "容声", "VIDAA"],
        "创维集团": ["创维", "酷开"],
        "小米生态链": ["小米", "米家", "素士", "云米", "追觅"],
    },
    
    "品牌收购关系": {
        "美的收购": ["东芝白电业务"],
        "海尔收购": ["AQUA", "GE家电"],
        "海信收购": ["东芝电视业务"],
    },
    
    "品牌定位": {
        "高端品牌": ["卡萨帝", "COLMO", "大松", "VIDAA"],
        "中端品牌": ["海尔", "美的", "格力", "海信", "TCL"],
        "性价比品牌": ["统帅", "华凌", "奥克斯", "志高"],
        "互联网品牌": ["小米", "米家", "雷鸟", "酷开"],
    }
}

# 实施代码：将扩充的品牌映射表集成到现有系统中

def update_api_response_monitor_brands():
    """
    更新 APIResponseMonitor 中的品牌映射表

    使用方法：
    1. 将此函数的返回值替换 src/core/api_response_monitor.py 中的 self.brand_mapping
    2. 或者直接复制下面的字典内容
    """

    # 获取扩充后的品牌映射表
    extended_mapping = get_extended_brand_mapping()

    # 格式化为可直接使用的代码
    code_lines = []
    code_lines.append("        # 🎯 扩充后的统一品牌映射表 (英文→中文) - 2025年最新版本")
    code_lines.append("        self.brand_mapping = {")

    # 按类别组织
    categories = {
        "# === 大家电品牌（海尔集团） ===": [
            "haier", "海尔", "HAIER", "Haier",
            "casarte", "卡萨帝", "CASARTE", "Casarte",
            "leader", "统帅", "LEADER", "Leader",
            "aqua", "AQUA", "Aqua"
        ],
        "# === 大家电品牌（美的集团） ===": [
            "midea", "美的", "MIDEA", "Midea",
            "littleswan", "小天鹅", "LITTLESWAN", "LittleSwan",
            "colmo", "COLMO", "Colmo",
            "华凌", "hualing", "HUALING", "Hualing",
            "布谷", "bugu", "BUGU", "Bugu",
            "toshiba", "东芝", "TOSHIBA", "Toshiba"
        ],
        "# === 大家电品牌（格力集团） ===": [
            "gree", "格力", "GREE", "Gree",
            "tosot", "大松", "TOSOT", "Tosot",
            "晶弘", "kinghome", "KINGHOME", "Kinghome"
        ],
        "# === 大家电品牌（其他中国品牌） ===": [
            "tcl", "TCL", "Tcl",
            "雷鸟", "thunderbird", "THUNDERBIRD", "Thunderbird",
            "hisense", "海信", "HISENSE", "Hisense",
            "kelon", "科龙", "KELON", "Kelon",
            "ronshen", "容声", "RONSHEN", "Ronshen",
            "vidaa", "VIDAA", "Vidaa",
            "changhong", "长虹", "CHANGHONG", "Changhong",
            "chiq", "CHiQ", "CHIQ", "ChiQ",
            "konka", "康佳", "KONKA", "Konka",
            "skyworth", "创维", "SKYWORTH", "Skyworth",
            "coocaa", "酷开", "COOCAA", "Coocaa",
            "aux", "奥克斯", "AUX", "Aux",
            "chigo", "志高", "CHIGO", "Chigo",
            "扬子", "yangzi", "YANGZI", "Yangzi",
            "扬子佳", "yangzijia", "YANGZIJIA",
            "新飞", "xinfei", "XINFEI", "Xinfei"
        ],
        "# === 国际大家电品牌 ===": [
            "siemens", "西门子", "SIEMENS", "Siemens",
            "bosch", "博世", "BOSCH", "Bosch",
            "panasonic", "松下", "PANASONIC", "Panasonic",
            "samsung", "三星", "SAMSUNG", "Samsung",
            "lg", "LG", "Lg", "lG",
            "whirlpool", "惠而浦", "WHIRLPOOL", "Whirlpool",
            "electrolux", "伊莱克斯", "ELECTROLUX", "Electrolux",
            "liebherr", "利勃海尔", "LIEBHERR", "Liebherr"
        ],
        "# === 小家电品牌（中国） ===": [
            "joyoung", "九阳", "JOYOUNG", "Joyoung",
            "supor", "苏泊尔", "SUPOR", "Supor",
            "bear", "小熊", "BEAR", "Bear",
            "小熊电器", "bearelectric", "BEARELECTRIC",
            "galanz", "格兰仕", "GALANZ", "Galanz",
            "royalstar", "荣事达", "ROYALSTAR", "Royalstar",
            "liven", "利仁", "LIVEN", "Liven",
            "deerma", "德尔玛", "DEERMA", "Deerma",
            "buydeem", "北鼎", "BUYDEEM", "Buydeem"
        ],
        "# === 小米生态链 ===": [
            "xiaomi", "小米", "XIAOMI", "Xiaomi",
            "mi", "MI", "Mi", "mI",
            "米家", "mijia", "MIJIA", "Mijia",
            "素士", "soocas", "SOOCAS", "Soocas",
            "云米", "viomi", "VIOMI", "Viomi",
            "追觅", "dreame", "DREAME", "Dreame"
        ],
        "# === 国际小家电品牌 ===": [
            "morphyrichards", "摩飞", "MORPHYRICHARDS", "MorphyRichards",
            "morphy", "MORPHY", "Morphy",
            "philips", "飞利浦", "PHILIPS", "Philips",
            "dyson", "戴森", "DYSON", "Dyson",
            "braun", "博朗", "BRAUN", "Braun",
            "tefal", "特福", "TEFAL", "Tefal",
            "delonghi", "德龙", "DELONGHI", "DeLonghi",
            "zojirushi", "象印", "ZOJIRUSHI", "Zojirushi",
            "tiger", "虎牌", "TIGER", "Tiger"
        ],
        "# === 数码电器品牌（中国手机） ===": [
            "huawei", "华为", "HUAWEI", "Huawei",
            "honor", "荣耀", "HONOR", "Honor",
            "oppo", "OPPO", "Oppo", "opPO",
            "vivo", "vivo", "VIVO", "Vivo",
            "oneplus", "一加", "ONEPLUS", "OnePlus",
            "realme", "真我", "REALME", "Realme",
            "meizu", "魅族", "MEIZU", "Meizu",
            "zte", "中兴", "ZTE", "Zte",
            "nubia", "努比亚", "NUBIA", "Nubia"
        ],
        "# === 国际数码品牌 ===": [
            "apple", "苹果", "APPLE", "Apple",
            "iphone", "iPhone", "IPHONE", "IPhone",
            "lenovo", "联想", "LENOVO", "Lenovo",
            "thinkpad", "ThinkPad", "THINKPAD", "Thinkpad",
            "hasee", "神舟", "HASEE", "Hasee",
            "tongfang", "同方", "TONGFANG", "Tongfang",
            "dell", "戴尔", "DELL", "Dell",
            "hp", "惠普", "HP", "Hp",
            "asus", "华硕", "ASUS", "Asus",
            "acer", "宏碁", "ACER", "Acer",
            "msi", "微星", "MSI", "Msi"
        ],
        "# === 骑行服装品牌（中国） ===": [
            "santic", "森地客", "SANTIC", "Santic",
            "spakct", "思帕客", "SPAKCT", "Spakct",
            "mysenlan", "迈森兰", "MYSENLAN", "Mysenlan",
            "msl", "MSL", "Msl",
            "grc", "GRC", "Grc", "gRC",
            "jakroo", "捷酷", "JAKROO", "Jakroo",
            "ccn", "CCN", "Ccn", "cCN",
            "sobike", "速盟", "SOBIKE", "Sobike",
            "lightning", "闪电", "LIGHTNING", "Lightning",
            "qiji", "骑记", "QIJI", "Qiji",
            "rockbros", "洛克兄弟", "ROCKBROS", "RockBros",
            "forever", "永久", "FOREVER", "Forever",
            "phoenix", "凤凰", "PHOENIX", "Phoenix",
            "pigeon", "飞鸽", "PIGEON", "Pigeon"
        ],
        "# === 国际骑行品牌 ===": [
            "decathlon", "迪卡侬", "DECATHLON", "Decathlon",
            "btwin", "BTWIN", "Btwin",
            "rapha", "Rapha", "RAPHA", "rAPHA",
            "castelli", "Castelli", "CASTELLI", "cASTELLI",
            "assos", "Assos", "ASSOS", "aSsos",
            "pearlizumi", "Pearl Izumi", "PEARLIZUMI",
            "pearl", "PEARL", "Pearl",
            "craft", "Craft", "CRAFT", "cRAFT",
            "endura", "Endura", "ENDURA", "eNDURA",
            "gore", "Gore", "GORE", "gORE",
            "goretex", "GORETEX", "GoreTex",
            "specialized", "闪电", "SPECIALIZED", "Specialized",
            "trek", "Trek", "TREK", "tREK",
            "giant", "捷安特", "GIANT", "Giant",
            "merida", "美利达", "MERIDA", "Merida"
        ],
        "# === 小众品牌和新兴品牌 ===": [
            "sevenstars", "seven stars", "七星",
            "SEVENSTARS", "Sevenstars", "SevenStars",
            "twinwash", "町渥", "TWINWASH", "Twinwash",
            "candara", "Candara", "CANDARA", "cANDARA",
            "lingmu", "菱木", "LINGMU", "Lingmu",
            "meiling", "美凌", "MEILING", "MeiLing",
            "haer", "HAER", "Haer", "hAER",
            "tronssra", "TRONSSRA", "Tronssra", "tRONSSRA",
            "hairi", "海日", "HAIRI", "Hairi"
        ]
    }

    # 生成代码
    for category, keys in categories.items():
        code_lines.append(f"            {category}")
        for key in keys:
            if key in extended_mapping:
                value = extended_mapping[key]
                code_lines.append(f'            "{key}": "{value}",')
        code_lines.append("")

    code_lines.append("        }")

    return "\n".join(code_lines)


def update_data_processor_brands():
    """
    更新 DataProcessor 中的品牌映射表

    使用方法：
    将此函数的返回值替换 src/data/processor.py 中的 brand_mapping 字典
    """

    extended_mapping = get_extended_brand_mapping()

    code_lines = []
    code_lines.append("        # 使用与APIResponseMonitor相同的扩充映射表")
    code_lines.append("        brand_mapping = {")

    # 简化版本，只包含核心映射
    core_brands = [
        # 主流品牌
        "haier", "midea", "gree", "tcl", "siemens", "bosch", "panasonic",
        "samsung", "lg", "whirlpool", "xiaomi", "mi", "hisense",
        # 子品牌
        "casarte", "leader", "littleswan", "ronshen", "galanz", "aux",
        # 小众品牌
        "sevenstars", "seven stars", "twinwash", "candara", "lingmu",
        "meiling", "haer", "tronssra", "hairi",
        # 英文变体
        "HAIER", "Haier", "MIDEA", "Midea", "GREE", "Gree",
        "SIEMENS", "Siemens", "BOSCH", "Bosch", "SAMSUNG", "Samsung",
        "XIAOMI", "Xiaomi", "HISENSE", "Hisense", "CASARTE", "Casarte",
        "LEADER", "Leader", "LITTLESWAN", "LittleSwan", "RONSHEN", "Ronshen",
        "GALANZ", "Galanz", "SEVENSTARS", "Sevenstars", "TWINWASH", "Twinwash",
        "MEILING", "MeiLing", "HAER", "Haer", "TRONSSRA", "Tronssra",
        "HAIRI", "Hairi",
        # 手机品牌
        "huawei", "apple", "oppo", "vivo", "oneplus", "meizu", "honor",
        "HUAWEI", "Huawei", "APPLE", "Apple", "ONEPLUS", "OnePlus",
        "MEIZU", "Meizu", "HONOR", "Honor",
        # 骑行品牌
        "santic", "spakct", "mysenlan", "decathlon", "rapha", "giant", "merida",
        "SANTIC", "Santic", "SPAKCT", "Spakct", "DECATHLON", "Decathlon",
        "GIANT", "Giant", "MERIDA", "Merida"
    ]

    for key in core_brands:
        if key in extended_mapping:
            value = extended_mapping[key]
            code_lines.append(f'            "{key}": "{value}",')

    code_lines.append("        }")

    return "\n".join(code_lines)


# 使用说明和统计信息
def get_implementation_guide():
    """
    获取实施指南
    """

    mapping = get_extended_brand_mapping()

    # 统计信息
    total_mappings = len(mapping)
    chinese_brands = len(set(v for v in mapping.values() if any('\u4e00' <= c <= '\u9fff' for c in v)))
    international_brands = len(set(v for v in mapping.values() if not any('\u4e00' <= c <= '\u9fff' for c in v)))

    # 按类别统计
    appliance_count = sum(1 for k in mapping.keys() if any(brand in k.lower() for brand in
                         ['haier', 'midea', 'gree', 'tcl', 'hisense', 'siemens', 'bosch', 'panasonic', 'samsung', 'lg']))
    cycling_count = sum(1 for k in mapping.keys() if any(brand in k.lower() for brand in
                       ['santic', 'spakct', 'mysenlan', 'decathlon', 'rapha', 'giant', 'merida', 'trek']))

    guide = f"""
# 品牌映射表扩充实施指南

## 📊 扩充统计
- **总映射关系**: {total_mappings} 个
- **中国品牌**: {chinese_brands} 个 ({chinese_brands/total_mappings*100:.1f}%)
- **国际品牌**: {international_brands} 个 ({international_brands/total_mappings*100:.1f}%)
- **电器类品牌**: ~{appliance_count} 个映射关系
- **骑行服装品牌**: ~{cycling_count} 个映射关系

## 🔧 实施步骤

### 1. 更新 APIResponseMonitor
```python
# 在 src/core/api_response_monitor.py 的 __init__ 方法中
# 将现有的 self.brand_mapping 替换为扩充版本
{update_api_response_monitor_brands()}
```

### 2. 更新 DataProcessor
```python
# 在 src/data/processor.py 的 _convert_to_chinese_brand 方法中
# 将现有的 brand_mapping 替换为扩充版本
{update_data_processor_brands()}
```

## 🎯 扩充亮点

### 电器类品牌扩充
- **大家电**: 覆盖海尔、美的、格力等主要集团及其子品牌
- **小家电**: 包含九阳、苏泊尔、小熊等知名品牌
- **数码电器**: 涵盖华为、小米、苹果等手机电脑品牌
- **国际品牌**: 西门子、博世、松下等进口品牌

### 骑行服装品牌扩充
- **中国品牌**: 森地客、思帕客、迈森兰等本土领军品牌
- **国际品牌**: Rapha、Castelli、Assos等顶级品牌
- **运动品牌**: 迪卡侬、捷安特、美利达等大众品牌
- **新兴品牌**: 包含电商平台常见的新兴骑行品牌

## 📈 预期效果
- **识别覆盖率**: 从95%提升到98%+
- **品牌统一性**: 100%中文标准化
- **新品类支持**: 新增骑行服装等垂直品类
- **维护便利性**: 统一的映射表管理

## 🔄 后续维护
- 定期更新新兴品牌
- 关注电商平台热门品牌
- 补充垂直品类品牌
- 优化品牌关系映射
"""

    return guide


if __name__ == "__main__":
    # 输出实施指南
    print(get_implementation_guide())

    # 测试扩充后的品牌映射表
    mapping = get_extended_brand_mapping()
    print(f"\n✅ 扩充后的品牌映射表包含 {len(mapping)} 个映射关系")

    # 测试几个典型案例
    test_cases = [
        "森地客骑行服", "Santic骑行装备", "迪卡侬自行车服装",
        "小熊电器养生壶", "九阳豆浆机", "德尔玛加湿器",
        "华为手机", "小米平板", "联想电脑"
    ]

    print(f"\n🧪 测试案例:")
    for case in test_cases:
        for key, value in mapping.items():
            if key.lower() in case.lower():
                print(f"  '{case}' → 可能识别为 '{value}'")
                break
