import React from 'react'
import KeywordInput from './KeywordInput'
import ParameterSettings from './ParameterSettings'
import AdvancedFilters from './AdvancedFilters'
import SearchStats from './SearchStats'
import './SearchConfig.css'

function SearchConfig({ config, onChange, disabled }) {
  const handleChange = (field, value) => {
    onChange({
      ...config,
      [field]: value
    })
  }

  return (
    <div className="search-config-container">
      {/* 标题部分 */}
      <div className="search-config-header">
        <div className="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </div>
        <h2 className="config-title">搜索配置</h2>
      </div>

      {/* 配置内容 */}
      <div className="search-config-body">
        {/* 关键词输入 */}
        <KeywordInput 
          keywords={config.keywords || ''}
          onChange={(keywords) => handleChange('keywords', keywords)}
          disabled={disabled}
        />

        {/* 参数设置 */}
        <ParameterSettings
          maxPages={config.maxPages}
          targetCount={config.targetCount}
          onMaxPagesChange={(value) => handleChange('maxPages', value)}
          onTargetCountChange={(value) => handleChange('targetCount', value)}
          disabled={disabled}
        />

        {/* 高级过滤选项 */}
        <AdvancedFilters
          filters={config.filters || {}}
          onChange={(filters) => handleChange('filters', filters)}
          disabled={disabled}
        />

        {/* 统计信息 */}
        <SearchStats
          maxPages={config.maxPages}
          targetCount={config.targetCount}
          keywordCount={(config.keywords || '').split(',').filter(k => k.trim()).length}
        />
      </div>
    </div>
  )
}

export default SearchConfig