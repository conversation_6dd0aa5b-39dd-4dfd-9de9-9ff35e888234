#!/usr/bin/env python3
"""
测试扩充后的品牌识别系统效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_extended_brand_recognition():
    """测试扩充后的品牌识别效果"""
    print("🔧 测试扩充后的品牌识别系统...")
    
    try:
        from src.core.api_response_monitor import APIResponseMonitor
        print("✅ APIResponseMonitor 导入成功")
        
        # 创建实例
        monitor = APIResponseMonitor()
        print("✅ APIResponseMonitor 实例创建成功")
        print(f"📊 品牌映射表包含 {len(monitor.brand_mapping)} 个映射关系")
        
        # 扩充后的测试案例
        test_cases = [
            # === 电器类品牌测试 ===
            
            # 大家电品牌
            "海尔冰箱490升十字门一级双变频风冷无霜",
            "美的251三开门白色家用小型风冷无霜冷冻",
            "格力空调家用壁挂式变频冷暖1.5匹智能",
            "TCL458升十字对开双开四开门双净味58cm超薄",
            "西门子冰箱家用多门风冷无霜变频大容量智能",
            "博世洗衣机滚筒全自动家用大容量变频",
            "松下冰箱家用三门风冷无霜变频节能省电",
            "三星洗衣机滚筒全自动家用大容量智能控制",
            "LG冰箱家用双门小型节能静音冷藏冷冻",
            
            # 子品牌测试
            "卡萨帝冰箱家用高端多门风冷无霜变频",
            "统帅洗衣机全自动家用大容量波轮智能",
            "小天鹅洗衣机滚筒全自动家用大容量变频",
            "COLMO冰箱家用高端智能风冷无霜",
            "华凌空调家用壁挂式变频冷暖智能",
            "东芝洗衣机滚筒全自动家用大容量",
            "大松空调家用柜式变频冷暖智能控制",
            "科龙冰箱家用三门风冷无霜变频节能",
            "容声冰箱家用双门小型节能静音",
            
            # 小家电品牌
            "九阳豆浆机家用全自动多功能破壁机",
            "苏泊尔电饭煲家用智能预约多功能",
            "小熊电器养生壶家用多功能煮茶器",
            "格兰仕微波炉家用智能光波炉烧烤",
            "荣事达电饭煲家用智能预约多功能",
            "利仁电饼铛家用双面加热煎烤机",
            "德尔玛加湿器家用静音大容量香薰",
            "北鼎养生壶家用多功能煮茶器智能",
            "飞利浦剃须刀男士电动充电式",
            "戴森吸尘器家用无线手持大吸力",
            "博朗剃须刀男士电动充电式",
            
            # 小米生态链
            "小米电饭煲家用智能预约多功能",
            "米家扫地机器人智能规划清扫",
            "素士电动牙刷充电式声波震动",
            "云米净水器家用直饮反渗透",
            "追觅扫地机器人智能规划清扫",
            
            # 数码电器品牌
            "华为手机Mate50 Pro 5G智能手机",
            "荣耀手机Magic5 Pro 5G智能手机",
            "OPPO手机Find X6 Pro 5G智能手机",
            "vivo手机X90 Pro+ 5G智能手机",
            "一加手机11 Pro 5G智能手机",
            "真我手机GT Neo5 5G智能手机",
            "魅族手机20 Pro 5G智能手机",
            "中兴手机Axon40 Ultra 5G智能手机",
            "努比亚手机Red Magic8 Pro游戏手机",
            "苹果iPhone14 Pro Max 5G智能手机",
            "联想笔记本电脑ThinkPad X1 Carbon",
            "神舟笔记本电脑战神Z8游戏本",
            "戴尔笔记本电脑XPS13超极本",
            "惠普笔记本电脑EliteBook商务本",
            "华硕笔记本电脑ROG玩家国度游戏本",
            "宏碁笔记本电脑Predator掠夺者游戏本",
            "微星笔记本电脑GE76游戏本",
            
            # === 骑行服装品牌测试 ===
            
            # 中国骑行品牌
            "森地客骑行服男士夏季短袖骑行装备",
            "思帕客骑行裤男士长裤骑行装备",
            "迈森兰骑行服套装男士春秋骑行装备",
            "GRC骑行服女士短袖骑行装备",
            "捷酷骑行手套男士半指骑行装备",
            "CCN骑行头盔男士安全帽骑行装备",
            "速盟骑行眼镜男士防风骑行装备",
            "闪电骑行鞋男士锁鞋骑行装备",
            "骑记骑行服男士长袖骑行装备",
            "洛克兄弟骑行包后座包骑行装备",
            "永久自行车骑行服男士骑行装备",
            "凤凰自行车骑行服女士骑行装备",
            "飞鸽自行车骑行服男士骑行装备",
            
            # 国际骑行品牌
            "迪卡侬骑行服男士短袖骑行装备",
            "Rapha骑行服男士专业骑行装备",
            "Castelli骑行服女士专业骑行装备",
            "Assos骑行裤男士专业骑行装备",
            "Pearl Izumi骑行服男士专业骑行装备",
            "Craft骑行服女士功能性骑行装备",
            "Endura骑行服男士防风骑行装备",
            "Gore骑行服男士防水骑行装备",
            "闪电Specialized自行车骑行装备",
            "Trek自行车骑行服男士骑行装备",
            "捷安特Giant自行车骑行装备",
            "美利达Merida自行车骑行装备",
            
            # === 小众品牌和新兴品牌测试 ===
            "德国sevenstars600L冰箱十字四开门",
            "Twinwash町渥10公斤滚筒洗衣机",
            "Candara洗衣机全自动家用15公斤大容量",
            "菱木全自动洗衣机商用大功率",
            "美凌智慧星洗衣机全自动家用",
            "Haer洗衣机全自动家用大容量波轮",
            "TRONSSRA内衣洗衣机迷你高温洗烘脱",
            "Hairi洗衣机全自动家用大容量出租屋",
            
            # === 中英文混合和大小写变体测试 ===
            "Haier/海尔冰箱490升十字门一级双变频",
            "MIDEA/美的洗衣机全自动家用大容量",
            "GREE格力空调家用壁挂式变频冷暖",
            "SIEMENS西门子冰箱家用多门风冷无霜",
            "BOSCH博世洗衣机滚筒全自动家用",
            "SAMSUNG三星洗衣机滚筒全自动家用",
            "XIAOMI小米电饭煲家用智能预约",
            "SANTIC森地客骑行服男士夏季短袖",
            "DECATHLON迪卡侬骑行服男士短袖",
            "GIANT捷安特自行车骑行装备",
        ]
        
        print(f"\n📋 测试 {len(test_cases)} 个扩充案例:")
        
        success_count = 0
        category_stats = {
            "大家电": 0,
            "小家电": 0,
            "数码电器": 0,
            "骑行装备": 0,
            "小众品牌": 0,
            "混合格式": 0
        }
        
        for i, goods_name in enumerate(test_cases, 1):
            try:
                result = monitor._extract_brand_name(goods_name, 0)
                
                # 判断类别
                category = "其他"
                if any(keyword in goods_name for keyword in ["冰箱", "洗衣机", "空调", "电视"]):
                    category = "大家电"
                elif any(keyword in goods_name for keyword in ["豆浆机", "电饭煲", "养生壶", "微波炉", "剃须刀", "吸尘器"]):
                    category = "小家电"
                elif any(keyword in goods_name for keyword in ["手机", "笔记本", "电脑"]):
                    category = "数码电器"
                elif any(keyword in goods_name for keyword in ["骑行", "自行车"]):
                    category = "骑行装备"
                elif any(brand in goods_name for brand in ["sevenstars", "Twinwash", "Candara", "菱木", "美凌", "Haer", "TRONSSRA", "Hairi"]):
                    category = "小众品牌"
                elif "/" in goods_name or any(char.isupper() for char in goods_name[:10]):
                    category = "混合格式"
                
                if result:
                    success_count += 1
                    if category in category_stats:
                        category_stats[category] += 1
                    print(f"✅ {i:2d}. [{category:6s}] '{goods_name[:50]}...' → '{result}'")
                else:
                    print(f"❌ {i:2d}. [{category:6s}] '{goods_name[:50]}...' → 未识别")
                    
            except Exception as e:
                print(f"💥 {i:2d}. 错误: {e}")
        
        # 统计结果
        print(f"\n{'='*80}")
        print(f"📊 扩充品牌识别系统测试结果")
        print(f"{'='*80}")
        print(f"总测试案例: {len(test_cases)}")
        print(f"识别成功: {success_count}")
        print(f"识别成功率: {(success_count/len(test_cases))*100:.1f}%")
        
        print(f"\n📈 分类统计:")
        for category, count in category_stats.items():
            category_total = sum(1 for case in test_cases if category_matches(case, category))
            if category_total > 0:
                rate = (count / category_total) * 100
                print(f"  {category:8s}: {count:2d}/{category_total:2d} ({rate:5.1f}%)")
        
        # 评估效果
        if success_count >= len(test_cases) * 0.95:
            print(f"\n🎉 扩充效果优秀！识别成功率达到95%+")
        elif success_count >= len(test_cases) * 0.90:
            print(f"\n✅ 扩充效果良好！识别成功率达到90%+")
        elif success_count >= len(test_cases) * 0.85:
            print(f"\n👍 扩充效果不错！识别成功率达到85%+")
        else:
            print(f"\n⚠️ 扩充效果需要进一步优化")
        
        # 展示扩充亮点
        print(f"\n🌟 扩充亮点:")
        print(f"  ✅ 电器类品牌: 覆盖大家电、小家电、数码电器全品类")
        print(f"  ✅ 骑行服装品牌: 新增专业骑行装备品类支持")
        print(f"  ✅ 品牌关系完整: 母子品牌、收购关系、变体统一")
        print(f"  ✅ 中文优先策略: 所有结果统一为中文标准名称")
        print(f"  ✅ 轻量化实现: 零外部依赖，高性能执行")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def category_matches(goods_name: str, category: str) -> bool:
    """判断商品名称是否属于指定类别"""
    if category == "大家电":
        return any(keyword in goods_name for keyword in ["冰箱", "洗衣机", "空调", "电视"])
    elif category == "小家电":
        return any(keyword in goods_name for keyword in ["豆浆机", "电饭煲", "养生壶", "微波炉", "剃须刀", "吸尘器"])
    elif category == "数码电器":
        return any(keyword in goods_name for keyword in ["手机", "笔记本", "电脑"])
    elif category == "骑行装备":
        return any(keyword in goods_name for keyword in ["骑行", "自行车"])
    elif category == "小众品牌":
        return any(brand in goods_name for brand in ["sevenstars", "Twinwash", "Candara", "菱木", "美凌", "Haer", "TRONSSRA", "Hairi"])
    elif category == "混合格式":
        return "/" in goods_name or any(char.isupper() for char in goods_name[:10])
    return False

if __name__ == "__main__":
    print("🚀 开始测试扩充后的品牌识别系统...")
    test_extended_brand_recognition()
    print(f"\n✅ 测试完成！")
