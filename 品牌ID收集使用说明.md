# 拼多多品牌ID数据收集工具使用说明

## 📋 工具概述

本工具用于收集拼多多商品数据中的品牌ID信息，用于扩充现有的`brand_id_mapping`字典。

### 🎯 收集目标
- **关键词**: 冰箱、空调、热水器、洗衣机
- **每个关键词**: 60个商品数据
- **总计**: 240个商品样本

## 🚀 使用步骤

### 1. 准备工作

#### 1.1 确保Cookie已配置
在运行收集脚本前，必须确保已配置有效的拼多多登录Cookie：

```bash
# 方法1: 使用Web界面配置Cookie
python -m backend.api_server
# 访问 http://localhost:8000 配置Cookie

# 方法2: 直接编辑配置文件
# 编辑 config/cookies.json 文件
```

#### 1.2 检查配置文件
确保 `config/settings.yaml` 配置正确：
```yaml
target:
  base_url: "https://mobile.yangkeduo.com"
  
browser:
  headless: false  # 建议设为false以便观察过程
```

### 2. 运行数据收集

```bash
# 运行品牌ID收集脚本
python brand_id_collector.py
```

#### 收集过程说明：
1. 启动浏览器并加载Cookie
2. 检查登录状态
3. 逐个搜索关键词（冰箱、空调、热水器、洗衣机）
4. 拦截API响应并提取商品数据
5. 保存原始数据到JSON文件

#### 预期输出：
```
🚀 拼多多品牌ID数据收集器
==================================================
📊 开始收集关键词: 冰箱
📊 开始收集关键词: 空调
📊 开始收集关键词: 热水器
📊 开始收集关键词: 洗衣机
📈 生成品牌ID分析报告...
✅ 收集完成!
```

### 3. 运行数据分析

```bash
# 运行品牌ID分析脚本
python brand_id_analyzer.py
```

#### 分析功能：
1. 加载收集到的原始数据
2. 提取品牌ID和品牌名称关系
3. 检测映射冲突和质量评分
4. 生成实现代码建议

## 📁 输出文件结构

```
data/
├── api_responses/              # 原始API响应数据
│   ├── refrigerator_responses.json      # 冰箱数据
│   ├── air_conditioner_responses.json   # 空调数据
│   ├── water_heater_responses.json      # 热水器数据
│   └── washing_machine_responses.json   # 洗衣机数据
│
└── analysis/                   # 分析结果
    ├── brand_id_analysis_detailed.json  # 详细分析报告
    ├── new_brand_mappings.json          # 新发现的映射
    └── high_quality_mappings.json       # 高质量映射推荐
```

## 📊 分析报告内容

### 详细分析报告包含：
- **品牌ID统计**: 每个品牌ID的出现频次、一致性得分
- **冲突检测**: 同一品牌ID对应多个品牌名称的情况
- **质量评分**: 基于一致性、频次、覆盖度的综合评分
- **实现代码**: 可直接使用的Python代码

### 示例分析结果：
```json
{
  "new_mappings": {
    "1234": "海尔",
    "5678": "美的",
    "9012": "格力"
  },
  "quality_scores": {
    "1234": 0.95,
    "5678": 0.87,
    "9012": 0.92
  }
}
```

## 🔧 实施新映射

### 1. 查看建议的映射代码
分析完成后，查看生成的实现代码：
```python
# 建议添加到 src/data/processor.py 的 brand_id_mapping 字典中:
self.brand_id_mapping = {
    # 现有映射
    "3838": "统帅",  # 统帅品牌ID
    
    # 新发现的品牌ID映射:
    "1234": "海尔",  # 海尔品牌ID
    "5678": "美的",  # 美的品牌ID
    "9012": "格力",  # 格力品牌ID
}
```

### 2. 更新代码
将高质量的映射添加到 `src/data/processor.py` 文件中的 `brand_id_mapping` 字典。

### 3. 测试验证
```bash
# 运行测试验证新映射
python -c "
from src.data.processor import DataProcessor
processor = DataProcessor()
print('新映射数量:', len(processor.brand_id_mapping))
print('映射内容:', processor.brand_id_mapping)
"
```

## ⚠️ 注意事项

### 1. Cookie有效性
- 确保Cookie是最新的且有效的
- 如果收集过程中出现登录问题，重新获取Cookie

### 2. 反爬虫措施
- 脚本已内置反检测机制
- 如果遇到风控，会自动处理或提示

### 3. 数据质量
- 优先使用高质量映射（质量得分 >= 0.8）
- 对于冲突的映射，需要人工判断

### 4. 运行环境
- 建议在稳定的网络环境下运行
- 确保有足够的磁盘空间存储数据

## 🐛 常见问题

### Q1: 收集到的数据很少怎么办？
A: 检查Cookie是否有效，确保已正确登录拼多多

### Q2: 出现"风控检测"提示？
A: 这是正常现象，脚本会自动处理，稍等片刻即可

### Q3: 分析结果中有很多冲突？
A: 这是正常的，选择出现频次最高的品牌名称作为映射

### Q4: 如何验证新映射的正确性？
A: 可以手动检查几个品牌ID对应的商品，确认品牌名称是否正确

## 📞 技术支持

如果在使用过程中遇到问题：
1. 查看控制台日志输出
2. 检查 `logs/` 目录下的日志文件
3. 确认网络连接和Cookie状态

## 🔄 定期更新

建议定期运行此工具来：
- 发现新的品牌ID
- 更新品牌映射关系
- 提高品牌识别准确率
