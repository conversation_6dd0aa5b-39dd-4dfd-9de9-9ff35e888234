import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react'
import DataTable from './DataTable'
import DataGrid from './DataGrid'
import ProductDetail from './ProductDetail'
import ImagePreview from './ImagePreview'
import clsx from 'clsx'
import './DataPreviewEnhanced.css'

export default function DataPreviewEnhanced({ 
  data = [], 
  loading = false,
  hasMore = false,
  onLoadMore = () => {},
  onExport = () => {}
}) {
  const [viewMode, setViewMode] = useState('table') // 'table' | 'grid'
  const [selectedProduct, setSelectedProduct] = useState(null)
  const [previewImages, setPreviewImages] = useState([])
  const [previewIndex, setPreviewIndex] = useState(0)
  const [selectedItems, setSelectedItems] = useState([])
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' })
  const [filters, setFilters] = useState({})
  const [searchQuery, setSearchQuery] = useState('')
  const containerRef = useRef(null)
  const [containerWidth, setContainerWidth] = useState(1200)

  // 监听容器宽度变化
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth)
      }
    }

    updateWidth()
    window.addEventListener('resize', updateWidth)
    return () => window.removeEventListener('resize', updateWidth)
  }, [])

  // 过滤和搜索数据
  const filteredData = useMemo(() => {
    let result = [...data]

    // 搜索过滤
    if (searchQuery) {
      result = result.filter(item => 
        item.goods_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.shop_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.brand_name?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // 价格区间过滤
    if (filters.minPrice) {
      result = result.filter(item => item.price >= filters.minPrice)
    }
    if (filters.maxPrice) {
      result = result.filter(item => item.price <= filters.maxPrice)
    }

    // 销量过滤
    if (filters.minSales) {
      result = result.filter(item => item.sales >= filters.minSales)
    }

    // 品牌过滤
    if (filters.brands && filters.brands.length > 0) {
      result = result.filter(item => filters.brands.includes(item.brand_name))
    }

    return result
  }, [data, searchQuery, filters])

  // 获取所有品牌
  const allBrands = useMemo(() => {
    const brands = new Set()
    data.forEach(item => {
      if (item.brand_name) {
        brands.add(item.brand_name)
      }
    })
    return Array.from(brands).sort()
  }, [data])

  // 处理商品点击
  const handleItemClick = useCallback((item) => {
    // 如果商品有链接，直接在新标签页打开
    if (item.goods_url) {
      window.open(item.goods_url, '_blank', 'noopener,noreferrer')
    } else if (item.link_url) {
      // 如果只有 link_url，尝试构建完整链接
      let url = item.link_url
      if (url.startsWith('goods.html')) {
        url = `https://mobile.yangkeduo.com/${url}`
      } else if (url.startsWith('/')) {
        url = `https://mobile.yangkeduo.com${url}`
      } else if (!url.startsWith('http')) {
        url = `https://mobile.yangkeduo.com/goods.html?goods_id=${item.goods_id}`
      }
      window.open(url, '_blank', 'noopener,noreferrer')
    } else if (item.goods_id) {
      // 如果只有商品ID，构建商品链接
      const url = `https://mobile.yangkeduo.com/goods.html?goods_id=${item.goods_id}`
      window.open(url, '_blank', 'noopener,noreferrer')
    } else {
      // 如果没有链接信息，显示商品详情弹窗
      setSelectedProduct(item)
    }
  }, [])

  // 处理图片预览
  const handleImagePreview = useCallback((images, index) => {
    setPreviewImages(images)
    setPreviewIndex(index)
  }, [])

  // 处理批量操作
  const handleBatchExport = useCallback(() => {
    if (selectedItems.length > 0) {
      onExport(selectedItems)
    }
  }, [selectedItems, onExport])

  // 处理全选
  const handleSelectAll = useCallback(() => {
    if (selectedItems.length === filteredData.length) {
      setSelectedItems([])
    } else {
      setSelectedItems([...filteredData])
    }
  }, [filteredData, selectedItems])

  // 清除选择
  const handleClearSelection = useCallback(() => {
    setSelectedItems([])
  }, [])

  return (
    <div className="data-preview-enhanced" ref={containerRef}>
      {/* 工具栏 */}
      <div className="preview-toolbar-container">
        <div className="toolbar-left">
          <h2 className="preview-title">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
            </svg>
            数据预览
          </h2>
          <span className="data-count">
            共 {filteredData.length} 条
            {selectedItems.length > 0 && ` (已选 ${selectedItems.length} 条)`}
          </span>
        </div>

        <div className="toolbar-center">
          {/* 搜索框 */}
          <div className="search-box">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M11.742 10.344a6.5 6.5 0 10-1.397 1.398h-.001c.*************.098.115l3.85 3.85a1 1 0 001.415-1.414l-3.85-3.85a1.007 1.007 0 00-.115-.1zM12 6.5a5.5 5.5 0 11-11 0 5.5 5.5 0 0111 0z"/>
            </svg>
            <input
              type="text"
              placeholder="搜索商品名称、店铺或品牌"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <button 
                className="clear-btn"
                onClick={() => setSearchQuery('')}
              >
                ✕
              </button>
            )}
          </div>
        </div>

        <div className="toolbar-right">
          {/* 视图切换 */}
          <div className="view-switcher">
            <button
              className={clsx('view-btn', { active: viewMode === 'table' })}
              onClick={() => setViewMode('table')}
              title="表格视图"
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
              </svg>
            </button>
            <button
              className={clsx('view-btn', { active: viewMode === 'grid' })}
              onClick={() => setViewMode('grid')}
              title="网格视图"
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <rect x="3" y="3" width="6" height="6" rx="1"/>
                <rect x="11" y="3" width="6" height="6" rx="1"/>
                <rect x="3" y="11" width="6" height="6" rx="1"/>
                <rect x="11" y="11" width="6" height="6" rx="1"/>
              </svg>
            </button>
          </div>

          {/* 操作按钮 */}
          {selectedItems.length > 0 && (
            <div className="batch-actions">
              <button 
                className="batch-btn"
                onClick={handleSelectAll}
              >
                {selectedItems.length === filteredData.length ? '取消全选' : '全选'}
              </button>
              <button 
                className="batch-btn primary"
                onClick={handleBatchExport}
              >
                导出选中 ({selectedItems.length})
              </button>
              <button 
                className="batch-btn"
                onClick={handleClearSelection}
              >
                清除选择
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 筛选器 */}
      <FilterBar 
        filters={filters}
        onFiltersChange={setFilters}
        brands={allBrands}
      />

      {/* 数据展示区域 */}
      <div className="preview-content">
        {viewMode === 'table' ? (
          <DataTable
            data={filteredData}
            loading={loading}
            hasMore={hasMore}
            onLoadMore={onLoadMore}
            onItemClick={handleItemClick}
            selectedItems={selectedItems}
            onSelectionChange={setSelectedItems}
            sortConfig={sortConfig}
            onSort={setSortConfig}
            filters={filters}
            onFilter={setFilters}
          />
        ) : (
          <DataGrid
            data={filteredData}
            loading={loading}
            hasMore={hasMore}
            onLoadMore={onLoadMore}
            onItemClick={handleItemClick}
            selectedItems={selectedItems}
            onSelectionChange={setSelectedItems}
            containerWidth={containerWidth}
          />
        )}
      </div>

      {/* 商品详情 */}
      <ProductDetail
        product={selectedProduct}
        visible={!!selectedProduct}
        onClose={() => setSelectedProduct(null)}
      />

      {/* 图片预览 */}
      <ImagePreview
        images={previewImages}
        visible={previewImages.length > 0}
        onClose={() => setPreviewImages([])}
        currentIndex={previewIndex}
      />
    </div>
  )
}

// 筛选器组件
function FilterBar({ filters, onFiltersChange, brands }) {
  const [showFilters, setShowFilters] = useState(false)

  const handlePriceChange = (type, value) => {
    onFiltersChange({
      ...filters,
      [type]: value ? parseFloat(value) : null
    })
  }

  const handleSalesChange = (value) => {
    onFiltersChange({
      ...filters,
      minSales: value ? parseInt(value) : null
    })
  }

  const handleBrandChange = (brand, checked) => {
    const currentBrands = filters.brands || []
    if (checked) {
      onFiltersChange({
        ...filters,
        brands: [...currentBrands, brand]
      })
    } else {
      onFiltersChange({
        ...filters,
        brands: currentBrands.filter(b => b !== brand)
      })
    }
  }

  const clearFilters = () => {
    onFiltersChange({})
  }

  const hasActiveFilters = filters.minPrice || filters.maxPrice || filters.minSales || 
    (filters.brands && filters.brands.length > 0)

  return (
    <div className={clsx('filter-bar', { expanded: showFilters })}>
      <div className="filter-header">
        <button 
          className="filter-toggle"
          onClick={() => setShowFilters(!showFilters)}
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M1 3h14v2H1V3zm2 4h10v2H3V7zm2 4h6v2H5v-2z"/>
          </svg>
          筛选
          {hasActiveFilters && <span className="filter-badge">•</span>}
        </button>
        {hasActiveFilters && (
          <button className="clear-filters" onClick={clearFilters}>
            清除筛选
          </button>
        )}
      </div>

      {showFilters && (
        <div className="filter-content">
          <div className="filter-group">
            <label>价格区间</label>
            <div className="price-inputs">
              <input
                type="number"
                placeholder="最低价"
                value={filters.minPrice || ''}
                onChange={(e) => handlePriceChange('minPrice', e.target.value)}
              />
              <span>-</span>
              <input
                type="number"
                placeholder="最高价"
                value={filters.maxPrice || ''}
                onChange={(e) => handlePriceChange('maxPrice', e.target.value)}
              />
            </div>
          </div>

          <div className="filter-group">
            <label>最低销量</label>
            <input
              type="number"
              placeholder="输入最低销量"
              value={filters.minSales || ''}
              onChange={(e) => handleSalesChange(e.target.value)}
            />
          </div>

          {brands.length > 0 && (
            <div className="filter-group">
              <label>品牌筛选</label>
              <div className="brand-list">
                {brands.slice(0, 10).map(brand => (
                  <label key={brand} className="brand-item">
                    <input
                      type="checkbox"
                      checked={(filters.brands || []).includes(brand)}
                      onChange={(e) => handleBrandChange(brand, e.target.checked)}
                    />
                    <span>{brand}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}