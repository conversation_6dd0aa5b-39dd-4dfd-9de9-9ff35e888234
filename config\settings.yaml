anti_detection:
  check_interval: 3
  human_behavior:
    enabled: false
  max_sleep_seconds: 2
  risk_indicators:
  - text=网络繁忙
  - text=请稍后再试
  - text=系统繁忙
  - text=访问过于频繁
  - text=请输入验证码
  - text=请求过于频繁
  - text=429
  - \.risk-control
  - \.captcha
  - \.verify-code
  - '#captcha'
  risk_status_codes:
  - 429
  - 403
  - 503
  - 509
  - 520
app:
  author: AI Assistant
  name: 拼多多商品数据爬虫
  version: 2.0.0
browser:
  args:
  - --disable-blink-features=AutomationControlled
  - --no-sandbox
  - --disable-dev-shm-usage
  device_scale_factor: 1.0
  headless: false
  slow_mo: 100
  timeout: 30000
  type: chromium
  viewport:
    height: 1080
    width: 1920
cdp:
  auto_close_browser: false
  custom_browser_path: ''
  debug_port: 9222
  enabled: true
  headless: false
  launch_timeout: 30
  save_login_state: true
  user_data_dir: pdd_user_data_dir
export:
  excel:
    column_mapping:
      activity_type_name: 活动类型
      brand_name: 品牌名称
      category: 商品分类
      comment_count: 评论数
      coupon_price: 券后价(元)
      created_time: 采集时间
      goods_id: 商品ID
      goods_name: 商品名称
      goods_url: 商品链接
      hd_thumb_url: 高清图片
      image_url: 商品图片
      is_government_subsidy: 国补商品
      is_subsidy: 百亿补贴
      keyword: 搜索关键词
      market_price: 市场价(元)
      marketing_tags: 营销标签
      merchant_type_name: 商家类型
      original_price: 原价(元)
      price: 拼团价(元)
      rating: 评分
      sales: 销量
      sales_tip: 销量描述
      special_text: 特殊信息
      subsidy_info: 补贴详情
      tags: 商品标签
    sheet_name_template: '{keyword}_商品数据'
  filename_template: 拼多多商品数据_{timestamp}.xlsx
  output_dir: ./output
fingerprint:
  audio_fingerprint: true
  canvas_noise_level: 0.1
  enabled: true
  font_fingerprint: true
  rotation_interval: 3600
  webgl_randomization: true
lightweight_fingerprint:
  enabled: true
  rotation_interval: 3600
logging:
  console_output: true
  level: DEBUG
  log_dir: ./logs
  log_file_template: pdd_crawler_{date}.log
  retention_days: 7
login_detection:
  check_interval: 5
  login_indicators:
  - .login-avatar
  - '[data-testid=''user-avatar'']'
  - .user-info
  logout_indicators:
  - .login-btn
  - '[data-testid=''login-button'']'
  - text=登录
performance:
  concurrent_limit: 3
  enable_cache: true
  memory_limit: 512
  request_delay: 2
product_filter:
  algorithm:
    case_sensitive: false
    fuzzy_threshold: 0.8
    number_tolerance: 0.05
    unit_normalization: true
  debug:
    enabled: true
    log_scores: true
    save_filtered: false
  enabled: true
  match_threshold: 0.6
  strict_mode: false
  weights:
    brand: 0.4
    keyword: 0.1
    product_type: 0.25
    specification: 0.25
  statistical:
    enabled: true
    min_sample_size: 10
    major_type_threshold: 0.7
    minor_type_threshold: 0.1
    confidence_threshold: 0.8
    log_statistics: true
proxy:
  enabled: false
  password: ''
  server: ''
  username: ''
retry:
  backoff_factor: 1.5
  max_attempts: 2
  max_wait_time: 10
  operation_timeout: 10
  quick_fail_exceptions:
  - LoginRequiredError
  - CookieInvalidError
  - PageStructureChangedError
  total_timeout: 60
  wait_time: 2
scroll:
  adaptive_delay:
    increment: 0.1
    max: 0.5
    min: 0.1
  api_detected_wait: 0.3
  api_response_timeout: 2.0
  dynamic_scrolling: true
  max_scrolls: 50
  no_data_threshold: 8
  scroll_distance: 1800
  scroll_distance_range:
    max: 2200
    min: 1600
  smart_api_detection: true
  wait_for_response: 0.8
search:
  keywords:
  - 海尔476
  max_pages: 20
  target_count: 100
sorting:
  enabled: false
  retry_on_fail: false
  timeout: 10
  types:
  - default: true
    name: 综合排序
    selector: .sort-item[data-sort='综合']
    value: comprehensive
  - name: 价格从低到高
    selector: .sort-item[data-sort='价格']
    sub_selector: .price-asc
    value: price_asc
  - name: 销量从高到低
    selector: .sort-item[data-sort='销量']
    value: sales_desc
stealth:
  enabled: true
  init_scripts_only: true
  languages:
  - zh-CN
  - zh
  - en
target:
  base_url: https://mobile.yangkeduo.com
  search_api: /proxy/api/search
  search_page: /search_result.html
user_agent:
  browser_weights:
    chrome: 0.7
    firefox: 0.2
    safari: 0.1
  consistency_check: true
  device_weights:
    desktop: 0.4
    mobile: 0.5
    tablet: 0.1
  rotation_interval: 3600
