import React, { useState, useEffect, useRef } from 'react'
import './LazyImage.css'

const LazyImage = ({
  src,
  alt,
  placeholder,
  className = '',
  onLoad,
  onError,
  ...props
}) => {
  const [imageSrc, setImageSrc] = useState(placeholder || '')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const imgRef = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // 开始加载图片
            const img = new Image()
            img.src = src
            
            img.onload = () => {
              setImageSrc(src)
              setLoading(false)
              if (onLoad) onLoad()
            }
            
            img.onerror = () => {
              setError(true)
              setLoading(false)
              if (onError) onError()
            }
            
            // 停止观察
            if (imgRef.current) {
              observer.unobserve(imgRef.current)
            }
          }
        })
      },
      {
        rootMargin: '50px' // 提前50px开始加载
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current)
      }
    }
  }, [src, onLoad, onError])

  return (
    <div ref={imgRef} className={`lazy-image-container ${className}`}>
      {loading && !error && (
        <div className="lazy-image-placeholder">
          <div className="lazy-image-spinner" />
        </div>
      )}
      
      {error && (
        <div className="lazy-image-error">
          <svg width="48" height="48" viewBox="0 0 48 48" fill="currentColor">
            <path d="M38 12.83L35.17 10 24 21.17 12.83 10 10 12.83 21.17 24 10 35.17 12.83 38 24 26.83 35.17 38 38 35.17 26.83 24z"/>
            <path d="M0 0h48v48H0z" fill="none"/>
          </svg>
          <span>加载失败</span>
        </div>
      )}
      
      {!error && (
        <img
          {...props}
          src={imageSrc}
          alt={alt}
          className={`lazy-image ${loading ? 'lazy-image-loading' : 'lazy-image-loaded'}`}
          style={{
            opacity: loading ? 0 : 1
          }}
        />
      )}
    </div>
  )
}

// 批量懒加载图片组件
export const LazyImageGallery = ({ images, columns = 3 }) => {
  return (
    <div 
      className="lazy-image-gallery"
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`
      }}
    >
      {images.map((image, index) => (
        <LazyImage
          key={index}
          src={image.src}
          alt={image.alt || `Image ${index + 1}`}
          className="lazy-image-gallery-item"
        />
      ))}
    </div>
  )
}

export default LazyImage