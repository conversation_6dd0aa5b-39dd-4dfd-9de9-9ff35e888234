# 拼多多爬虫使用指南 (2025-01-27 更新版)

## 快速开始

### 1. 配置文件设置 (config/settings.yaml)

```yaml
search:
  keywords:
    - 冰箱
    - 洗衣机
  target_count: 60  # 每个关键词收集60个商品
```

### 2. 运行爬虫

```bash
python run_main.py
# 或
python -m src.main
```

## 重要更新说明

### 目标数量计算方式（重大变更）

**旧版本**：所有关键词共享一个总目标
- 设置：target_count: 60, keywords: ["冰箱", "洗衣机"]
- 结果：冰箱40个 + 洗衣机20个 = 总共60个

**新版本**：每个关键词独立计算目标
- 设置：target_count: 60, keywords: ["冰箱", "洗衣机"]
- 结果：冰箱60个 + 洗衣机60个 = 总共120个

### 性能优化参数

```yaml
scroll:
  adaptive_delay:
    min: 0.5    # 最小延迟（秒）
    max: 3      # 最大延迟（秒）
  scroll_distance: 1500  # 滚动距离（像素）
  wait_for_response: 2   # API响应等待（秒）
  max_scrolls: 50       # 最大滚动次数
```

## 最佳实践

### 1. 合理设置目标数量
- 考虑总数据量 = 关键词数量 × target_count
- 建议单个关键词不超过100个，避免触发风控

### 2. Cookie管理
- 使用真实的拼多多账号Cookie
- 定期更新Cookie，避免过期
- Cookie文件：config/cookies.json

### 3. 风控应对
- 观察日志中的429错误
- 如果频繁出现风控，适当增加延迟
- 可以调整 adaptive_delay 的 min 和 max 值

### 4. 数据导出
- 数据自动导出到 output/ 目录
- 文件名格式：拼多多商品数据_YYYYMMDD_HHMMSS.xlsx
- 每个关键词一个工作表

## 常见问题

### Q: 为什么实际收集的数量比设置的少？
A: 可能原因：
1. 搜索结果不足
2. 触发风控限制
3. 网络连接问题

### Q: 如何提高爬取速度？
A: 
1. 确保网络稳定
2. 使用有效的Cookie
3. 适当调整滚动参数

### Q: 如何避免风控？
A:
1. 使用真实Cookie
2. 合理设置延迟
3. 避免短时间内大量请求

## 高级配置

### 1. 启用排序功能
```yaml
sorting:
  enabled: true  # 开启排序
```

### 2. 调试模式
```yaml
logging:
  level: DEBUG  # 显示详细日志
```

### 3. 无头模式
```yaml
browser:
  headless: true  # 后台运行
```