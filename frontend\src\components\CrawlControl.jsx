import React, { useState } from 'react'
import './CrawlControl.css'

function CrawlControl({ status, onStart, onPause, onResume, onStop, onExport, onExportCSV, hasData }) {
  const [showExportMenu, setShowExportMenu] = useState(false)
  const renderControlButtons = () => {
    switch (status) {
      case 'idle':
        return (
          <button className="btn btn-primary btn-large" onClick={onStart}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
            </svg>
            开始爬取
          </button>
        )
      case 'running':
        return (
          <>
            <button className="btn btn-secondary" onClick={onPause}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd"/>
              </svg>
              暂停
            </button>
            <button className="btn btn-secondary" onClick={onStop}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd"/>
              </svg>
              停止
            </button>
          </>
        )
      case 'paused':
        return (
          <>
            <button className="btn btn-primary" onClick={onResume}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
              </svg>
              继续
            </button>
            <button className="btn btn-secondary" onClick={onStop}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd"/>
              </svg>
              停止
            </button>
          </>
        )
      case 'completed':
        return (
          <button className="btn btn-primary btn-large" onClick={onStart}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd"/>
            </svg>
            重新开始
          </button>
        )
      default:
        return null
    }
  }

  return (
    <div className="crawl-control card">
      <h2 className="control-title">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
        </svg>
        爬取控制
      </h2>
      
      <div className="control-buttons">
        {renderControlButtons()}
      </div>
      
      {hasData && (
        <div className="export-section">
          <div className="divider"></div>
          <div style={{ position: 'relative' }}>
            <button 
              className={`btn btn-primary btn-block ${status === 'running' ? 'btn-disabled' : ''}`}
              onClick={() => status !== 'running' && setShowExportMenu(!showExportMenu)}
              disabled={status === 'running'}
              title={status === 'running' ? '请等待爬取完成后再导出' : '导出数据'}
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
              </svg>
              {status === 'running' ? '正在爬取中...' : '导出数据'}
              <svg 
                width="16" 
                height="16" 
                viewBox="0 0 20 20" 
                fill="currentColor"
                style={{ marginLeft: '8px' }}
              >
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd"/>
              </svg>
            </button>
            
            {showExportMenu && (
              <div style={{
                position: 'absolute',
                top: '100%',
                left: 0,
                right: 0,
                marginTop: '4px',
                background: 'white',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                zIndex: 10
              }}>
                <button
                  className="export-menu-item"
                  onClick={() => {
                    setShowExportMenu(false)
                    onExport()
                  }}
                  style={{
                    width: '100%',
                    padding: '10px 16px',
                    textAlign: 'left',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 20 20" fill="#10b981">
                    <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd"/>
                  </svg>
                  导出为 Excel (.xlsx)
                </button>
                <button
                  className="export-menu-item"
                  onClick={() => {
                    setShowExportMenu(false)
                    onExportCSV && onExportCSV()
                  }}
                  style={{
                    width: '100%',
                    padding: '10px 16px',
                    textAlign: 'left',
                    background: 'none',
                    border: 'none',
                    borderTop: '1px solid #e2e8f0',
                    cursor: 'pointer',
                    fontSize: '14px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 20 20" fill="#3b82f6">
                    <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm2 10a1 1 0 10-2 0v3a1 1 0 102 0v-3zm2 0a1 1 0 10-2 0v3a1 1 0 102 0v-3zm6-1a1 1 0 00-1-1h-3a1 1 0 100 2h3a1 1 0 001-1z" clipRule="evenodd"/>
                  </svg>
                  导出为 CSV (.csv)
                </button>
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="control-tips">
        <h3 className="tips-title">操作提示</h3>
        <ul className="tips-list">
          <li>开始爬取前请确保已填写搜索关键词</li>
          <li>爬取过程中可随时暂停和继续</li>
          <li>数据会实时显示在右侧预览区</li>
          <li>完成后可导出为Excel格式</li>
        </ul>
      </div>
    </div>
  )
}

export default CrawlControl