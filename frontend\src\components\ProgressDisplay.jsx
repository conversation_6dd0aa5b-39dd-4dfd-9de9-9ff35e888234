import React from 'react'
import './ProgressDisplay.css'

function ProgressDisplay({ status, progress }) {
  const progressPercentage = progress.targetItems > 0 
    ? Math.round((progress.itemsCollected / progress.targetItems) * 100)
    : 0

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return (
          <div className="status-icon running">
            <svg className="spin" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2a10 10 0 00-9.95 9h2.02a8 8 0 117.93 9 8 8 0 01-7.93-7H2.05A10 10 0 1012 2z" fill="currentColor"/>
            </svg>
          </div>
        )
      case 'paused':
        return (
          <div className="status-icon paused">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
            </svg>
          </div>
        )
      case 'completed':
        return (
          <div className="status-icon completed">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
            </svg>
          </div>
        )
      default:
        return (
          <div className="status-icon idle">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <circle cx="12" cy="12" r="10"/>
            </svg>
          </div>
        )
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'running': return '正在爬取'
      case 'paused': return '已暂停'
      case 'completed': return '爬取完成'
      default: return '等待开始'
    }
  }

  return (
    <div className="progress-display card">
      <div className="progress-header">
        <h2 className="progress-title">爬取进度</h2>
        <div className="progress-status">
          {getStatusIcon()}
          <span className="status-text">{getStatusText()}</span>
        </div>
      </div>
      
      <div className="progress-content">
        {/* 进度条 */}
        <div className="progress-bar-container">
          <div className="progress-bar">
            <div 
              className={`progress-fill ${status}`}
              style={{ width: `${progressPercentage}%` }}
            >
              {progressPercentage > 10 && (
                <span className="progress-percentage">{progressPercentage}%</span>
              )}
            </div>
          </div>
          {progressPercentage <= 10 && progressPercentage > 0 && (
            <span className="progress-percentage-outside">{progressPercentage}%</span>
          )}
        </div>
        
        {/* 统计信息 */}
        <div className="progress-stats">
          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                <path fillRule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H4v10h12V5h-2a1 1 0 100-2 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">已采集商品</span>
              <span className="stat-value">{progress.itemsCollected}</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">当前页数</span>
              <span className="stat-value">{progress.currentPage} / {progress.totalPages}</span>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="stat-info">
              <span className="stat-label">预计剩余</span>
              <span className="stat-value">
                {status === 'running' 
                  ? `~${Math.ceil((progress.totalPages - progress.currentPage) * 3)} 分钟`
                  : '--'
                }
              </span>
            </div>
          </div>
        </div>
        
        {/* 实时动态效果 */}
        {status === 'running' && (
          <div className="live-indicator">
            <span className="live-dot"></span>
            <span className="live-text">实时爬取中...</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProgressDisplay