# === 拼多多爬虫系统依赖配置 ===
# 更新时间: 2025-07-28
# 支持环境: Windows + WSL双环境
# Python版本: >=3.8

# === 核心浏览器自动化 ===
playwright>=1.54.0          # 现代化浏览器自动化框架，支持最新Chrome浏览器
playwright-stealth>=2.0.0   # Playwright反检测插件，绕过网站反爬虫检测

# === 数据处理和导出 ===
orjson>=3.11.0              # 高性能JSON解析库，比标准json库快2倍
openpyxl>=3.1.5             # Excel文件读写库，用于导出商品数据
python-dateutil>=2.9.0      # 日期时间处理增强库
pandas>=2.0.0               # 数据分析和处理库，用于数据统计和分析

# === 高性能文本匹配 ===
rapidfuzz>=3.10.0           # 高性能模糊字符串匹配库，用于品牌识别优化

# === 配置和日志管理 ===
pyyaml>=6.0.2               # YAML配置文件解析库
loguru>=0.7.3               # 现代化日志库，支持彩色输出和结构化日志

# === 重试和错误处理 ===
tenacity>=9.1.0             # 智能重试库，支持指数退避和条件重试

# === 系统依赖修复 ===
greenlet>=3.2.2             # 修复gevent依赖冲突，确保异步处理稳定性

# === 开发和调试工具 (可选) ===
# pytest>=8.0.0             # 单元测试框架
# pytest-asyncio>=0.23.0    # 异步测试支持
