import React from 'react'
import './Skeleton.css'

const Skeleton = ({ 
  variant = 'text', 
  width, 
  height, 
  animation = true,
  rounded = false,
  className = '' 
}) => {
  const styles = {
    width: width || (variant === 'text' ? '100%' : undefined),
    height: height || (variant === 'text' ? '1em' : undefined)
  }

  const classes = [
    'skeleton',
    `skeleton-${variant}`,
    animation && 'skeleton-animated',
    rounded && 'skeleton-rounded',
    className
  ].filter(Boolean).join(' ')

  return <div className={classes} style={styles} />
}

// 骨架屏组合组件
export const SkeletonCard = ({ lines = 3 }) => {
  return (
    <div className="skeleton-card">
      <Skeleton variant="rect" height={200} />
      <div className="skeleton-card-content">
        <Skeleton variant="text" width="60%" height={24} />
        {Array.from({ length: lines }).map((_, index) => (
          <Skeleton 
            key={index} 
            variant="text" 
            width={index === lines - 1 ? "80%" : "100%"} 
          />
        ))}
      </div>
    </div>
  )
}

export const SkeletonTable = ({ rows = 5, columns = 4 }) => {
  return (
    <div className="skeleton-table">
      <div className="skeleton-table-header">
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={index} variant="text" height={40} />
        ))}
      </div>
      <div className="skeleton-table-body">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="skeleton-table-row">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} variant="text" height={32} />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

export default Skeleton