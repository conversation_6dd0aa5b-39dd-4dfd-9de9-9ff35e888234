import React from 'react'
import './ConfirmDialog.css'

const ConfirmDialog = ({
  isOpen,
  title,
  message,
  confirmText = '确认',
  cancelText = '取消',
  type = 'warning',
  onConfirm,
  onCancel
}) => {
  if (!isOpen) return null

  const icons = {
    warning: (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="currentColor">
        <path fillRule="evenodd" d="M24 4C12.96 4 4 12.96 4 24s8.96 20 20 20 20-8.96 20-20S35.04 4 24 4zm-2 10h4v12h-4V14zm0 16h4v4h-4v-4z" clipRule="evenodd"/>
      </svg>
    ),
    danger: (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="currentColor">
        <path fillRule="evenodd" d="M24 4C12.96 4 4 12.96 4 24s8.96 20 20 20 20-8.96 20-20S35.04 4 24 4zm-2 10h4v12h-4V14zm0 16h4v4h-4v-4z" clipRule="evenodd"/>
      </svg>
    ),
    info: (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="currentColor">
        <path fillRule="evenodd" d="M24 4C12.96 4 4 12.96 4 24s8.96 20 20 20 20-8.96 20-20S35.04 4 24 4zm-2 14h4v12h-4V18zm0-6h4v4h-4v-4z" clipRule="evenodd"/>
      </svg>
    )
  }

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="confirm-dialog" onClick={(e) => e.stopPropagation()}>
        <div className={`confirm-dialog-icon confirm-dialog-icon-${type}`}>
          {icons[type]}
        </div>
        
        <div className="confirm-dialog-content">
          <h3 className="confirm-dialog-title">{title}</h3>
          <p className="confirm-dialog-message">{message}</p>
        </div>
        
        <div className="confirm-dialog-actions">
          <button 
            className="btn btn-secondary"
            onClick={onCancel}
          >
            {cancelText}
          </button>
          <button 
            className={`btn btn-primary btn-${type}`}
            onClick={onConfirm}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ConfirmDialog