<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .status { 
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <h1>拼多多爬虫前端测试页面</h1>
    
    <div class="status info">
        <h3>测试步骤：</h3>
        <ol>
            <li>点击"测试后端连接"检查API是否正常</li>
            <li>如果后端正常，说明可以继续测试前端</li>
            <li>如果后端连接失败，需要先启动后端服务</li>
        </ol>
    </div>

    <div>
        <button onclick="testBackend()">测试后端连接</button>
        <button onclick="testFrontend()">测试前端路径</button>
        <button onclick="checkDependencies()">检查依赖</button>
    </div>

    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(div);
        }

        async function testBackend() {
            addResult('正在测试后端连接...');
            
            try {
                const response = await fetch('http://localhost:8000/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ 后端API连接成功！', 'success');
                    addResult(`API状态: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult('❌ 后端API响应异常', 'error');
                }
            } catch (error) {
                addResult('❌ 无法连接到后端API (http://localhost:8000)', 'error');
                addResult('请确保后端服务已启动', 'error');
                addResult(`错误详情: ${error.message}`, 'error');
            }
        }

        async function testFrontend() {
            addResult('检查前端文件...');
            
            const files = [
                '/src/main.jsx',
                '/src/App.jsx',
                '/src/App.css',
                '/index.html'
            ];

            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        addResult(`✅ ${file} 存在`, 'success');
                    } else {
                        addResult(`❌ ${file} 不存在或无法访问`, 'error');
                    }
                } catch (error) {
                    addResult(`⚠️ 无法检查 ${file}`, 'error');
                }
            }
        }

        function checkDependencies() {
            addResult('依赖检查说明：', 'info');
            addResult('1. 打开命令行，进入frontend目录', 'info');
            addResult('2. 运行: npm install', 'info');
            addResult('3. 运行: npm list', 'info');
            addResult('4. 确保安装了: react, react-dom, vite', 'info');
            
            // 显示当前页面信息
            addResult(`当前页面URL: ${window.location.href}`, 'info');
            addResult(`如果您能看到这个页面，说明静态服务器正常工作`, 'success');
        }

        // 页面加载时自动测试
        window.onload = () => {
            addResult('测试页面已加载', 'success');
            addResult('您可以使用这个页面来诊断问题', 'info');
        };
    </script>
</body>
</html>