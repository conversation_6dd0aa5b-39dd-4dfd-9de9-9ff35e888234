// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      renders: new Map(),
      apiCalls: new Map(),
      interactions: []
    }
    this.startTime = performance.now()
  }

  // 记录组件渲染时间
  measureRender(componentName, callback) {
    const start = performance.now()
    const result = callback()
    const duration = performance.now() - start
    
    if (!this.metrics.renders.has(componentName)) {
      this.metrics.renders.set(componentName, [])
    }
    
    this.metrics.renders.get(componentName).push({
      duration,
      timestamp: Date.now()
    })
    
    // 如果渲染时间超过16ms（60fps），记录警告
    if (duration > 16) {
      console.warn(`[Performance] ${componentName} 渲染耗时 ${duration.toFixed(2)}ms`)
    }
    
    return result
  }

  // 记录API调用时间
  async measureAPI(apiName, apiCall) {
    const start = performance.now()
    try {
      const result = await apiCall()
      const duration = performance.now() - start
      
      if (!this.metrics.apiCalls.has(apiName)) {
        this.metrics.apiCalls.set(apiName, [])
      }
      
      this.metrics.apiCalls.get(apiName).push({
        duration,
        timestamp: Date.now(),
        status: 'success'
      })
      
      // 如果API调用超过1秒，记录警告
      if (duration > 1000) {
        console.warn(`[Performance] API ${apiName} 耗时 ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const duration = performance.now() - start
      
      if (!this.metrics.apiCalls.has(apiName)) {
        this.metrics.apiCalls.set(apiName, [])
      }
      
      this.metrics.apiCalls.get(apiName).push({
        duration,
        timestamp: Date.now(),
        status: 'error'
      })
      
      throw error
    }
  }

  // 记录用户交互
  recordInteraction(type, target) {
    this.metrics.interactions.push({
      type,
      target,
      timestamp: Date.now()
    })
  }

  // 获取性能报告
  getReport() {
    const report = {
      totalTime: performance.now() - this.startTime,
      renders: {},
      apiCalls: {},
      interactions: this.metrics.interactions,
      vitals: this.getWebVitals()
    }

    // 计算渲染统计
    this.metrics.renders.forEach((renders, component) => {
      const durations = renders.map(r => r.duration)
      report.renders[component] = {
        count: renders.length,
        average: durations.reduce((a, b) => a + b, 0) / durations.length,
        max: Math.max(...durations),
        min: Math.min(...durations)
      }
    })

    // 计算API调用统计
    this.metrics.apiCalls.forEach((calls, api) => {
      const durations = calls.map(c => c.duration)
      const errors = calls.filter(c => c.status === 'error').length
      report.apiCalls[api] = {
        count: calls.length,
        errors,
        average: durations.reduce((a, b) => a + b, 0) / durations.length,
        max: Math.max(...durations),
        min: Math.min(...durations)
      }
    })

    return report
  }

  // 获取 Web Vitals
  getWebVitals() {
    const vitals = {}
    
    // FCP (First Contentful Paint)
    const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0]
    if (fcpEntry) {
      vitals.FCP = fcpEntry.startTime
    }
    
    // LCP (Largest Contentful Paint)
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint')
    if (lcpEntries.length > 0) {
      vitals.LCP = lcpEntries[lcpEntries.length - 1].startTime
    }
    
    // CLS (Cumulative Layout Shift)
    let clsScore = 0
    const clsEntries = performance.getEntriesByType('layout-shift')
    clsEntries.forEach(entry => {
      if (!entry.hadRecentInput) {
        clsScore += entry.value
      }
    })
    vitals.CLS = clsScore
    
    return vitals
  }

  // 清除指标
  clear() {
    this.metrics.renders.clear()
    this.metrics.apiCalls.clear()
    this.metrics.interactions = []
    this.startTime = performance.now()
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()

// React Hook for performance monitoring
export const usePerformanceMonitor = (componentName) => {
  return {
    measureRender: (callback) => performanceMonitor.measureRender(componentName, callback),
    recordInteraction: (type, target) => performanceMonitor.recordInteraction(type, target)
  }
}

// 导出性能报告
export const exportPerformanceReport = () => {
  const report = performanceMonitor.getReport()
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${new Date().toISOString()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}