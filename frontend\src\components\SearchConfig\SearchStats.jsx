import React from 'react'
import './SearchStats.css'

export default function SearchStats({ maxPages, targetCount, keywordCount }) {
  const stats = [
    {
      id: 'time',
      label: '预计耗时',
      value: `${Math.ceil(maxPages * keywordCount * 3)} 分钟`,
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <circle cx="10" cy="10" r="9" stroke="currentColor" strokeWidth="1.5"/>
          <path d="M10 6V10L13 13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
        </svg>
      ),
      color: 'blue'
    },
    {
      id: 'items',
      label: '预计商品数',
      value: `${maxPages * 30 * keywordCount} 个`,
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <rect x="3" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="11" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="3" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
          <rect x="11" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
        </svg>
      ),
      color: 'green'
    },
    {
      id: 'keywords',
      label: '关键词数',
      value: keywordCount > 0 ? `${keywordCount} 个` : '未设置',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M3 7H17M3 13H17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          <circle cx="7" cy="7" r="2" fill="currentColor"/>
          <circle cx="13" cy="13" r="2" fill="currentColor"/>
        </svg>
      ),
      color: 'purple'
    },
    {
      id: 'efficiency',
      label: '采集效率',
      value: keywordCount > 0 ? `${Math.round(targetCount / (maxPages * keywordCount * 3))} 个/分钟` : '-',
      icon: (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M10 2L12.09 7.26L18 8.27L14 12.14L14.81 18L10 15.77L5.19 18L6 12.14L2 8.27L7.91 7.26L10 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinejoin="round"/>
        </svg>
      ),
      color: 'orange'
    }
  ]

  return (
    <div className="search-stats">
      <div className="stats-grid">
        {stats.map(stat => (
          <div key={stat.id} className="stat-card">
            <div className={`stat-icon ${stat.color}`}>
              {stat.icon}
            </div>
            <div className="stat-content">
              <span className="stat-label">{stat.label}</span>
              <span className="stat-value">{stat.value}</span>
            </div>
          </div>
        ))}
      </div>
      
      {keywordCount === 0 && (
        <div className="stats-warning">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 1.5A6.5 6.5 0 1014.5 8 6.5 6.5 0 008 1.5zm0 10a.75.75 0 110-1.5.75.75 0 010 1.5zm.75-3.5a.75.75 0 01-1.5 0V5a.75.75 0 011.5 0v3z"/>
          </svg>
          <span>请先输入搜索关键词</span>
        </div>
      )}
    </div>
  )
}