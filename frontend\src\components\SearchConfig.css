.search-config {
  animation: fadeIn 0.3s ease;
}

.config-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-title svg {
  color: var(--primary);
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.input-wrapper {
  position: relative;
}

.input-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  font-size: 16px;
  line-height: 1;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.input-clear:hover {
  background: var(--secondary);
  color: var(--text);
}

.input-clear:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.required {
  color: var(--error);
}

.help-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.config-stats {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: var(--secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}