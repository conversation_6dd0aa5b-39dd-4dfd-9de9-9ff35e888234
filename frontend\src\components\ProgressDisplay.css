.progress-display {
  animation: fadeIn 0.3s ease;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.progress-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.progress-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-icon.idle {
  background: var(--secondary);
  color: var(--text-secondary);
}

.status-icon.running {
  background: rgba(33, 150, 243, 0.1);
  color: var(--info);
}

.status-icon.paused {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning);
}

.status-icon.completed {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.progress-bar-container {
  position: relative;
}

.progress-bar {
  height: 24px;
  background: var(--secondary);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: 12px;
  transition: width 0.5s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 12px;
}

.progress-fill.running {
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

.progress-fill.completed {
  background: linear-gradient(135deg, var(--success) 0%, #388e3c 100%);
}

.progress-fill.paused {
  background: linear-gradient(135deg, var(--warning) 0%, #f57c00 100%);
}

.progress-percentage {
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.progress-percentage-outside {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  font-weight: 600;
  color: var(--text);
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--surface);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(33, 150, 243, 0.05);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: var(--radius-sm);
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--info);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.live-text {
  font-size: 14px;
  color: var(--info);
  font-weight: 500;
}

@media (max-width: 768px) {
  .progress-stats {
    grid-template-columns: 1fr;
  }
}