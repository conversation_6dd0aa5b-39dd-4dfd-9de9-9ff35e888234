# 拼多多爬虫前端控制面板

这是一个美观、现代化的Web界面，用于控制和监控拼多多商品数据爬虫。

## 功能特性

- 🔍 **搜索配置**：灵活设置搜索关键词、爬取页数和目标数量
- 📊 **排序选择**：支持综合排序、销量排序、价格排序等多种方式
- ⚡ **实时监控**：实时显示爬取进度、已采集商品数量等状态
- 📋 **数据预览**：实时展示爬取到的商品信息，支持详情查看
- 🎨 **主题切换**：支持深色/浅色主题切换
- 💾 **数据导出**：一键导出为Excel格式

## 技术栈

- React 18
- Vite
- CSS3 (自定义样式，无需UI框架)
- 响应式设计

## 安装运行

1. 进入前端目录：
```bash
cd frontend
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm run dev
```

4. 在浏览器中访问 `http://localhost:3000`

## 构建部署

构建生产版本：
```bash
npm run build
```

预览构建结果：
```bash
npm run preview
```

## 项目结构

```
frontend/
├── src/
│   ├── components/        # React组件
│   │   ├── Header.jsx    # 头部组件
│   │   ├── SearchConfig.jsx  # 搜索配置
│   │   ├── SortSelector.jsx  # 排序选择
│   │   ├── CrawlControl.jsx  # 爬取控制
│   │   ├── ProgressDisplay.jsx # 进度显示
│   │   └── DataPreview.jsx    # 数据预览
│   ├── App.jsx           # 主应用组件
│   ├── main.jsx          # 入口文件
│   └── *.css             # 样式文件
├── index.html            # HTML模板
├── package.json          # 项目配置
└── vite.config.js        # Vite配置
```

## 后端集成

本前端项目设计为与拼多多爬虫后端API配合使用。需要实现以下API接口：

- `POST /api/crawl/start` - 开始爬取
- `POST /api/crawl/pause` - 暂停爬取
- `POST /api/crawl/resume` - 继续爬取
- `POST /api/crawl/stop` - 停止爬取
- `GET /api/crawl/status` - 获取爬取状态
- `GET /api/crawl/export` - 导出数据
- `WebSocket /ws` - 实时数据推送

## 注意事项

- 确保后端服务已启动并正确配置CORS
- WebSocket连接用于实时更新爬取进度和数据
- 建议使用Chrome或Firefox等现代浏览器访问