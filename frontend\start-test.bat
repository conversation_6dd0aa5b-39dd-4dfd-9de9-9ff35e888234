@echo off
echo ========================================
echo 拼多多爬虫前端测试启动脚本
echo ========================================
echo.

REM 检查 Node.js 是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未检测到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo [信息] Node.js 版本:
node --version
echo.

REM 检查是否在frontend目录
if not exist "package.json" (
    echo [错误] 请在frontend目录下运行此脚本
    pause
    exit /b 1
)

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo [信息] 正在安装依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
)

echo [信息] 正在启动前端开发服务器...
echo.
echo ========================================
echo 访问地址: http://localhost:5173
echo 快捷键帮助: 按 ? 键
echo 停止服务: 按 Ctrl+C
echo ========================================
echo.

REM 启动开发服务器
npm run dev