/* 遮罩层 */
.product-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

/* 模态框 */
.product-detail-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 16px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  z-index: 1001;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* 关闭按钮 */
.modal-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  z-index: 10;
}

.modal-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: rotate(90deg);
}

/* 详情容器 */
.detail-container {
  display: flex;
  height: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

/* 左侧图片区域 */
.detail-images {
  width: 450px;
  flex-shrink: 0;
  padding: 24px;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main-image {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #8c8c8c;
}

.discount-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #ff4d4f;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 6px;
}

/* 缩略图列表 */
.thumbnail-list {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
}

.thumbnail-item {
  width: 64px;
  height: 64px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.thumbnail-item:hover {
  border-color: #d9d9d9;
}

.thumbnail-item.active {
  border-color: #1890ff;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 右侧信息区域 */
.detail-info {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

/* 标题区域 */
.detail-header {
  margin-bottom: 24px;
}

.detail-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.detail-brand {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: #f0f0f0;
  border-radius: 6px;
}

.brand-label {
  font-size: 12px;
  color: #8c8c8c;
}

.brand-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

/* 价格区域 */
.detail-price-section {
  background: #fff5f5;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.price-row {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 8px;
}

.price-label {
  font-size: 14px;
  color: #595959;
}

.current-price {
  font-size: 28px;
  font-weight: 600;
  color: #ff4d4f;
}

.original-price {
  font-size: 16px;
  color: #8c8c8c;
  text-decoration: line-through;
}

.coupon-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 8px;
  border-top: 1px solid #ffe0e0;
}

.coupon-label {
  font-size: 14px;
  color: #595959;
}

.coupon-price {
  font-size: 20px;
  font-weight: 600;
  color: #ff4d4f;
}

.coupon-amount {
  font-size: 14px;
  color: #ff4d4f;
  background: #fff;
  padding: 2px 8px;
  border-radius: 4px;
}

/* 统计数据 */
.detail-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 信息列表 */
.detail-info-list {
  margin-bottom: 24px;
}

.info-row {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 100px;
  font-size: 14px;
  color: #8c8c8c;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #262626;
}

/* 标签区域 */
.detail-tags-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0 0 12px 0;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-badge {
  display: inline-block;
  padding: 4px 12px;
  background: #e6f4ff;
  color: #1890ff;
  font-size: 12px;
  border-radius: 6px;
}

/* 服务保障 */
.detail-services {
  margin-bottom: 32px;
}

.service-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #595959;
}

.service-item svg {
  color: #52c41a;
  flex-shrink: 0;
}

/* 操作按钮 */
.detail-actions {
  display: flex;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.action-btn.primary {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.action-btn.primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式 */
@media (max-width: 768px) {
  .product-detail-modal {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    border-radius: 0;
  }
  
  .detail-container {
    flex-direction: column;
  }
  
  .detail-images {
    width: 100%;
    padding: 16px;
  }
  
  .detail-info {
    padding: 16px;
  }
}